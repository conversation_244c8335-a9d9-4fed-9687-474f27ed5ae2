package com.ruoyi.web.controller.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;

/**
 * 通用请求处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common")
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";

    /**
     * 通用下载请求
     * 
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求（单个）
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 富文本编辑器图片上传（无需认证）
     */
    @Anonymous
    @PostMapping("/uploadImage")
    public AjaxResult uploadImage(@RequestParam(value = "upload", required = false) MultipartFile upload,
                                  @RequestParam(value = "file", required = false) MultipartFile file,
                                  HttpServletRequest request) throws Exception
    {
        try
        {
            log.info("接收到图片上传请求，upload参数: {}, file参数: {}",
                    upload != null ? upload.getOriginalFilename() : "null",
                    file != null ? file.getOriginalFilename() : "null");

            // 打印所有请求参数
            log.info("所有请求参数: {}", request.getParameterMap().keySet());

            // 尝试从request中获取所有可能的文件参数
            if (request instanceof org.springframework.web.multipart.MultipartHttpServletRequest) {
                org.springframework.web.multipart.MultipartHttpServletRequest multipartRequest =
                    (org.springframework.web.multipart.MultipartHttpServletRequest) request;
                log.info("所有文件参数名: {}", multipartRequest.getFileMap().keySet());
            }

            // 确定使用哪个文件参数
            MultipartFile uploadFile = getUploadFile(request, upload, file);

            // 验证文件类型
            if (uploadFile == null || uploadFile.isEmpty()) {
                log.warn("没有接收到有效的文件参数");
                return createCKEditorErrorResponse("请选择要上传的图片");
            }

            log.info("使用文件: {}, 大小: {} bytes", uploadFile.getOriginalFilename(), uploadFile.getSize());

            String fileName = uploadFile.getOriginalFilename();
            if (fileName == null || !isImageFile(fileName)) {
                return createCKEditorErrorResponse("只支持上传图片文件（jpg、jpeg、png、gif、bmp）");
            }

            // 验证文件大小（5MB）
            if (uploadFile.getSize() > 5 * 1024 * 1024) {
                return createCKEditorErrorResponse("图片大小不能超过5MB");
            }

            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String uploadFileName = FileUploadUtils.upload(filePath, uploadFile);

            // 直接返回相对路径 - 参考您提供的代码风格
            String relativeUrl = uploadFileName;

            // 返回CKEditor期望的格式
            AjaxResult ajax = AjaxResult.success();
            ajax.put("uploaded", 1);  // CKEditor期望的成功标识
            ajax.put("fileName", FileUtils.getName(uploadFileName));  // 文件名
            ajax.put("url", relativeUrl);  // 直接返回相对路径

            log.info("图片上传成功，相对路径: {}", relativeUrl);
            return ajax;
        }
        catch (Exception e)
        {
            log.error("图片上传失败", e);
            return createCKEditorErrorResponse("图片上传失败：" + e.getMessage());
        }
    }

    /**
     * 创建CKEditor期望的错误响应格式
     */
    private AjaxResult createCKEditorErrorResponse(String message) {
        AjaxResult ajax = AjaxResult.error(message);
        ajax.put("uploaded", 0);  // CKEditor期望的失败标识
        ajax.put("error", message);  // CKEditor期望的错误信息字段
        return ajax;
    }

    /**
     * 获取上传的文件，尝试多种可能的参数名
     */
    private MultipartFile getUploadFile(HttpServletRequest request, MultipartFile upload, MultipartFile file) {
        // 优先使用明确传入的参数
        if (upload != null && !upload.isEmpty()) {
            return upload;
        }
        if (file != null && !file.isEmpty()) {
            return file;
        }

        // 如果是MultipartRequest，尝试获取所有文件参数
        if (request instanceof org.springframework.web.multipart.MultipartHttpServletRequest) {
            org.springframework.web.multipart.MultipartHttpServletRequest multipartRequest =
                (org.springframework.web.multipart.MultipartHttpServletRequest) request;

            // 尝试常见的参数名
            String[] possibleNames = {"upload", "file", "image", "picture", "photo"};
            for (String name : possibleNames) {
                MultipartFile multipartFile = multipartRequest.getFile(name);
                if (multipartFile != null && !multipartFile.isEmpty()) {
                    log.info("找到文件参数: {}", name);
                    return multipartFile;
                }
            }

            // 如果还没找到，获取第一个非空文件
            for (MultipartFile multipartFile : multipartRequest.getFileMap().values()) {
                if (multipartFile != null && !multipartFile.isEmpty()) {
                    log.info("使用第一个非空文件参数");
                    return multipartFile;
                }
            }
        }

        return null;
    }

    /**
     * 验证是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return "jpg".equals(extension) || "jpeg".equals(extension) ||
               "png".equals(extension) || "gif".equals(extension) ||
               "bmp".equals(extension);
    }

    /**
     * 通用上传请求（多个）
     */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                // 上传并返回新文件名称
                String fileName = FileUploadUtils.upload(filePath, file);
                String url = serverConfig.getUrl() + fileName;
                urls.add(url);
                fileNames.add(fileName);
                newFileNames.add(FileUtils.getName(fileName));
                originalFilenames.add(file.getOriginalFilename());
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("fileNames", StringUtils.join(fileNames, FILE_DELIMETER));
            ajax.put("newFileNames", StringUtils.join(newFileNames, FILE_DELIMETER));
            ajax.put("originalFilenames", StringUtils.join(originalFilenames, FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + FileUtils.stripPrefix(resource);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}
