package com.ruoyi.biz.controller;

import java.util.List;
import java.io.IOException;
import java.io.InputStream;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.biz.domain.Member;
import com.ruoyi.biz.service.IMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 成员Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/biz/member")
public class MemberController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(MemberController.class);

    @Autowired
    private IMemberService memberService;

    /**
     * 查询成员列表
     */
    @PreAuthorize("@ss.hasPermi('biz:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(Member member)
    {
        startPage();
        List<Member> list = memberService.selectMemberList(member);
        return getDataTable(list);
    }

    /**
     * 导出成员列表
     */
    @PreAuthorize("@ss.hasPermi('biz:member:export')")
    @Log(title = "成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Member member)
    {
        List<Member> list = memberService.selectMemberList(member);
        ExcelUtil<Member> util = new ExcelUtil<Member>(Member.class);
        util.exportExcel(response, list, "成员数据");
    }

    /**
     * 导入成员数据
     */
    @Log(title = "成员管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('biz:member:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        String operName = getUsername();
        String message = memberService.importMemberFromExcel(file.getInputStream(), operName);
        return success(message);
    }

    /**
     * 下载成员导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        try
        {
            ClassPathResource resource = new ClassPathResource("static/成员导入Excel模板.xlsx");
            InputStream inputStream = resource.getInputStream();

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode("成员导入模板.xlsx", "UTF-8"));

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            inputStream.close();
            response.getOutputStream().flush();
        }
        catch (IOException e)
        {
            log.error("下载模板失败", e);
        }
    }

    /**
     * 获取成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('biz:member:query')")
    @GetMapping(value = "/{memberId}")
    public AjaxResult getInfo(@PathVariable("memberId") Long memberId)
    {
        return success(memberService.selectMemberByMemberId(memberId));
    }

    /**
     * 新增成员
     */
    @PreAuthorize("@ss.hasPermi('biz:member:add')")
    @Log(title = "成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Member member)
    {
        return toAjax(memberService.insertMember(member));
    }

    /**
     * 修改成员
     */
    @PreAuthorize("@ss.hasPermi('biz:member:edit')")
    @Log(title = "成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Member member)
    {
        return toAjax(memberService.updateMember(member));
    }

    /**
     * 重置成员密码
     */
    @PreAuthorize("@ss.hasPermi('biz:member:resetPwd')")
    @Log(title = "成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd/{memberId}")
    public AjaxResult resetPwd(@PathVariable Long memberId)
    {
        return toAjax(memberService.resetMemberPwd(memberId));
    }

    /**
     * 删除成员
     */
    @PreAuthorize("@ss.hasPermi('biz:member:remove')")
    @Log(title = "成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{memberIds}")
    public AjaxResult remove(@PathVariable Long[] memberIds)
    {
        return toAjax(memberService.deleteMemberByMemberIds(memberIds));
    }
}
