package com.ruoyi.biz.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.biz.domain.Question;
import com.ruoyi.biz.dto.QuestionDTO;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 题目数据转换器
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Component
public class QuestionConverter {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * DTO转换为实体
     */
    public Question dtoToEntity(QuestionDTO dto) {
        if (dto == null) {
            return null;
        }
        
        Question question = new Question();
        question.setQuestionId(dto.getQuestionId());
        question.setBankId(dto.getBankId());
        question.setQuestionType(convertQuestionTypeToInt(dto.getQuestionType()));
        question.setDifficulty(convertDifficultyToInt(dto.getDifficulty()));
        question.setQuestionContent(dto.getQuestionContent());
        question.setAnalysis(dto.getExplanation());
        
        // 转换选项数据
        if (!CollectionUtils.isEmpty(dto.getOptions())) {
            question.setOptions(convertOptionsToJson(dto.getOptions()));
        }

        // 设置创建人（如果是新增）
        if (question.getQuestionId() == null) {
            try {
                question.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                // 如果获取当前用户失败，设置默认值
                question.setCreateBy("admin");
            }
        }

        return question;
    }
    
    /**
     * 实体转换为DTO
     */
    public QuestionDTO entityToDto(Question question) {
        if (question == null) {
            return null;
        }
        
        QuestionDTO dto = new QuestionDTO();
        dto.setQuestionId(question.getQuestionId());
        dto.setBankId(question.getBankId());
        dto.setQuestionType(convertQuestionTypeToString(question.getQuestionType()));
        dto.setDifficulty(convertDifficultyToString(question.getDifficulty()));
        dto.setQuestionContent(question.getQuestionContent());
        dto.setExplanation(question.getAnalysis());
        
        // 转换选项数据
        if (StringUtils.hasText(question.getOptions())) {
            dto.setOptions(convertJsonToOptions(question.getOptions()));
            dto.setCorrectAnswer(extractCorrectAnswer(question.getOptions()));
        }
        
        return dto;
    }
    
    /**
     * 题型字符串转数字
     */
    private Integer convertQuestionTypeToInt(String questionType) {
        if (!StringUtils.hasText(questionType)) {
            return null;
        }
        
        switch (questionType) {
            case "single":
                return 1;
            case "multiple":
                return 2;
            case "judgment":
                return 3;
            default:
                throw new ServiceException("不支持的题型: " + questionType);
        }
    }
    
    /**
     * 题型数字转字符串
     */
    private String convertQuestionTypeToString(Integer questionType) {
        if (questionType == null) {
            return null;
        }
        
        switch (questionType) {
            case 1:
                return "single";
            case 2:
                return "multiple";
            case 3:
                return "judgment";
            default:
                return "single";
        }
    }
    
    /**
     * 难度字符串转数字
     */
    private Integer convertDifficultyToInt(String difficulty) {
        if (!StringUtils.hasText(difficulty)) {
            return 2; // 如果难度为空，默认设置为中等（2）
        }

        switch (difficulty) {
            case "简单":
                return 1;
            case "中等":
                return 2;
            case "困难":
                return 3;
            default:
                throw new ServiceException("不支持的难度: " + difficulty);
        }
    }
    
    /**
     * 难度数字转字符串
     */
    private String convertDifficultyToString(Integer difficulty) {
        if (difficulty == null) {
            return null;
        }
        
        switch (difficulty) {
            case 1:
                return "简单";
            case 2:
                return "中等";
            case 3:
                return "困难";
            default:
                return "中等";
        }
    }
    
    /**
     * 选项列表转JSON字符串
     */
    private String convertOptionsToJson(List<QuestionDTO.OptionDTO> options) {
        try {
            List<Map<String, Object>> optionMaps = options.stream()
                .map(option -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("key", option.getKey());
                    map.put("content", option.getContent());
                    map.put("isCorrect", option.getIsCorrect());
                    return map;
                })
                .collect(Collectors.toList());
            
            return objectMapper.writeValueAsString(optionMaps);
        } catch (JsonProcessingException e) {
            throw new ServiceException("选项数据转换失败: " + e.getMessage());
        }
    }
    
    /**
     * JSON字符串转选项列表
     */
    private List<QuestionDTO.OptionDTO> convertJsonToOptions(String optionsJson) {
        try {
            List<Map<String, Object>> optionMaps = objectMapper.readValue(
                optionsJson, 
                new TypeReference<List<Map<String, Object>>>() {}
            );
            
            return optionMaps.stream()
                .map(map -> new QuestionDTO.OptionDTO(
                    (String) map.get("key"),
                    (String) map.get("content"),
                    (Boolean) map.get("isCorrect")
                ))
                .collect(Collectors.toList());
        } catch (JsonProcessingException e) {
            throw new ServiceException("选项数据解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 从选项JSON中提取正确答案
     */
    private String extractCorrectAnswer(String optionsJson) {
        try {
            List<Map<String, Object>> optionMaps = objectMapper.readValue(
                optionsJson, 
                new TypeReference<List<Map<String, Object>>>() {}
            );
            
            List<String> correctAnswers = optionMaps.stream()
                .filter(map -> Boolean.TRUE.equals(map.get("isCorrect")))
                .map(map -> (String) map.get("key"))
                .collect(Collectors.toList());
            
            return String.join(",", correctAnswers);
        } catch (JsonProcessingException e) {
            return "";
        }
    }
    
    /**
     * 验证题目数据
     */
    public void validateQuestionData(QuestionDTO dto) {
        // 验证选择题必须有选项
        if (("single".equals(dto.getQuestionType()) || "multiple".equals(dto.getQuestionType()))) {
            if (CollectionUtils.isEmpty(dto.getOptions())) {
                throw new ServiceException("选择题必须设置选项");
            }
            
            // 验证必须有正确答案
            boolean hasCorrectAnswer = dto.getOptions().stream()
                .anyMatch(option -> Boolean.TRUE.equals(option.getIsCorrect()));
            
            if (!hasCorrectAnswer) {
                throw new ServiceException("选择题必须设置正确答案");
            }
            
            // 验证单选题只能有一个正确答案
            if ("single".equals(dto.getQuestionType())) {
                long correctCount = dto.getOptions().stream()
                    .mapToLong(option -> Boolean.TRUE.equals(option.getIsCorrect()) ? 1 : 0)
                    .sum();
                
                if (correctCount != 1) {
                    throw new ServiceException("单选题只能有一个正确答案");
                }
            }
        }
        
        // 判断题验证
        if ("judgment".equals(dto.getQuestionType())) {
            // 判断题可以通过options或correctAnswer设置答案
            boolean hasValidAnswer = false;

            if (!CollectionUtils.isEmpty(dto.getOptions())) {
                // 通过options设置答案
                hasValidAnswer = dto.getOptions().stream()
                    .anyMatch(option -> Boolean.TRUE.equals(option.getIsCorrect()));
            } else if (StringUtils.hasText(dto.getCorrectAnswer())) {
                // 通过correctAnswer设置答案
                if ("true".equals(dto.getCorrectAnswer()) || "false".equals(dto.getCorrectAnswer())) {
                    hasValidAnswer = true;
                }
            }

            if (!hasValidAnswer) {
                throw new ServiceException("判断题必须设置正确答案（true或false）");
            }
        }
    }
}
