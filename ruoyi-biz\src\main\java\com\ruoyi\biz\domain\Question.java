package com.ruoyi.biz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 题目对象 tbl_question
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class Question extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题目ID */
    private Long questionId;

    /** 题库ID */
    @Excel(name = "题库ID")
    private Long bankId;

    /** 分类ID */
    @Excel(name = "分类ID")
    private Long categoryId;

    /** 知识点ID，逗号分隔 */
    @Excel(name = "知识点ID，逗号分隔")
    private String pointIds;

    /** 题型：1单选，2多选，3判断 */
    @Excel(name = "题型：1单选，2多选，3判断")
    private Integer questionType;

    /** 难度：1简单，2中等，3困难 */
    @Excel(name = "难度：1简单，2中等，3困难")
    private Integer difficulty;

    /** 题干内容 */
    @Excel(name = "题干内容")
    private String questionContent;

    /** 解析 */
    @Excel(name = "解析")
    private String analysis;

    /** 选项，JSON格式 */
    @Excel(name = "选项，JSON格式")
    private String options;

    public void setQuestionId(Long questionId) 
    {
        this.questionId = questionId;
    }

    public Long getQuestionId() 
    {
        return questionId;
    }

    public void setBankId(Long bankId) 
    {
        this.bankId = bankId;
    }

    public Long getBankId() 
    {
        return bankId;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setPointIds(String pointIds) 
    {
        this.pointIds = pointIds;
    }

    public String getPointIds() 
    {
        return pointIds;
    }

    public void setQuestionType(Integer questionType) 
    {
        this.questionType = questionType;
    }

    public Integer getQuestionType() 
    {
        return questionType;
    }

    public void setDifficulty(Integer difficulty) 
    {
        this.difficulty = difficulty;
    }

    public Integer getDifficulty() 
    {
        return difficulty;
    }

    public void setQuestionContent(String questionContent) 
    {
        this.questionContent = questionContent;
    }

    public String getQuestionContent() 
    {
        return questionContent;
    }

    public void setAnalysis(String analysis) 
    {
        this.analysis = analysis;
    }

    public String getAnalysis() 
    {
        return analysis;
    }

    public void setOptions(String options)
    {
        this.options = options;
    }

    public String getOptions()
    {
        return options;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("questionId", getQuestionId())
            .append("bankId", getBankId())
            .append("categoryId", getCategoryId())
            .append("pointIds", getPointIds())
            .append("questionType", getQuestionType())
            .append("difficulty", getDifficulty())
            .append("questionContent", getQuestionContent())
            .append("analysis", getAnalysis())
            .append("options", getOptions())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
