package com.ruoyi.biz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 题库对象 tbl_question_bank
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public class QuestionBank extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 题库ID */
    private Long bankId;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer orderNum;

    /** 题库名称 */
    @Excel(name = "题库名称")
    private String bankName;

    /** 题库描述 */
    @Excel(name = "题库描述")
    private String bankDesc;

    /** 所属分类ID */
    @Excel(name = "所属分类ID")
    private Long categoryId;

    /** 封面图片路径 */
    @Excel(name = "封面图片路径")
    private String coverImg;

    /** 状态：0启用 1禁用 */
    @Excel(name = "状态：0启用 1禁用")
    private Integer status;

    public void setBankId(Long bankId) 
    {
        this.bankId = bankId;
    }

    public Long getBankId()
    {
        return bankId;
    }

    public void setOrderNum(Integer orderNum)
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum()
    {
        return orderNum;
    }

    public void setBankName(String bankName)
    {
        this.bankName = bankName;
    }

    public String getBankName() 
    {
        return bankName;
    }

    public void setBankDesc(String bankDesc) 
    {
        this.bankDesc = bankDesc;
    }

    public String getBankDesc() 
    {
        return bankDesc;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId() 
    {
        return categoryId;
    }

    public void setCoverImg(String coverImg) 
    {
        this.coverImg = coverImg;
    }

    public String getCoverImg() 
    {
        return coverImg;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("bankId", getBankId())
            .append("orderNum", getOrderNum())
            .append("bankName", getBankName())
            .append("bankDesc", getBankDesc())
            .append("categoryId", getCategoryId())
            .append("coverImg", getCoverImg())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
