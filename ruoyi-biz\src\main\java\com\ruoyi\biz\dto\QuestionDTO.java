package com.ruoyi.biz.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 题目数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class QuestionDTO {
    
    /** 题目ID */
    private Long questionId;
    
    /** 题库ID */
    @NotNull(message = "题库ID不能为空")
    private Long bankId;
    
    /** 题型：single=单选，multiple=多选，judgment=判断 */
    @NotBlank(message = "题型不能为空")
    @Pattern(regexp = "^(single|multiple|judgment)$", message = "题型只能是single、multiple或judgment")
    private String questionType;
    
    /** 难度：简单、中等、困难 */
    @NotBlank(message = "难度不能为空")
    @Pattern(regexp = "^(简单|中等|困难)$", message = "难度只能是简单、中等或困难")
    private String difficulty;
    
    /** 题干内容 */
    @NotBlank(message = "题干内容不能为空")
    private String questionContent;
    
    /** 解析内容 */
    private String explanation;
    
    /** 选项列表 */
    private List<OptionDTO> options;
    
    /** 正确答案 */
    private String correctAnswer;
    
    /**
     * 选项数据传输对象
     */
    public static class OptionDTO {
        /** 选项标识 */
        private String key;
        
        /** 选项内容 */
        private String content;
        
        /** 是否正确答案 */
        private Boolean isCorrect;
        
        public OptionDTO() {}
        
        public OptionDTO(String key, String content, Boolean isCorrect) {
            this.key = key;
            this.content = content;
            this.isCorrect = isCorrect;
        }
        
        // Getters and Setters
        public String getKey() {
            return key;
        }
        
        public void setKey(String key) {
            this.key = key;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        public Boolean getIsCorrect() {
            return isCorrect;
        }
        
        public void setIsCorrect(Boolean isCorrect) {
            this.isCorrect = isCorrect;
        }
    }
    
    // Getters and Setters
    public Long getQuestionId() {
        return questionId;
    }
    
    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }
    
    public Long getBankId() {
        return bankId;
    }
    
    public void setBankId(Long bankId) {
        this.bankId = bankId;
    }
    
    public String getQuestionType() {
        return questionType;
    }
    
    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public String getQuestionContent() {
        return questionContent;
    }
    
    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent;
    }
    
    public String getExplanation() {
        return explanation;
    }
    
    public void setExplanation(String explanation) {
        this.explanation = explanation;
    }
    
    public List<OptionDTO> getOptions() {
        return options;
    }
    
    public void setOptions(List<OptionDTO> options) {
        this.options = options;
    }
    
    public String getCorrectAnswer() {
        return correctAnswer;
    }
    
    public void setCorrectAnswer(String correctAnswer) {
        this.correctAnswer = correctAnswer;
    }
}
