package com.ruoyi.biz.mapper;

import java.util.List;
import com.ruoyi.biz.domain.Member;

/**
 * 成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface MemberMapper 
{
    /**
     * 查询成员
     *
     * @param memberId 成员主键
     * @return 成员
     */
    public Member selectMemberByMemberId(Long memberId);

    /**
     * 通过身份证号查询成员
     *
     * @param idCard 身份证号
     * @return 成员
     */
    public Member selectMemberByIdCard(String idCard);

    /**
     * 查询成员列表
     * 
     * @param member 成员
     * @return 成员集合
     */
    public List<Member> selectMemberList(Member member);

    /**
     * 新增成员
     * 
     * @param member 成员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 修改成员
     * 
     * @param member 成员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 删除成员
     * 
     * @param memberId 成员主键
     * @return 结果
     */
    public int deleteMemberByMemberId(Long memberId);

    /**
     * 批量删除成员
     * 
     * @param memberIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberByMemberIds(Long[] memberIds);
}
