package com.ruoyi.biz.mapper;

import java.util.List;
import com.ruoyi.biz.domain.QuestionBank;

/**
 * 题库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface QuestionBankMapper 
{
    /**
     * 查询题库
     * 
     * @param bankId 题库主键
     * @return 题库
     */
    public QuestionBank selectQuestionBankByBankId(Long bankId);

    /**
     * 查询题库列表
     * 
     * @param questionBank 题库
     * @return 题库集合
     */
    public List<QuestionBank> selectQuestionBankList(QuestionBank questionBank);

    /**
     * 新增题库
     * 
     * @param questionBank 题库
     * @return 结果
     */
    public int insertQuestionBank(QuestionBank questionBank);

    /**
     * 修改题库
     * 
     * @param questionBank 题库
     * @return 结果
     */
    public int updateQuestionBank(QuestionBank questionBank);

    /**
     * 删除题库
     * 
     * @param bankId 题库主键
     * @return 结果
     */
    public int deleteQuestionBankByBankId(Long bankId);

    /**
     * 批量删除题库
     * 
     * @param bankIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuestionBankByBankIds(Long[] bankIds);
}
