package com.ruoyi.biz.mapper;

import java.util.List;
import com.ruoyi.biz.domain.Question;

/**
 * 题目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface QuestionMapper 
{
    /**
     * 查询题目
     * 
     * @param questionId 题目主键
     * @return 题目
     */
    public Question selectQuestionByQuestionId(Long questionId);

    /**
     * 查询题目列表
     * 
     * @param question 题目
     * @return 题目集合
     */
    public List<Question> selectQuestionList(Question question);

    /**
     * 新增题目
     * 
     * @param question 题目
     * @return 结果
     */
    public int insertQuestion(Question question);

    /**
     * 修改题目
     * 
     * @param question 题目
     * @return 结果
     */
    public int updateQuestion(Question question);

    /**
     * 删除题目
     * 
     * @param questionId 题目主键
     * @return 结果
     */
    public int deleteQuestionByQuestionId(Long questionId);

    /**
     * 批量删除题目
     *
     * @param questionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuestionByQuestionIds(Long[] questionIds);

    /**
     * 统计题库中各题型的数量
     *
     * @param bankId 题库ID
     * @return 统计结果
     */
    public java.util.List<java.util.Map<String, Object>> selectQuestionStatistics(Long bankId);
}
