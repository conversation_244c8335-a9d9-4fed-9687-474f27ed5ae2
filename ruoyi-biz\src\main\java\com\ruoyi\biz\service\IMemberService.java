package com.ruoyi.biz.service;

import java.util.List;
import java.io.InputStream;
import com.ruoyi.biz.domain.Member;

/**
 * 成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IMemberService 
{
    /**
     * 查询成员
     * 
     * @param memberId 成员主键
     * @return 成员
     */
    public Member selectMemberByMemberId(Long memberId);

    /**
     * 查询成员列表
     * 
     * @param member 成员
     * @return 成员集合
     */
    public List<Member> selectMemberList(Member member);

    /**
     * 新增成员
     * 
     * @param member 成员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 修改成员
     * 
     * @param member 成员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 批量删除成员
     * 
     * @param memberIds 需要删除的成员主键集合
     * @return 结果
     */
    public int deleteMemberByMemberIds(Long[] memberIds);

    /**
     * 删除成员信息
     *
     * @param memberId 成员主键
     * @return 结果
     */
    public int deleteMemberByMemberId(Long memberId);

    /**
     * 从Excel文件导入成员数据
     *
     * @param inputStream Excel文件输入流
     * @param operName 操作用户
     * @return 结果
     */
    public String importMemberFromExcel(InputStream inputStream, String operName);

    /**
     * 重置成员密码
     *
     * @param memberId 成员主键
     * @return 结果
     */
    public int resetMemberPwd(Long memberId);
}
