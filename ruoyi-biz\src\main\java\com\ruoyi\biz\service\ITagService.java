package com.ruoyi.biz.service;

import java.util.List;
import com.ruoyi.biz.domain.Tag;

/**
 * 标签Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface ITagService 
{
    /**
     * 查询标签
     * 
     * @param tagId 标签主键
     * @return 标签
     */
    public Tag selectTagByTagId(Long tagId);

    /**
     * 查询标签列表
     * 
     * @param tag 标签
     * @return 标签集合
     */
    public List<Tag> selectTagList(Tag tag);

    /**
     * 新增标签
     * 
     * @param tag 标签
     * @return 结果
     */
    public int insertTag(Tag tag);

    /**
     * 修改标签
     * 
     * @param tag 标签
     * @return 结果
     */
    public int updateTag(Tag tag);

    /**
     * 批量删除标签
     * 
     * @param tagIds 需要删除的标签主键集合
     * @return 结果
     */
    public int deleteTagByTagIds(Long[] tagIds);

    /**
     * 删除标签信息
     *
     * @param tagId 标签主键
     * @return 结果
     */
    public int deleteTagByTagId(Long tagId);

    /**
     * 根据标签名称查询标签
     *
     * @param tagName 标签名称
     * @return 标签
     */
    public Tag selectTagByName(String tagName);
}
