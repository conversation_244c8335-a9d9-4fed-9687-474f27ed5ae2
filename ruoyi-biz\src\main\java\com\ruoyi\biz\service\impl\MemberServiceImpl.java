package com.ruoyi.biz.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.io.InputStream;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.biz.mapper.MemberMapper;
import com.ruoyi.biz.domain.Member;
import com.ruoyi.biz.domain.Tag;
import com.ruoyi.biz.service.IMemberService;
import com.ruoyi.biz.service.ITagService;

/**
 * 成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class MemberServiceImpl implements IMemberService 
{
    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private ITagService tagService;

    /**
     * 查询成员
     * 
     * @param memberId 成员主键
     * @return 成员
     */
    @Override
    public Member selectMemberByMemberId(Long memberId)
    {
        return memberMapper.selectMemberByMemberId(memberId);
    }

    /**
     * 查询成员列表
     * 
     * @param member 成员
     * @return 成员
     */
    @Override
    public List<Member> selectMemberList(Member member)
    {
        return memberMapper.selectMemberList(member);
    }

    /**
     * 新增成员
     * 
     * @param member 成员
     * @return 结果
     */
    @Override
    public int insertMember(Member member)
    {
        member.setCreateTime(DateUtils.getNowDate());
        return memberMapper.insertMember(member);
    }

    /**
     * 修改成员
     * 
     * @param member 成员
     * @return 结果
     */
    @Override
    public int updateMember(Member member)
    {
        member.setUpdateTime(DateUtils.getNowDate());
        return memberMapper.updateMember(member);
    }

    /**
     * 批量删除成员
     * 
     * @param memberIds 需要删除的成员主键
     * @return 结果
     */
    @Override
    public int deleteMemberByMemberIds(Long[] memberIds)
    {
        return memberMapper.deleteMemberByMemberIds(memberIds);
    }

    /**
     * 删除成员信息
     *
     * @param memberId 成员主键
     * @return 结果
     */
    @Override
    public int deleteMemberByMemberId(Long memberId)
    {
        return memberMapper.deleteMemberByMemberId(memberId);
    }



    /**
     * 从Excel文件导入成员数据
     *
     * @param inputStream Excel文件输入流
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importMemberFromExcel(InputStream inputStream, String operName)
    {
        Workbook workbook = null;
        try
        {
            try
            {
                workbook = new XSSFWorkbook(inputStream);
            }
            catch (Exception e)
            {
                inputStream.reset();
                workbook = new HSSFWorkbook(inputStream);
            }

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null)
            {
                throw new ServiceException("Excel文件格式错误！");
            }

            int successNum = 0;
            int failureNum = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();

            // 从第3行开始读取数据（索引为2）
            for (int i = 2; i <= sheet.getLastRowNum(); i++)
            {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查是否为完全空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 4; j++) // 检查前4列
                {
                    Cell cell = row.getCell(j);
                    if (cell != null && StringUtils.isNotEmpty(getCellValue(cell)))
                    {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (isEmptyRow) continue; // 跳过完全空行

                try
                {
                    // 读取数据
                    String idCard = getCellValue(row.getCell(0)); // 账号
                    String realName = getCellValue(row.getCell(1)); // 姓名
                    String password = getCellValue(row.getCell(2)); // 密码
                    String tagNames = getCellValue(row.getCell(3)); // 标签

                    // 跳过账号为空的行
                    if (StringUtils.isEmpty(idCard))
                    {
                        continue; // 直接跳过，不计入失败数
                    }

                    // 姓名不是必填项，如果为空则使用账号作为姓名
                    if (StringUtils.isEmpty(realName))
                    {
                        realName = idCard;
                    }

                    // 处理标签
                    String tagIds = processTagNames(tagNames);

                    // 检查是否存在该成员
                    Member existingMember = memberMapper.selectMemberByIdCard(idCard);
                    if (existingMember != null)
                    {
                        // 追加标签信息（合并已有标签和新标签）
                        String mergedTagIds = mergeTagIds(existingMember.getTags(), tagIds);
                        existingMember.setTags(mergedTagIds);
                        existingMember.setUpdateTime(DateUtils.getNowDate());
                        memberMapper.updateMember(existingMember);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + idCard + " 更新成功");
                    }
                    else
                    {
                        // 创建新成员
                        Member member = new Member();
                        member.setIdCard(idCard);
                        member.setRealName(realName);
                        member.setPassword(StringUtils.isEmpty(password) ? SecurityUtils.encryptPassword("123456") : SecurityUtils.encryptPassword(password));
                        member.setTags(tagIds);
                        member.setStatus(0L); // 默认启用
                        member.setMemberType(0L); // 默认学员
                        member.setCreateTime(DateUtils.getNowDate());

                        memberMapper.insertMember(member);
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + idCard + " 导入成功");
                    }
                }
                catch (Exception e)
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、第" + (i + 1) + "行：" + e.getMessage());
                }
            }

            if (failureNum > 0)
            {
                failureMsg.insert(0, "导入完成！成功 " + successNum + " 条，失败 " + failureNum + " 条，错误如下：");
                if (successNum > 0)
                {
                    return failureMsg.toString() + successMsg.toString();
                }
                else
                {
                    throw new ServiceException(failureMsg.toString());
                }
            }
            else
            {
                successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            }
            return successMsg.toString();
        }
        catch (Exception e)
        {
            throw new ServiceException("导入失败：" + e.getMessage());
        }
        finally
        {
            if (workbook != null)
            {
                try
                {
                    workbook.close();
                }
                catch (Exception e)
                {
                    // 忽略关闭异常
                }
            }
        }
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell)
    {
        if (cell == null) return "";

        switch (cell.getCellType())
        {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell))
                {
                    return DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, cell.getDateCellValue());
                }
                else
                {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 处理标签名称，返回标签ID字符串
     */
    private String processTagNames(String tagNames)
    {
        if (StringUtils.isEmpty(tagNames)) return "";

        List<String> tagIdList = new ArrayList<>();
        String[] names = tagNames.split("[,，]"); // 支持中英文逗号分隔

        for (String name : names)
        {
            name = name.trim();
            if (StringUtils.isEmpty(name)) continue;

            // 查找标签
            Tag tag = tagService.selectTagByName(name);
            if (tag == null)
            {
                // 标签不存在，创建新标签
                tag = new Tag();
                tag.setTagName(name);
                tag.setOrderNum(0L);
                tag.setCreateTime(DateUtils.getNowDate());
                tagService.insertTag(tag);
            }
            tagIdList.add(tag.getTagId().toString());
        }

        return String.join(",", tagIdList);
    }

    /**
     * 合并标签ID（去重）
     */
    private String mergeTagIds(String existingTagIds, String newTagIds)
    {
        if (StringUtils.isEmpty(existingTagIds) && StringUtils.isEmpty(newTagIds))
        {
            return "";
        }

        if (StringUtils.isEmpty(existingTagIds))
        {
            return newTagIds;
        }

        if (StringUtils.isEmpty(newTagIds))
        {
            return existingTagIds;
        }

        // 使用Set去重
        java.util.Set<String> tagIdSet = new java.util.HashSet<>();

        // 添加已有标签
        String[] existingIds = existingTagIds.split(",");
        for (String id : existingIds)
        {
            if (StringUtils.isNotEmpty(id.trim()))
            {
                tagIdSet.add(id.trim());
            }
        }

        // 添加新标签
        String[] newIds = newTagIds.split(",");
        for (String id : newIds)
        {
            if (StringUtils.isNotEmpty(id.trim()))
            {
                tagIdSet.add(id.trim());
            }
        }

        return String.join(",", tagIdSet);
    }

    /**
     * 重置成员密码
     *
     * @param memberId 成员主键
     * @return 结果
     */
    @Override
    public int resetMemberPwd(Long memberId)
    {
        Member member = new Member();
        member.setMemberId(memberId);
        member.setPassword(SecurityUtils.encryptPassword("123456"));
        member.setUpdateTime(DateUtils.getNowDate());
        return memberMapper.updateMember(member);
    }
}
