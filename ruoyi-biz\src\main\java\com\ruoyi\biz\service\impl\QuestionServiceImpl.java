package com.ruoyi.biz.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.biz.mapper.QuestionMapper;
import com.ruoyi.biz.domain.Question;
import com.ruoyi.biz.service.IQuestionService;

/**
 * 题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class QuestionServiceImpl implements IQuestionService 
{
    @Autowired
    private QuestionMapper questionMapper;

    /**
     * 查询题目
     * 
     * @param questionId 题目主键
     * @return 题目
     */
    @Override
    public Question selectQuestionByQuestionId(Long questionId)
    {
        return questionMapper.selectQuestionByQuestionId(questionId);
    }

    /**
     * 查询题目列表
     * 
     * @param question 题目
     * @return 题目
     */
    @Override
    public List<Question> selectQuestionList(Question question)
    {
        return questionMapper.selectQuestionList(question);
    }

    /**
     * 新增题目
     * 
     * @param question 题目
     * @return 结果
     */
    @Override
    public int insertQuestion(Question question)
    {
        question.setCreateTime(DateUtils.getNowDate());
        // 如果没有设置创建人，则设置当前用户
        if (question.getCreateBy() == null || question.getCreateBy().isEmpty()) {
            try {
                question.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                question.setCreateBy("admin");
            }
        }
        return questionMapper.insertQuestion(question);
    }

    /**
     * 修改题目
     * 
     * @param question 题目
     * @return 结果
     */
    @Override
    public int updateQuestion(Question question)
    {
        question.setUpdateTime(DateUtils.getNowDate());
        return questionMapper.updateQuestion(question);
    }

    /**
     * 批量删除题目
     * 
     * @param questionIds 需要删除的题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionByQuestionIds(Long[] questionIds)
    {
        return questionMapper.deleteQuestionByQuestionIds(questionIds);
    }

    /**
     * 删除题目信息
     *
     * @param questionId 题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionByQuestionId(Long questionId)
    {
        return questionMapper.deleteQuestionByQuestionId(questionId);
    }

    /**
     * 获取题库统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getQuestionStatistics(Long bankId)
    {
        List<Map<String, Object>> statistics = questionMapper.selectQuestionStatistics(bankId);

        // 初始化统计数据
        Map<String, Object> result = new HashMap<>();
        result.put("total", 0);
        result.put("singleChoice", 0);
        result.put("multipleChoice", 0);
        result.put("judgment", 0);

        int total = 0;

        // 处理统计结果
        for (Map<String, Object> stat : statistics) {
            try {
                // 获取统计数据
                Object questionTypeObj = stat.get("question_type");
                Object countObj = stat.get("count");

                if (questionTypeObj == null || countObj == null) {
                    continue;
                }

                // 简单的数字转换
                int questionType = 0;
                int count = 0;

                // 处理question_type
                if (questionTypeObj instanceof Number) {
                    questionType = ((Number) questionTypeObj).intValue();
                } else {
                    String str = questionTypeObj.toString().trim();
                    if (str.matches("^\\d+$")) {
                        questionType = Integer.parseInt(str);
                    } else {
                        continue; // 跳过无效数据
                    }
                }

                // 处理count
                if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                } else {
                    String str = countObj.toString().trim();
                    if (str.matches("^\\d+$")) {
                        count = Integer.parseInt(str);
                    } else {
                        continue; // 跳过无效数据
                    }
                }

                // 只处理有效的题型和数量
                if (count > 0 && questionType >= 1 && questionType <= 3) {
                    total += count;

                    switch (questionType) {
                        case 1: // 单选题
                            result.put("singleChoice", count);
                            break;
                        case 2: // 多选题
                            result.put("multipleChoice", count);
                            break;
                        case 3: // 判断题
                            result.put("judgment", count);
                            break;
                    }
                }
            } catch (Exception e) {
                // 静默处理错误，不影响其他统计
                System.err.println("跳过无效统计数据: " + stat);
            }
        }

        result.put("total", total);
        return result;
    }
}
