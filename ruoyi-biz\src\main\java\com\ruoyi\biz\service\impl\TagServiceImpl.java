package com.ruoyi.biz.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.biz.mapper.TagMapper;
import com.ruoyi.biz.domain.Tag;
import com.ruoyi.biz.service.ITagService;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class TagServiceImpl implements ITagService 
{
    @Autowired
    private TagMapper tagMapper;

    /**
     * 查询标签
     * 
     * @param tagId 标签主键
     * @return 标签
     */
    @Override
    public Tag selectTagByTagId(Long tagId)
    {
        return tagMapper.selectTagByTagId(tagId);
    }

    /**
     * 查询标签列表
     * 
     * @param tag 标签
     * @return 标签
     */
    @Override
    public List<Tag> selectTagList(Tag tag)
    {
        return tagMapper.selectTagList(tag);
    }

    /**
     * 新增标签
     * 
     * @param tag 标签
     * @return 结果
     */
    @Override
    public int insertTag(Tag tag)
    {
        tag.setCreateTime(DateUtils.getNowDate());
        return tagMapper.insertTag(tag);
    }

    /**
     * 修改标签
     * 
     * @param tag 标签
     * @return 结果
     */
    @Override
    public int updateTag(Tag tag)
    {
        tag.setUpdateTime(DateUtils.getNowDate());
        return tagMapper.updateTag(tag);
    }

    /**
     * 批量删除标签
     * 
     * @param tagIds 需要删除的标签主键
     * @return 结果
     */
    @Override
    public int deleteTagByTagIds(Long[] tagIds)
    {
        return tagMapper.deleteTagByTagIds(tagIds);
    }

    /**
     * 删除标签信息
     *
     * @param tagId 标签主键
     * @return 结果
     */
    @Override
    public int deleteTagByTagId(Long tagId)
    {
        return tagMapper.deleteTagByTagId(tagId);
    }

    /**
     * 根据标签名称查询标签
     *
     * @param tagName 标签名称
     * @return 标签
     */
    @Override
    public Tag selectTagByName(String tagName)
    {
        return tagMapper.selectTagByName(tagName);
    }
}
