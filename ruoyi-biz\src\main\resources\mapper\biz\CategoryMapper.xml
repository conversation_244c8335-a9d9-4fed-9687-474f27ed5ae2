<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.CategoryMapper">
    
    <resultMap type="Category" id="CategoryResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="name"    column="name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCategoryVo">
        select id, parent_id, name, order_num, create_time, update_time from tbl_category
    </sql>

    <select id="selectCategoryList" parameterType="Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
        </where>
        order by order_num desc, id desc
    </select>
    
    <select id="selectCategoryById" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertCategory" parameterType="Category" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCategory" parameterType="Category">
        update tbl_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCategoryById" parameterType="Long">
        delete from tbl_category where id = #{id}
    </delete>

    <delete id="deleteCategoryByIds" parameterType="String">
        delete from tbl_category where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>