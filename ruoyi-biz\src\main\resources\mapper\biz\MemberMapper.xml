<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.MemberMapper">
    
    <resultMap type="Member" id="MemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="password"    column="password"    />
        <result property="realName"    column="real_name"    />
        <result property="idCard"    column="id_card"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="orgId"    column="org_id"    />
        <result property="tags"    column="tags"    />
        <result property="tagNames"    column="tag_names"    />
        <result property="points"    column="points"    />
        <result property="memberType"    column="member_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMemberVo">
        select member_id, password, real_name, id_card, phone, email, org_id, tags, points, member_type, status, create_time, update_time from tbl_member
    </sql>

    <select id="selectMemberList" parameterType="Member" resultMap="MemberResult">
        SELECT 
            m.member_id, 
            m.password, 
            m.real_name, 
            m.id_card, 
            m.phone, 
            m.email, 
            m.org_id, 
            m.tags,
            (
                SELECT 
                    GROUP_CONCAT(tag_name) 
                FROM 
                    tbl_tag 
                WHERE 
                    FIND_IN_SET(tag_id, m.tags)
            ) AS tag_names,
            m.points, 
            m.member_type, 
            m.status, 
            m.create_time, 
            m.update_time 
        FROM 
            tbl_member m
        <where>  
            <if test="realName != null  and realName != ''"> and m.real_name like concat('%', #{realName}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and m.id_card like concat('%', #{idCard}, '%')</if>
            <if test="phone != null  and phone != ''"> and m.phone = #{phone}</if>
            <if test="email != null  and email != ''"> and m.email = #{email}</if>
            <if test="orgId != null "> and m.org_id = #{orgId}</if>
            <if test="tags != null  and tags != ''"> 
                and EXISTS (
                    SELECT 1 FROM tbl_tag WHERE tag_id = #{tags} AND FIND_IN_SET(tag_id, m.tags)
                )
            </if>
            <if test="points != null "> and m.points = #{points}</if>
            <if test="memberType != null "> and m.member_type = #{memberType}</if>
            <if test="status != null "> and m.status = #{status}</if>
        </where>
        ORDER BY m.create_time DESC
    </select>
    
    <select id="selectMemberByMemberId" parameterType="Long" resultMap="MemberResult">
        SELECT 
            m.member_id, 
            m.password, 
            m.real_name, 
            m.id_card, 
            m.phone, 
            m.email, 
            m.org_id, 
            m.tags,
            (
                SELECT 
                    GROUP_CONCAT(tag_name) 
                FROM 
                    tbl_tag 
                WHERE 
                    FIND_IN_SET(tag_id, m.tags)
            ) AS tag_names,
            m.points, 
            m.member_type, 
            m.status, 
            m.create_time, 
            m.update_time 
        FROM
            tbl_member m
        WHERE m.member_id = #{memberId}
    </select>

    <select id="selectMemberByIdCard" parameterType="String" resultMap="MemberResult">
        SELECT
            m.member_id,
            m.password,
            m.real_name,
            m.id_card,
            m.phone,
            m.email,
            m.org_id,
            m.tags,
            (
                SELECT
                    GROUP_CONCAT(tag_name)
                FROM
                    tbl_tag
                WHERE
                    FIND_IN_SET(tag_id, m.tags)
            ) AS tag_names,
            m.points,
            m.member_type,
            m.status,
            m.create_time,
            m.update_time
        FROM
            tbl_member m
        WHERE m.id_card = #{idCard}
    </select>

    <insert id="insertMember" parameterType="Member" useGeneratedKeys="true" keyProperty="memberId">
        insert into tbl_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="password != null and password != ''">password,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="orgId != null">org_id,</if>
            <if test="tags != null">tags,</if>
            <if test="points != null">points,</if>
            <if test="memberType != null">member_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="password != null and password != ''">#{password},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="tags != null">#{tags},</if>
            <if test="points != null">#{points},</if>
            <if test="memberType != null">#{memberType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMember" parameterType="Member">
        update tbl_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="password != null">password = #{password},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="points != null">points = #{points},</if>
            <if test="memberType != null">member_type = #{memberType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteMemberByMemberId" parameterType="Long">
        delete from tbl_member where member_id = #{memberId}
    </delete>

    <delete id="deleteMemberByMemberIds" parameterType="String">
        delete from tbl_member where member_id in 
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>
</mapper>