<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.QuestionBankMapper">
    
    <resultMap type="QuestionBank" id="QuestionBankResult">
        <result property="bankId"    column="bank_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="bankName"    column="bank_name"    />
        <result property="bankDesc"    column="bank_desc"    />
        <result property="categoryId"    column="category_id"    />
        <result property="coverImg"    column="cover_img"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuestionBankVo">
        select bank_id, order_num, bank_name, bank_desc, category_id, cover_img, status, create_by, create_time, update_time from tbl_question_bank
    </sql>

    <select id="selectQuestionBankList" parameterType="QuestionBank" resultMap="QuestionBankResult">
        <include refid="selectQuestionBankVo"/>
        <where>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
        </where>
        order by order_num desc, bank_id desc
    </select>
    
    <select id="selectQuestionBankByBankId" parameterType="Long" resultMap="QuestionBankResult">
        <include refid="selectQuestionBankVo"/>
        where bank_id = #{bankId}
    </select>

    <insert id="insertQuestionBank" parameterType="QuestionBank" useGeneratedKeys="true" keyProperty="bankId">
        insert into tbl_question_bank
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNum != null">order_num,</if>
            <if test="bankName != null and bankName != ''">bank_name,</if>
            <if test="bankDesc != null">bank_desc,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="coverImg != null">cover_img,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNum != null">#{orderNum},</if>
            <if test="bankName != null and bankName != ''">#{bankName},</if>
            <if test="bankDesc != null">#{bankDesc},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="coverImg != null">#{coverImg},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQuestionBank" parameterType="QuestionBank">
        update tbl_question_bank
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="bankName != null and bankName != ''">bank_name = #{bankName},</if>
            <if test="bankDesc != null">bank_desc = #{bankDesc},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="coverImg != null">cover_img = #{coverImg},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where bank_id = #{bankId}
    </update>

    <delete id="deleteQuestionBankByBankId" parameterType="Long">
        delete from tbl_question_bank where bank_id = #{bankId}
    </delete>

    <delete id="deleteQuestionBankByBankIds" parameterType="String">
        delete from tbl_question_bank where bank_id in 
        <foreach item="bankId" collection="array" open="(" separator="," close=")">
            #{bankId}
        </foreach>
    </delete>
</mapper>