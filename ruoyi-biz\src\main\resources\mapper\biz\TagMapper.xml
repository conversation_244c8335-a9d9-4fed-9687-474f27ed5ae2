<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.TagMapper">
    
    <resultMap type="Tag" id="TagResult">
        <result property="tagId"    column="tag_id"    />
        <result property="tagName"    column="tag_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTagVo">
        select tag_id, tag_name, order_num, create_time, update_time from tbl_tag
    </sql>

    <select id="selectTagList" parameterType="Tag" resultMap="TagResult">
        <include refid="selectTagVo"/>
        <where>  
            <if test="tagName != null  and tagName != ''"> and tag_name like concat('%', #{tagName}, '%')</if>
        </where>
        order by order_num desc, tag_id desc
    </select>
    
    <select id="selectTagByTagId" parameterType="Long" resultMap="TagResult">
        <include refid="selectTagVo"/>
        where tag_id = #{tagId}
    </select>

    <select id="selectTagByName" parameterType="String" resultMap="TagResult">
        <include refid="selectTagVo"/>
        where tag_name = #{tagName}
    </select>

    <insert id="insertTag" parameterType="Tag" useGeneratedKeys="true" keyProperty="tagId">
        insert into tbl_tag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">#{tagName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTag" parameterType="Tag">
        update tbl_tag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tagName != null and tagName != ''">tag_name = #{tagName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where tag_id = #{tagId}
    </update>

    <delete id="deleteTagByTagId" parameterType="Long">
        delete from tbl_tag where tag_id = #{tagId}
    </delete>

    <delete id="deleteTagByTagIds" parameterType="String">
        delete from tbl_tag where tag_id in 
        <foreach item="tagId" collection="array" open="(" separator="," close=")">
            #{tagId}
        </foreach>
    </delete>
</mapper>