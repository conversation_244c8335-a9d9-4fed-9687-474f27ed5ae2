{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "question", "type", "Object", "required", "index", "Number", "expanded", "Boolean", "default", "selected", "data", "computed", "isChoiceQuestion", "includes", "questionType", "isJudgmentQuestion", "validOptions", "options", "JSON", "parse", "e", "Array", "isArray", "filter", "option", "key", "optionKey", "content", "optionContent", "<PERSON><PERSON><PERSON>", "toString", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "methods", "getQuestionTypeName", "typeMap", "getQuestionTypeColor", "colorMap", "getDifficultyName", "difficulty", "difficultyMap", "getJudgmentAnswerType", "answer", "isTrue", "getJudgmentAnswerText", "formatTime", "time", "Date", "toLocaleString", "getOption<PERSON>ey", "String", "fromCharCode", "isCorrectOption", "original<PERSON>ey", "find", "opt", "isCorrect", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "correctAnswers", "split", "map", "ans", "toggleExpand", "$emit", "questionId", "handleEdit", "handleCopy", "handleDelete", "handleSelectionChange"], "sources": ["src/views/biz/questionBank/components/QuestionCard.vue"], "sourcesContent": ["<template>\n  <div class=\"question-card\">\n    <!-- 题目头部 -->\n    <div class=\"question-header\">\n      <div class=\"header-left\">\n        <el-checkbox\n          :value=\"selected\"\n          @change=\"handleSelectionChange\"\n          style=\"margin-right: 12px;\"\n        ></el-checkbox>\n        <span class=\"question-number\">{{ index }}.</span>\n        <span class=\"question-type-bracket\">[{{ getQuestionTypeName(question.questionType) }}]</span>\n        <span class=\"difficulty-label\">难度系数: {{ getDifficultyName(question.difficulty) }}</span>\n        <span class=\"create-time\">创建时间: {{ formatTime(question.createTime) }}</span>\n      </div>\n      <div class=\"header-right\">\n        <el-button\n          type=\"text\"\n          :icon=\"expanded ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpand\"\n          size=\"small\"\n        >\n          {{ expanded ? '收起' : '展开' }}\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-edit\"\n          @click=\"handleEdit\"\n          size=\"small\"\n        >\n          编辑\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-copy-document\"\n          @click=\"handleCopy\"\n          size=\"small\"\n        >\n          复制\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-delete\"\n          @click=\"handleDelete\"\n          size=\"small\"\n          style=\"color: #F56C6C;\"\n        >\n          删除\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 题目内容 -->\n    <div class=\"question-content\">\n      <div v-html=\"question.questionContent\"></div>\n    </div>\n\n    <!-- 展开内容 -->\n    <div v-if=\"expanded\" class=\"question-detail\">\n\n\n      <!-- 题目选项（包括选择题和判断题） -->\n      <div v-if=\"validOptions.length > 0\" class=\"answer-section\">\n        <div class=\"options-list\">\n          <div\n            v-for=\"(option, index) in validOptions\"\n            :key=\"option.key || option.optionKey\"\n            class=\"option-item\"\n            :class=\"{ 'correct-option': isCorrectOption(option.key || option.optionKey) }\"\n          >\n            <span class=\"option-key\">{{ getOptionKey(option, index) }}.</span>\n            <span class=\"option-content\" v-html=\"option.content || option.optionContent\"></span>\n            <span v-if=\"isCorrectOption(option.key || option.optionKey)\" class=\"correct-mark\">✓</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 题目解析 -->\n      <div v-if=\"question.explanation || question.analysis\" class=\"explanation-section\">\n        <div class=\"explanation-text\" v-html=\"question.explanation || question.analysis\"></div>\n      </div>\n\n\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"QuestionCard\",\n  props: {\n    question: {\n      type: Object,\n      required: true\n    },\n    index: {\n      type: Number,\n      required: true\n    },\n    expanded: {\n      type: Boolean,\n      default: false\n    },\n    selected: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isChoiceQuestion() {\n      return ['single', 'multiple', 1, 2].includes(this.question.questionType)\n    },\n    isJudgmentQuestion() {\n      return ['judgment', 3].includes(this.question.questionType)\n    },\n    // 过滤有效的选项\n    validOptions() {\n      let options = this.question.options\n\n      // 如果options是字符串，需要解析为JSON\n      if (typeof options === 'string') {\n        try {\n          options = JSON.parse(options)\n        } catch (e) {\n          return []\n        }\n      }\n\n      if (!options || !Array.isArray(options)) {\n        return []\n      }\n\n      return options.filter(option => {\n        // 检查选项是否有效\n        const key = option.key || option.optionKey\n        const content = option.content || option.optionContent\n\n        const hasKey = key && key.toString().trim()\n        // 内容可以为空，但key必须存在\n        const hasValidKey = hasKey\n        // 如果内容为空或只包含HTML标签，则过滤掉\n        const hasValidContent = content && content.toString().trim() &&\n                               content.toString().replace(/<[^>]*>/g, '').trim()\n\n        return hasValidKey && hasValidContent\n      })\n    }\n  },\n  methods: {\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        // 字符串格式\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        // 数字格式\n        1: '单选题',\n        2: '多选题',\n        3: '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        // 字符串格式\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning',\n        // 数字格式\n        1: 'primary',\n        2: 'success',\n        3: 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 获取难度名称\n    getDifficultyName(difficulty) {\n      const difficultyMap = {\n        // 字符串格式\n        '简单': '简单',\n        '中等': '中等',\n        '困难': '困难',\n        // 数字格式\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || '中等'\n    },\n    // 获取判断题答案类型（用于标签颜色）\n    getJudgmentAnswerType(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? 'success' : 'danger'\n    },\n    // 获取判断题答案文本\n    getJudgmentAnswerText(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? '正确' : '错误'\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '--'\n      return new Date(time).toLocaleString()\n    },\n\n    // 获取选项键显示\n    getOptionKey(option, index) {\n      const key = option.key || option.optionKey\n\n      // 判断题特殊处理\n      if (this.isJudgmentQuestion) {\n        if (key === 'true' || key === true) {\n          return 'A'\n        } else if (key === 'false' || key === false) {\n          return 'B'\n        }\n      }\n\n      // 选择题直接返回原key，如果没有key则使用字母序列\n      if (key) {\n        return key\n      } else {\n        return String.fromCharCode(65 + index) // A, B, C, D...\n      }\n    },\n\n    // 判断是否为正确选项\n    isCorrectOption(originalKey) {\n      if (!originalKey) return false\n\n      // 首先尝试从选项的 isCorrect 字段判断\n      const option = this.validOptions.find(opt =>\n        (opt.key || opt.optionKey) === originalKey\n      )\n      if (option && option.isCorrect !== undefined) {\n        return option.isCorrect === true\n      }\n\n      // 如果没有 isCorrect 字段，则使用 correctAnswer 字段\n      if (!this.question.correctAnswer) return false\n\n      // 处理单选题（字符串格式：'single' 或数字格式：1）\n      if (this.question.questionType === 'single' || this.question.questionType === 1) {\n        return this.question.correctAnswer.toString() === originalKey.toString()\n      }\n      // 处理多选题（字符串格式：'multiple' 或数字格式：2）\n      else if (this.question.questionType === 'multiple' || this.question.questionType === 2) {\n        const correctAnswers = this.question.correctAnswer.toString().split(',').map(ans => ans.trim())\n        return correctAnswers.includes(originalKey.toString())\n      }\n      return false\n    },\n    // 切换展开状态\n    toggleExpand() {\n      this.$emit('toggle-expand', this.question.questionId)\n    },\n    // 编辑题目\n    handleEdit() {\n      this.$emit('edit', this.question)\n    },\n    // 复制题目\n    handleCopy() {\n      this.$emit('copy', this.question)\n    },\n    // 删除题目\n    handleDelete() {\n      this.$emit('delete', this.question)\n    },\n\n    // 处理选择状态变化\n    handleSelectionChange(selected) {\n      this.$emit('selection-change', this.question.questionId, selected)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.question-card {\n  background: #fff;\n  border-radius: 6px;\n  box-shadow: 0 2px 6px rgba(0,0,0,0.08);\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.question-number {\n  font-weight: bold;\n  font-size: 15px;\n  color: #333;\n}\n\n.question-type-bracket {\n  color: #409eff;\n  font-weight: 500;\n  font-size: 13px;\n}\n\n.difficulty-label {\n  font-size: 13px;\n  color: #666;\n}\n\n.create-time {\n  font-size: 11px;\n  color: #999;\n}\n\n.header-right {\n  display: flex;\n  gap: 5px;\n}\n\n.question-preview {\n  padding: 16px 20px;\n}\n\n.question-content {\n  padding: 12px 16px 12px 50px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  background: #fafafa;\n}\n\n.question-detail {\n  border-top: 1px solid #e9ecef;\n  padding: 20px;\n}\n\n.detail-section {\n  margin-bottom: 20px;\n}\n\n.detail-section:last-child {\n  margin-bottom: 0;\n}\n\n.detail-section h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #333;\n  font-weight: 600;\n}\n\n.content-text, .explanation-text {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.options-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fff;\n  border-radius: 6px;\n  gap: 12px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.option-item:hover {\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.option-item.correct-option {\n  background: #f0f9ff;\n  border: 1px solid #67c23a;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);\n}\n\n.option-key {\n  font-weight: bold;\n  color: #409eff;\n  min-width: 24px;\n  font-size: 15px;\n}\n\n.option-content {\n  flex: 1;\n  font-size: 15px;\n  color: #333;\n  line-height: 1.6;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.answer-section {\n  padding: 20px;\n  background: #f9f9f9;\n}\n\n.judgment-answer {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 4px;\n}\n\n.answer-label {\n  font-weight: bold;\n  color: #333;\n}\n\n.answer-value {\n  font-weight: 500;\n}\n\n.answer-value.success {\n  color: #67c23a;\n}\n\n.answer-value.danger {\n  color: #f56c6c;\n}\n\n.explanation-section {\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n  background: #f5f5f5;\n}\n\n.explanation-text {\n  font-size: 14px;\n  line-height: 1.8;\n  color: #666;\n  background: #fff;\n  padding: 16px;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.explanation-text::before {\n  content: \"解析：\";\n  font-weight: bold;\n  color: #409eff;\n  display: block;\n  margin-bottom: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAwFA;EACAA,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;IACA;IACAG,QAAA;MACAL,IAAA,EAAAM,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAR,IAAA,EAAAM,OAAA;MACAC,OAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;EACA;EACAC,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,oCAAAC,QAAA,MAAAb,QAAA,CAAAc,YAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,uBAAAF,QAAA,MAAAb,QAAA,CAAAc,YAAA;IACA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,IAAAC,OAAA,QAAAjB,QAAA,CAAAiB,OAAA;;MAEA;MACA,WAAAA,OAAA;QACA;UACAA,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAF,OAAA;QACA,SAAAG,CAAA;UACA;QACA;MACA;MAEA,KAAAH,OAAA,KAAAI,KAAA,CAAAC,OAAA,CAAAL,OAAA;QACA;MACA;MAEA,OAAAA,OAAA,CAAAM,MAAA,WAAAC,MAAA;QACA;QACA,IAAAC,GAAA,GAAAD,MAAA,CAAAC,GAAA,IAAAD,MAAA,CAAAE,SAAA;QACA,IAAAC,OAAA,GAAAH,MAAA,CAAAG,OAAA,IAAAH,MAAA,CAAAI,aAAA;QAEA,IAAAC,MAAA,GAAAJ,GAAA,IAAAA,GAAA,CAAAK,QAAA,GAAAC,IAAA;QACA;QACA,IAAAC,WAAA,GAAAH,MAAA;QACA;QACA,IAAAI,eAAA,GAAAN,OAAA,IAAAA,OAAA,CAAAG,QAAA,GAAAC,IAAA,MACAJ,OAAA,CAAAG,QAAA,GAAAI,OAAA,iBAAAH,IAAA;QAEA,OAAAC,WAAA,IAAAC,eAAA;MACA;IACA;EACA;EACAE,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAnC,IAAA;MACA,IAAAoC,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAApC,IAAA;IACA;IACA;IACAqC,oBAAA,WAAAA,qBAAArC,IAAA;MACA,IAAAsC,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAtC,IAAA;IACA;IACA;IACAuC,iBAAA,WAAAA,kBAAAC,UAAA;MACA,IAAAC,aAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,aAAA,CAAAD,UAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,MAAA;MACA,IAAAC,MAAA,GAAAD,MAAA,eAAAA,MAAA,aAAAA,MAAA,UAAAA,MAAA;MACA,OAAAC,MAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAF,MAAA;MACA,IAAAC,MAAA,GAAAD,MAAA,eAAAA,MAAA,aAAAA,MAAA,UAAAA,MAAA;MACA,OAAAC,MAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,WAAAC,IAAA,CAAAD,IAAA,EAAAE,cAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA3B,MAAA,EAAApB,KAAA;MACA,IAAAqB,GAAA,GAAAD,MAAA,CAAAC,GAAA,IAAAD,MAAA,CAAAE,SAAA;;MAEA;MACA,SAAAX,kBAAA;QACA,IAAAU,GAAA,eAAAA,GAAA;UACA;QACA,WAAAA,GAAA,gBAAAA,GAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAA,GAAA;QACA,OAAAA,GAAA;MACA;QACA,OAAA2B,MAAA,CAAAC,YAAA,MAAAjD,KAAA;MACA;IACA;IAEA;IACAkD,eAAA,WAAAA,gBAAAC,WAAA;MACA,KAAAA,WAAA;;MAEA;MACA,IAAA/B,MAAA,QAAAR,YAAA,CAAAwC,IAAA,WAAAC,GAAA;QAAA,OACA,CAAAA,GAAA,CAAAhC,GAAA,IAAAgC,GAAA,CAAA/B,SAAA,MAAA6B,WAAA;MAAA,CACA;MACA,IAAA/B,MAAA,IAAAA,MAAA,CAAAkC,SAAA,KAAAC,SAAA;QACA,OAAAnC,MAAA,CAAAkC,SAAA;MACA;;MAEA;MACA,UAAA1D,QAAA,CAAA4D,aAAA;;MAEA;MACA,SAAA5D,QAAA,CAAAc,YAAA,sBAAAd,QAAA,CAAAc,YAAA;QACA,YAAAd,QAAA,CAAA4D,aAAA,CAAA9B,QAAA,OAAAyB,WAAA,CAAAzB,QAAA;MACA;MACA;MAAA,KACA,SAAA9B,QAAA,CAAAc,YAAA,wBAAAd,QAAA,CAAAc,YAAA;QACA,IAAA+C,cAAA,QAAA7D,QAAA,CAAA4D,aAAA,CAAA9B,QAAA,GAAAgC,KAAA,MAAAC,GAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAjC,IAAA;QAAA;QACA,OAAA8B,cAAA,CAAAhD,QAAA,CAAA0C,WAAA,CAAAzB,QAAA;MACA;MACA;IACA;IACA;IACAmC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,uBAAAlE,QAAA,CAAAmE,UAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAF,KAAA,cAAAlE,QAAA;IACA;IACA;IACAqE,UAAA,WAAAA,WAAA;MACA,KAAAH,KAAA,cAAAlE,QAAA;IACA;IACA;IACAsE,YAAA,WAAAA,aAAA;MACA,KAAAJ,KAAA,gBAAAlE,QAAA;IACA;IAEA;IACAuE,qBAAA,WAAAA,sBAAA9D,QAAA;MACA,KAAAyD,KAAA,0BAAAlE,QAAA,CAAAmE,UAAA,EAAA1D,QAAA;IACA;EACA;AACA", "ignoreList": []}]}