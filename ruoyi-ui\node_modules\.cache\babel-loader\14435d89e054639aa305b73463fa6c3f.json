{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_question", "require", "_OptionEditor", "_interopRequireDefault", "name", "components", "OptionEditor", "props", "visible", "type", "Boolean", "default", "questionType", "String", "questionData", "Object", "bankId", "Number", "required", "data", "dialogVisible", "submitting", "form", "questionId", "questionContent", "difficulty", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "rules", "message", "trigger", "computed", "dialogTitle", "typeMap", "typeName", "isEdit", "concat", "isChoiceQuestion", "includes", "watch", "val", "initForm", "$emit", "formQuestionType", "newType", "handleQuestionTypeChange", "handler", "newData", "immediate", "methods", "convertQuestionDataForForm", "resetForm", "formData", "convertQuestionTypeToString", "convertDifficultyToString", "analysis", "JSON", "parse", "e", "console", "error", "Array", "isArray", "map", "option", "optionKey", "key", "optionContent", "content", "correctOptions", "filter", "isCorrect", "difficultyMap", "length", "addOption", "<PERSON><PERSON><PERSON>", "fromCharCode", "push", "removeOption", "index", "splice", "for<PERSON>ach", "idx", "updateCorrectAnswerAfterRemove", "validKeys", "opt", "currentAnswers", "split", "validAnswers", "ans", "join", "handleSingleCorrectChange", "checked", "handleMultipleCorrectChange", "answers", "isMultipleCorrect", "isCorrectOption", "handleSubmit", "_this", "$refs", "validate", "valid", "submitData", "convertFormDataForSubmit", "apiCall", "updateQuestion", "addQuestion", "then", "response", "$message", "success", "catch", "_this2", "handleBeforeClose", "done", "_this3", "warning", "<PERSON><PERSON><PERSON><PERSON>", "$confirm", "confirmButtonText", "cancelButtonText", "handleClose", "handleCancel", "resetFields"], "sources": ["src/views/biz/questionBank/components/QuestionForm.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"dialogTitle\"\n    :visible.sync=\"dialogVisible\"\n    width=\"55%\"\n    :before-close=\"handleBeforeClose\"\n    :close-on-click-modal=\"false\"\n    :close-on-press-escape=\"false\"\n    append-to-body\n  >\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n      <!-- 基本信息 -->\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"题型\" prop=\"questionType\">\n            <el-select v-model=\"form.questionType\" placeholder=\"请选择题型\" style=\"width: 100%;\" :disabled=\"isEdit\">\n              <el-option label=\"单选题\" value=\"single\"></el-option>\n              <el-option label=\"多选题\" value=\"multiple\"></el-option>\n              <el-option label=\"判断题\" value=\"judgment\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"难度\" prop=\"difficulty\">\n            <el-radio-group v-model=\"form.difficulty\">\n              <el-radio-button label=\"简单\">简单</el-radio-button>\n              <el-radio-button label=\"中等\">中等</el-radio-button>\n              <el-radio-button label=\"困难\">困难</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 题干 -->\n      <el-form-item label=\"题干\" prop=\"questionContent\">\n        <option-editor\n          v-model=\"form.questionContent\"\n          :min-height=\"100\"\n          placeholder=\"请输入题干内容\"\n          editor-id=\"question-content\"\n        />\n      </el-form-item>\n\n\n\n      <!-- 选择题选项 -->\n      <div v-if=\"isChoiceQuestion\">\n        <el-form-item label=\"选项设置\" required>\n          <div class=\"options-container\">\n            <div\n              v-for=\"(option, index) in form.options\"\n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <div class=\"option-row\">\n                <div class=\"option-answer\">\n                  <el-checkbox\n                    v-if=\"form.questionType === 'single'\"\n                    :value=\"form.correctAnswer === option.optionKey\"\n                    @change=\"handleSingleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                  <el-checkbox\n                    v-else-if=\"form.questionType === 'multiple'\"\n                    :value=\"isMultipleCorrect(option.optionKey)\"\n                    @change=\"handleMultipleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                </div>\n                <div class=\"option-content\">\n                  <option-editor\n                    :key=\"`option-${option.optionKey}-${form.questionType}`\"\n                    v-model=\"option.optionContent\"\n                    :min-height=\"50\"\n                    :placeholder=\"`请输入选项${option.optionKey}内容`\"\n                    :editor-id=\"`option-${option.optionKey}-${form.questionType}`\"\n                  />\n                </div>\n                <div class=\"option-actions\">\n                  <el-button\n                    v-if=\"form.options.length > 2\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"removeOption(index)\"\n                    style=\"color: #F56C6C;\"\n                    size=\"mini\"\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </div>\n            <el-button\n              v-if=\"form.options.length < 8\"\n              type=\"dashed\"\n              icon=\"el-icon-plus\"\n              @click=\"addOption\"\n              style=\"width: 100%; margin-top: 10px;\"\n            >\n              添加选项\n            </el-button>\n          </div>\n        </el-form-item>\n      </div>\n\n      <!-- 判断题答案 -->\n      <div v-if=\"form.questionType === 'judgment'\">\n        <el-form-item label=\"正确答案\" prop=\"correctAnswer\">\n          <el-radio-group v-model=\"form.correctAnswer\">\n            <el-radio label=\"true\">正确</el-radio>\n            <el-radio label=\"false\">错误</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </div>\n\n      <!-- 题目解析 -->\n      <el-form-item label=\"解析\">\n        <option-editor\n          v-model=\"form.explanation\"\n          :min-height=\"100\"\n          placeholder=\"请输入解析内容\"\n          editor-id=\"question-explanation\"\n        />\n      </el-form-item>\n\n\n    </el-form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleCancel\" :disabled=\"submitting\">取消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n        {{ isEdit ? '更新' : '保存' }}\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { addQuestion, updateQuestion } from '@/api/biz/question'\nimport OptionEditor from './OptionEditor'\n\nexport default {\n  name: \"QuestionForm\",\n  components: {\n    OptionEditor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    questionType: {\n      type: String,\n      default: 'single'\n    },\n    questionData: {\n      type: Object,\n      default: null\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      form: {\n        questionId: null,\n        bankId: null,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      },\n      rules: {\n        questionType: [\n          { required: true, message: '请选择题型', trigger: 'change' }\n        ],\n        questionContent: [\n          { required: true, message: '请输入题目内容', trigger: 'blur' }\n        ],\n        difficulty: [\n          { required: true, message: '请选择难度系数', trigger: 'change' }\n        ],\n        correctAnswer: [\n          { required: true, message: '请设置正确答案', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  computed: {\n    dialogTitle() {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      const typeName = typeMap[this.form.questionType] || '题目'\n      return this.isEdit ? `编辑${typeName}` : `创建${typeName}`\n    },\n    isEdit() {\n      return this.questionData && this.questionData.questionId\n    },\n    isChoiceQuestion() {\n      return ['single', 'multiple'].includes(this.form.questionType)\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    'form.questionType'(newType) {\n      this.handleQuestionTypeChange(newType)\n    },\n    questionData: {\n      handler(newData) {\n        if (newData && this.dialogVisible) {\n          this.initForm()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    // 初始化表单\n    initForm() {\n      if (this.questionData) {\n        this.form = this.convertQuestionDataForForm(this.questionData)\n      } else {\n        this.resetForm()\n        this.form.questionType = this.questionType\n        this.form.bankId = this.bankId\n      }\n      this.handleQuestionTypeChange(this.form.questionType)\n    },\n\n    // 转换后端数据为表单格式\n    convertQuestionDataForForm(questionData) {\n      const formData = {\n        questionId: questionData.questionId,\n        bankId: questionData.bankId,\n        questionType: this.convertQuestionTypeToString(questionData.questionType),\n        difficulty: this.convertDifficultyToString(questionData.difficulty),\n        questionContent: questionData.questionContent,\n        explanation: questionData.explanation || questionData.analysis || '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 处理选择题选项\n      if (questionData.options) {\n        let options = questionData.options\n\n        // 如果options是字符串，需要解析为JSON\n        if (typeof options === 'string') {\n          try {\n            options = JSON.parse(options)\n          } catch (e) {\n            console.error('解析选项JSON失败:', e)\n            options = []\n          }\n        }\n\n        if (Array.isArray(options)) {\n          formData.options = options.map(option => ({\n            optionKey: option.key || option.optionKey,\n            optionContent: option.content || option.optionContent\n          }))\n\n          // 设置正确答案\n          const correctOptions = options\n            .filter(option => option.isCorrect)\n            .map(option => option.key || option.optionKey)\n\n          if (questionData.questionType === 'single' || questionData.questionType === 1) {\n            formData.correctAnswer = correctOptions[0] || ''\n          } else if (questionData.questionType === 'multiple' || questionData.questionType === 2) {\n            formData.correctAnswer = correctOptions\n          }\n        }\n      }\n\n      // 处理判断题答案\n      if ((questionData.questionType === 'judgment' || questionData.questionType === 3) && questionData.correctAnswer) {\n        formData.correctAnswer = questionData.correctAnswer\n      }\n\n      return formData\n    },\n\n    // 题型数字转字符串\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n\n    // 难度数字转字符串\n    convertDifficultyToString(difficulty) {\n      const difficultyMap = {\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || difficulty\n    },\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        questionId: null,\n        bankId: this.bankId,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      }\n    },\n    // 题型变化处理\n    handleQuestionTypeChange(type) {\n      if (type === 'single' || type === 'multiple') {\n        if (this.form.options.length === 0) {\n          this.form.options = [\n            { optionKey: 'A', optionContent: '' },\n            { optionKey: 'B', optionContent: '' },\n            { optionKey: 'C', optionContent: '' },\n            { optionKey: 'D', optionContent: '' }\n          ]\n        }\n        // 设置默认答案\n        if (type === 'single') {\n          this.form.correctAnswer = 'A'\n        } else if (type === 'multiple') {\n          this.form.correctAnswer = 'A,B'\n        }\n      } else if (type === 'judgment') {\n        this.form.options = []\n        this.form.correctAnswer = 'true'  // 判断题默认选择\"正确\"\n      } else {\n        this.form.options = []\n        this.form.correctAnswer = ''\n      }\n    },\n    // 添加选项\n    addOption() {\n      const nextKey = String.fromCharCode(65 + this.form.options.length)\n      this.form.options.push({\n        optionKey: nextKey,\n        optionContent: ''\n      })\n    },\n    // 删除选项\n    removeOption(index) {\n      this.form.options.splice(index, 1)\n      // 重新分配选项键\n      this.form.options.forEach((option, idx) => {\n        option.optionKey = String.fromCharCode(65 + idx)\n      })\n      // 更新正确答案\n      this.updateCorrectAnswerAfterRemove()\n    },\n    // 删除选项后更新正确答案\n    updateCorrectAnswerAfterRemove() {\n      if (this.form.questionType === 'single') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        if (!validKeys.includes(this.form.correctAnswer)) {\n          this.form.correctAnswer = ''\n        }\n      } else if (this.form.questionType === 'multiple') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        const currentAnswers = this.form.correctAnswer.split(',').filter(Boolean)\n        const validAnswers = currentAnswers.filter(ans => validKeys.includes(ans))\n        this.form.correctAnswer = validAnswers.join(',')\n      }\n    },\n    // 单选题正确答案变化\n    handleSingleCorrectChange(optionKey, checked) {\n      this.form.correctAnswer = checked ? optionKey : ''\n    },\n    // 多选题正确答案变化\n    handleMultipleCorrectChange(optionKey, checked) {\n      let answers = this.form.correctAnswer ? this.form.correctAnswer.split(',') : []\n      if (checked) {\n        if (!answers.includes(optionKey)) {\n          answers.push(optionKey)\n        }\n      } else {\n        answers = answers.filter(ans => ans !== optionKey)\n      }\n      this.form.correctAnswer = answers.join(',')\n    },\n    // 判断多选题是否为正确答案\n    isMultipleCorrect(optionKey) {\n      if (!this.form.correctAnswer) return false\n      return this.form.correctAnswer.split(',').includes(optionKey)\n    },\n\n    // 判断选项是否为正确答案\n    isCorrectOption(optionKey) {\n      if (this.form.questionType === 'single') {\n        return this.form.correctAnswer === optionKey\n      } else if (this.form.questionType === 'multiple') {\n        if (!this.form.correctAnswer) return false\n        return this.form.correctAnswer.split(',').includes(optionKey)\n      }\n      return false\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          this.submitting = true\n\n          // 转换表单数据为后端需要的格式\n          const submitData = this.convertFormDataForSubmit()\n\n          const apiCall = this.isEdit ? updateQuestion(submitData) : addQuestion(submitData)\n          apiCall.then(response => {\n            this.submitting = false\n            this.$message.success(this.isEdit ? '更新成功' : '保存成功')\n            this.$emit('success')\n          }).catch(error => {\n            this.submitting = false\n            console.error('保存题目失败', error)\n            this.$message.error('保存题目失败')\n          })\n        }\n      })\n    },\n\n    // 转换表单数据为提交格式\n    convertFormDataForSubmit() {\n      const data = {\n        questionId: this.form.questionId,\n        bankId: this.form.bankId,\n        questionType: this.form.questionType,\n        difficulty: this.form.difficulty,\n        questionContent: this.form.questionContent,\n        explanation: this.form.explanation\n      }\n\n      // 处理选择题选项\n      if (this.isChoiceQuestion) {\n        data.options = this.form.options.map(option => ({\n          key: option.optionKey,\n          content: option.optionContent,\n          isCorrect: this.isCorrectOption(option.optionKey)\n        }))\n      }\n\n      // 处理判断题答案\n      if (this.form.questionType === 'judgment') {\n        data.correctAnswer = this.form.correctAnswer\n        // 判断题也需要设置options格式，以便后端统一处理\n        data.options = [\n          {\n            key: 'true',\n            content: '正确',\n            isCorrect: this.form.correctAnswer === 'true'\n          },\n          {\n            key: 'false',\n            content: '错误',\n            isCorrect: this.form.correctAnswer === 'false'\n          }\n        ]\n      }\n\n      return data\n    },\n    // 关闭前确认\n    handleBeforeClose(done) {\n      // 如果正在提交，不允许关闭\n      if (this.submitting) {\n        this.$message.warning('正在保存中，请稍候...')\n        return\n      }\n\n      // 检查表单是否有内容\n      const hasContent = this.form.questionContent ||\n                        (this.form.options && this.form.options.length > 0) ||\n                        this.form.explanation\n\n      if (hasContent) {\n        this.$confirm('确认关闭？未保存的内容将会丢失', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.handleClose()\n          done()\n        }).catch(() => {\n          // 用户取消关闭\n        })\n      } else {\n        this.handleClose()\n        done()\n      }\n    },\n\n    // 取消按钮点击\n    handleCancel() {\n      this.handleClose()\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.form.resetFields()\n      this.resetForm()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.editor-container {\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n}\n\n.editor {\n  min-height: 200px;\n}\n\n.options-container {\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n}\n\n.option-item {\n  margin-bottom: 20px;\n}\n\n.option-item:last-child {\n  margin-bottom: 0;\n}\n\n.option-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n}\n\n.option-answer {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-actions {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n  text-align: center;\n}\n\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA4IA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,YAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,YAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;IACAK,MAAA;MACAP,IAAA,GAAAI,MAAA,EAAAI,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MACAC,IAAA;QACAC,UAAA;QACAP,MAAA;QACAJ,YAAA;QACAY,eAAA;QACAC,UAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;MACA;MACAC,KAAA;QACAjB,YAAA,GACA;UAAAM,QAAA;UAAAY,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,eAAA,GACA;UAAAN,QAAA;UAAAY,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,UAAA,GACA;UAAAP,QAAA;UAAAY,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,aAAA,GACA;UAAAT,QAAA;UAAAY,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;MACA;MACA,IAAAC,QAAA,GAAAD,OAAA,MAAAZ,IAAA,CAAAV,YAAA;MACA,YAAAwB,MAAA,kBAAAC,MAAA,CAAAF,QAAA,mBAAAE,MAAA,CAAAF,QAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,YAAAtB,YAAA,SAAAA,YAAA,CAAAS,UAAA;IACA;IACAe,gBAAA,WAAAA,iBAAA;MACA,8BAAAC,QAAA,MAAAjB,IAAA,CAAAV,YAAA;IACA;EACA;EACA4B,KAAA;IACAhC,OAAA,WAAAA,QAAAiC,GAAA;MACA,KAAArB,aAAA,GAAAqB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,QAAA;MACA;IACA;IACAtB,aAAA,WAAAA,cAAAqB,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;IACA,8BAAAG,iBAAAC,OAAA;MACA,KAAAC,wBAAA,CAAAD,OAAA;IACA;IACA/B,YAAA;MACAiC,OAAA,WAAAA,QAAAC,OAAA;QACA,IAAAA,OAAA,SAAA5B,aAAA;UACA,KAAAsB,QAAA;QACA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA;IACA;IACAR,QAAA,WAAAA,SAAA;MACA,SAAA5B,YAAA;QACA,KAAAQ,IAAA,QAAA6B,0BAAA,MAAArC,YAAA;MACA;QACA,KAAAsC,SAAA;QACA,KAAA9B,IAAA,CAAAV,YAAA,QAAAA,YAAA;QACA,KAAAU,IAAA,CAAAN,MAAA,QAAAA,MAAA;MACA;MACA,KAAA8B,wBAAA,MAAAxB,IAAA,CAAAV,YAAA;IACA;IAEA;IACAuC,0BAAA,WAAAA,2BAAArC,YAAA;MACA,IAAAuC,QAAA;QACA9B,UAAA,EAAAT,YAAA,CAAAS,UAAA;QACAP,MAAA,EAAAF,YAAA,CAAAE,MAAA;QACAJ,YAAA,OAAA0C,2BAAA,CAAAxC,YAAA,CAAAF,YAAA;QACAa,UAAA,OAAA8B,yBAAA,CAAAzC,YAAA,CAAAW,UAAA;QACAD,eAAA,EAAAV,YAAA,CAAAU,eAAA;QACAI,WAAA,EAAAd,YAAA,CAAAc,WAAA,IAAAd,YAAA,CAAA0C,QAAA;QACA9B,OAAA;QACAC,aAAA;MACA;;MAEA;MACA,IAAAb,YAAA,CAAAY,OAAA;QACA,IAAAA,OAAA,GAAAZ,YAAA,CAAAY,OAAA;;QAEA;QACA,WAAAA,OAAA;UACA;YACAA,OAAA,GAAA+B,IAAA,CAAAC,KAAA,CAAAhC,OAAA;UACA,SAAAiC,CAAA;YACAC,OAAA,CAAAC,KAAA,gBAAAF,CAAA;YACAjC,OAAA;UACA;QACA;QAEA,IAAAoC,KAAA,CAAAC,OAAA,CAAArC,OAAA;UACA2B,QAAA,CAAA3B,OAAA,GAAAA,OAAA,CAAAsC,GAAA,WAAAC,MAAA;YAAA;cACAC,SAAA,EAAAD,MAAA,CAAAE,GAAA,IAAAF,MAAA,CAAAC,SAAA;cACAE,aAAA,EAAAH,MAAA,CAAAI,OAAA,IAAAJ,MAAA,CAAAG;YACA;UAAA;;UAEA;UACA,IAAAE,cAAA,GAAA5C,OAAA,CACA6C,MAAA,WAAAN,MAAA;YAAA,OAAAA,MAAA,CAAAO,SAAA;UAAA,GACAR,GAAA,WAAAC,MAAA;YAAA,OAAAA,MAAA,CAAAE,GAAA,IAAAF,MAAA,CAAAC,SAAA;UAAA;UAEA,IAAApD,YAAA,CAAAF,YAAA,iBAAAE,YAAA,CAAAF,YAAA;YACAyC,QAAA,CAAA1B,aAAA,GAAA2C,cAAA;UACA,WAAAxD,YAAA,CAAAF,YAAA,mBAAAE,YAAA,CAAAF,YAAA;YACAyC,QAAA,CAAA1B,aAAA,GAAA2C,cAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAxD,YAAA,CAAAF,YAAA,mBAAAE,YAAA,CAAAF,YAAA,WAAAE,YAAA,CAAAa,aAAA;QACA0B,QAAA,CAAA1B,aAAA,GAAAb,YAAA,CAAAa,aAAA;MACA;MAEA,OAAA0B,QAAA;IACA;IAEA;IACAC,2BAAA,WAAAA,4BAAA7C,IAAA;MACA,IAAAyB,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAzB,IAAA,KAAAA,IAAA;IACA;IAEA;IACA8C,yBAAA,WAAAA,0BAAA9B,UAAA;MACA,IAAAgD,aAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,aAAA,CAAAhD,UAAA,KAAAA,UAAA;IACA;IAEA;IACA2B,SAAA,WAAAA,UAAA;MACA,KAAA9B,IAAA;QACAC,UAAA;QACAP,MAAA,OAAAA,MAAA;QACAJ,YAAA;QACAY,eAAA;QACAC,UAAA;QACAC,OAAA;QACAC,aAAA;QACAC,WAAA;MACA;IACA;IACA;IACAkB,wBAAA,WAAAA,yBAAArC,IAAA;MACA,IAAAA,IAAA,iBAAAA,IAAA;QACA,SAAAa,IAAA,CAAAI,OAAA,CAAAgD,MAAA;UACA,KAAApD,IAAA,CAAAI,OAAA,IACA;YAAAwC,SAAA;YAAAE,aAAA;UAAA,GACA;YAAAF,SAAA;YAAAE,aAAA;UAAA,GACA;YAAAF,SAAA;YAAAE,aAAA;UAAA,GACA;YAAAF,SAAA;YAAAE,aAAA;UAAA,EACA;QACA;QACA;QACA,IAAA3D,IAAA;UACA,KAAAa,IAAA,CAAAK,aAAA;QACA,WAAAlB,IAAA;UACA,KAAAa,IAAA,CAAAK,aAAA;QACA;MACA,WAAAlB,IAAA;QACA,KAAAa,IAAA,CAAAI,OAAA;QACA,KAAAJ,IAAA,CAAAK,aAAA;MACA;QACA,KAAAL,IAAA,CAAAI,OAAA;QACA,KAAAJ,IAAA,CAAAK,aAAA;MACA;IACA;IACA;IACAgD,SAAA,WAAAA,UAAA;MACA,IAAAC,OAAA,GAAA/D,MAAA,CAAAgE,YAAA,WAAAvD,IAAA,CAAAI,OAAA,CAAAgD,MAAA;MACA,KAAApD,IAAA,CAAAI,OAAA,CAAAoD,IAAA;QACAZ,SAAA,EAAAU,OAAA;QACAR,aAAA;MACA;IACA;IACA;IACAW,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAA1D,IAAA,CAAAI,OAAA,CAAAuD,MAAA,CAAAD,KAAA;MACA;MACA,KAAA1D,IAAA,CAAAI,OAAA,CAAAwD,OAAA,WAAAjB,MAAA,EAAAkB,GAAA;QACAlB,MAAA,CAAAC,SAAA,GAAArD,MAAA,CAAAgE,YAAA,MAAAM,GAAA;MACA;MACA;MACA,KAAAC,8BAAA;IACA;IACA;IACAA,8BAAA,WAAAA,+BAAA;MACA,SAAA9D,IAAA,CAAAV,YAAA;QACA,IAAAyE,SAAA,QAAA/D,IAAA,CAAAI,OAAA,CAAAsC,GAAA,WAAAsB,GAAA;UAAA,OAAAA,GAAA,CAAApB,SAAA;QAAA;QACA,KAAAmB,SAAA,CAAA9C,QAAA,MAAAjB,IAAA,CAAAK,aAAA;UACA,KAAAL,IAAA,CAAAK,aAAA;QACA;MACA,gBAAAL,IAAA,CAAAV,YAAA;QACA,IAAAyE,UAAA,QAAA/D,IAAA,CAAAI,OAAA,CAAAsC,GAAA,WAAAsB,GAAA;UAAA,OAAAA,GAAA,CAAApB,SAAA;QAAA;QACA,IAAAqB,cAAA,QAAAjE,IAAA,CAAAK,aAAA,CAAA6D,KAAA,MAAAjB,MAAA,CAAA7D,OAAA;QACA,IAAA+E,YAAA,GAAAF,cAAA,CAAAhB,MAAA,WAAAmB,GAAA;UAAA,OAAAL,UAAA,CAAA9C,QAAA,CAAAmD,GAAA;QAAA;QACA,KAAApE,IAAA,CAAAK,aAAA,GAAA8D,YAAA,CAAAE,IAAA;MACA;IACA;IACA;IACAC,yBAAA,WAAAA,0BAAA1B,SAAA,EAAA2B,OAAA;MACA,KAAAvE,IAAA,CAAAK,aAAA,GAAAkE,OAAA,GAAA3B,SAAA;IACA;IACA;IACA4B,2BAAA,WAAAA,4BAAA5B,SAAA,EAAA2B,OAAA;MACA,IAAAE,OAAA,QAAAzE,IAAA,CAAAK,aAAA,QAAAL,IAAA,CAAAK,aAAA,CAAA6D,KAAA;MACA,IAAAK,OAAA;QACA,KAAAE,OAAA,CAAAxD,QAAA,CAAA2B,SAAA;UACA6B,OAAA,CAAAjB,IAAA,CAAAZ,SAAA;QACA;MACA;QACA6B,OAAA,GAAAA,OAAA,CAAAxB,MAAA,WAAAmB,GAAA;UAAA,OAAAA,GAAA,KAAAxB,SAAA;QAAA;MACA;MACA,KAAA5C,IAAA,CAAAK,aAAA,GAAAoE,OAAA,CAAAJ,IAAA;IACA;IACA;IACAK,iBAAA,WAAAA,kBAAA9B,SAAA;MACA,UAAA5C,IAAA,CAAAK,aAAA;MACA,YAAAL,IAAA,CAAAK,aAAA,CAAA6D,KAAA,MAAAjD,QAAA,CAAA2B,SAAA;IACA;IAEA;IACA+B,eAAA,WAAAA,gBAAA/B,SAAA;MACA,SAAA5C,IAAA,CAAAV,YAAA;QACA,YAAAU,IAAA,CAAAK,aAAA,KAAAuC,SAAA;MACA,gBAAA5C,IAAA,CAAAV,YAAA;QACA,UAAAU,IAAA,CAAAK,aAAA;QACA,YAAAL,IAAA,CAAAK,aAAA,CAAA6D,KAAA,MAAAjD,QAAA,CAAA2B,SAAA;MACA;MACA;IACA;IAEA;IACAgC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAA9E,IAAA,CAAA+E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAA9E,UAAA;;UAEA;UACA,IAAAkF,UAAA,GAAAJ,KAAA,CAAAK,wBAAA;UAEA,IAAAC,OAAA,GAAAN,KAAA,CAAA/D,MAAA,OAAAsE,wBAAA,EAAAH,UAAA,QAAAI,qBAAA,EAAAJ,UAAA;UACAE,OAAA,CAAAG,IAAA,WAAAC,QAAA;YACAV,KAAA,CAAA9E,UAAA;YACA8E,KAAA,CAAAW,QAAA,CAAAC,OAAA,CAAAZ,KAAA,CAAA/D,MAAA;YACA+D,KAAA,CAAAxD,KAAA;UACA,GAAAqE,KAAA,WAAAnD,KAAA;YACAsC,KAAA,CAAA9E,UAAA;YACAuC,OAAA,CAAAC,KAAA,WAAAA,KAAA;YACAsC,KAAA,CAAAW,QAAA,CAAAjD,KAAA;UACA;QACA;MACA;IACA;IAEA;IACA2C,wBAAA,WAAAA,yBAAA;MAAA,IAAAS,MAAA;MACA,IAAA9F,IAAA;QACAI,UAAA,OAAAD,IAAA,CAAAC,UAAA;QACAP,MAAA,OAAAM,IAAA,CAAAN,MAAA;QACAJ,YAAA,OAAAU,IAAA,CAAAV,YAAA;QACAa,UAAA,OAAAH,IAAA,CAAAG,UAAA;QACAD,eAAA,OAAAF,IAAA,CAAAE,eAAA;QACAI,WAAA,OAAAN,IAAA,CAAAM;MACA;;MAEA;MACA,SAAAU,gBAAA;QACAnB,IAAA,CAAAO,OAAA,QAAAJ,IAAA,CAAAI,OAAA,CAAAsC,GAAA,WAAAC,MAAA;UAAA;YACAE,GAAA,EAAAF,MAAA,CAAAC,SAAA;YACAG,OAAA,EAAAJ,MAAA,CAAAG,aAAA;YACAI,SAAA,EAAAyC,MAAA,CAAAhB,eAAA,CAAAhC,MAAA,CAAAC,SAAA;UACA;QAAA;MACA;;MAEA;MACA,SAAA5C,IAAA,CAAAV,YAAA;QACAO,IAAA,CAAAQ,aAAA,QAAAL,IAAA,CAAAK,aAAA;QACA;QACAR,IAAA,CAAAO,OAAA,IACA;UACAyC,GAAA;UACAE,OAAA;UACAG,SAAA,OAAAlD,IAAA,CAAAK,aAAA;QACA,GACA;UACAwC,GAAA;UACAE,OAAA;UACAG,SAAA,OAAAlD,IAAA,CAAAK,aAAA;QACA,EACA;MACA;MAEA,OAAAR,IAAA;IACA;IACA;IACA+F,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA/F,UAAA;QACA,KAAAyF,QAAA,CAAAO,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,UAAA,QAAAhG,IAAA,CAAAE,eAAA,IACA,KAAAF,IAAA,CAAAI,OAAA,SAAAJ,IAAA,CAAAI,OAAA,CAAAgD,MAAA,QACA,KAAApD,IAAA,CAAAM,WAAA;MAEA,IAAA0F,UAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAhH,IAAA;QACA,GAAAmG,IAAA;UACAQ,MAAA,CAAAM,WAAA;UACAP,IAAA;QACA,GAAAH,KAAA;UACA;QAAA,CACA;MACA;QACA,KAAAU,WAAA;QACAP,IAAA;MACA;IACA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAAD,WAAA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAtG,aAAA;MACA,KAAAgF,KAAA,CAAA9E,IAAA,CAAAsG,WAAA;MACA,KAAAxE,SAAA;IACA;EACA;AACA", "ignoreList": []}]}