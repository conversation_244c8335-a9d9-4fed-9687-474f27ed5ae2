{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_question", "_request", "name", "props", "visible", "type", "Boolean", "default", "bankId", "String", "Number", "required", "defaultMode", "data", "dialogVisible", "currentStep", "importMode", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "uploadedFile", "parsing", "importing", "parsedData", "parseErrors", "importResult", "successCount", "failCount", "errors", "watch", "val", "resetImport", "$emit", "methods", "downloadTemplate", "fileName", "concat", "getQuestionTypeName", "download", "questionType", "typeMap", "getQuestionTypeColor", "colorMap", "nextStep", "prevStep", "beforeFileUpload", "file", "isLt10M", "size", "isExcel", "$message", "error", "isDocument", "toLowerCase", "endsWith", "handleFileSuccess", "response", "code", "url", "success", "msg", "handleFileError", "parseFile", "_this", "parseData", "parseImportFile", "then", "questions", "catch", "console", "parseDocumentContent", "_this2", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "fileContent", "parseResult", "_t", "w", "_context", "n", "p", "readFileContent", "v", "parseQuestionContent", "a", "fileUrl", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "responseType", "onload", "status", "responseText", "Error", "onerror", "send", "content", "_this3", "sections", "splitByQuestionType", "for<PERSON>ach", "section", "sectionIndex", "parsedQuestions", "parseSectionQuestions", "push", "apply", "_toConsumableArray2", "message", "typeRegex", "lastIndex", "match", "currentType", "exec", "substring", "index", "trim", "length", "_this4", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "question", "parseQuestionBlock", "blocks", "numberRegex", "filter", "questionIndex", "lines", "split", "map", "line", "firstLine", "numberMatch", "questionContent", "currentLineIndex", "isOptionLine", "difficulty", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptions", "nextIndex", "parseQuestionMeta", "test", "startIndex", "currentIndex", "optionMatch", "optionKey", "toUpperCase", "optionContent", "i", "answerMatch", "parseAnswer", "explanationMatch", "difficultyMatch", "extractAnswerFromContent", "answerText", "includes", "replace", "bracketPatterns", "_i", "_bracketPatterns", "pattern", "matches", "matchAll", "answer", "typeText", "importData", "_this5", "batchImportQuestions", "handleComplete", "handleClose"], "sources": ["src/views/biz/questionBank/components/BatchImport.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量导入题目\"\n    :visible.sync=\"dialogVisible\"\n    width=\"70%\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"import-container\">\n      <!-- 导入步骤 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"下载模板\"></el-step>\n        <el-step title=\"上传文件\"></el-step>\n        <el-step title=\"数据预览\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 选择导入方式 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"import-mode-section\">\n          <h3>第一步：选择导入方式</h3>\n\n          <!-- 导入方式选择 -->\n          <el-radio-group v-model=\"importMode\" style=\"margin-bottom: 30px;\">\n            <el-radio label=\"excel\">Excel模板导入</el-radio>\n            <el-radio label=\"document\">文档内容导入</el-radio>\n          </el-radio-group>\n\n          <!-- Excel模板导入 -->\n          <div v-if=\"importMode === 'excel'\" class=\"template-section\">\n            <h4>Excel模板导入</h4>\n            <p>请先下载题目导入模板，按照模板格式填写题目数据</p>\n\n            <div class=\"template-buttons\">\n              <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate('all')\">\n                下载题目导入模板\n              </el-button>\n            </div>\n\n            <div class=\"template-tips\">\n              <h4>填写说明：</h4>\n              <ul>\n                <li>模板支持单选题、多选题、判断题混合导入</li>\n                <li>题目内容：必填，支持HTML格式</li>\n                <li>选项内容：选择题必填，选项A-H，每个选项单独一列</li>\n                <li>正确答案：必填，单选题填写A/B/C等，多选题用逗号分隔如A,C，判断题填写true/false</li>\n                <li>难度系数：选填，可填写\"简单\"、\"中等\"、\"困难\"</li>\n                <li>题目解析：选填，支持HTML格式</li>\n                <li>请确保Excel文件编码为UTF-8，避免中文乱码</li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- 文档内容导入 -->\n          <div v-if=\"importMode === 'document'\" class=\"document-section\">\n            <h4>文档内容导入</h4>\n            <p>支持上传包含题目内容的文档文件，系统将自动解析题目信息</p>\n\n            <div class=\"document-format-tips\">\n              <h4>格式要求：</h4>\n              <div class=\"format-rules\">\n                <div class=\"rule-item\">\n                  <h5>题型标注（必填）：</h5>\n                  <p><code>[单选题]</code> <code>[多选题]</code> <code>[判断题]</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>题号规则（必填）：</h5>\n                  <p>题目前必须有题号，如：<code>1.</code> <code>2：</code> <code>3．</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>选项格式（必填）：</h5>\n                  <p><code>A.选项内容</code> <code>B：选项内容</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>答案标注（必填）：</h5>\n                  <p><code>答案：A</code> 或题干内 <code>【A】</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>解析和难度（可选）：</h5>\n                  <p><code>解析：解析内容</code> <code>难度：中等</code></p>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"document-example\">\n              <h4>示例格式：</h4>\n              <pre class=\"example-text\">\n[单选题]\n1.（ ）是我国最早的诗歌总集。\nA.《左传》\nB.《离骚》\nC.《坛经》\nD.《诗经》\n答案：D\n解析：诗经是我国最早的诗歌总集。\n难度：中等\n\n[判断题]\n2.元杂剧的四大悲剧包括郑光祖的《赵氏孤儿》。\n答案：错误\n解析：《赵氏孤儿》实为纪君祥所作。\n              </pre>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 上传文件 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>第二步：上传题目文件</h3>\n          <p v-if=\"importMode === 'excel'\">请选择填写好的Excel文件进行上传</p>\n          <p v-if=\"importMode === 'document'\">请选择包含题目内容的文档文件进行上传</p>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\"importMode === 'excel' ? '.xlsx,.xls' : '.txt,.doc,.docx'\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将文件拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'excel'\">\n                  支持 .xlsx、.xls 格式文件，文件大小不超过10MB\n                </p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'document'\">\n                  支持 .txt、.doc、.docx 格式文件，文件大小不超过10MB\n                </p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" @click=\"parseFile\" :disabled=\"!uploadedFile\" :loading=\"parsing\">\n            解析文件\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 数据预览 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"preview-section\">\n          <h3>第三步：数据预览与确认</h3>\n          <p>共解析到 {{ parsedData.length }} 道题目，请确认数据无误后点击导入</p>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <el-alert\n              title=\"数据解析错误\"\n              type=\"error\"\n              :closable=\"false\"\n              show-icon\n              style=\"margin-bottom: 15px;\"\n            />\n            <div class=\"error-list\">\n              <div v-for=\"(error, index) in parseErrors\" :key=\"index\" class=\"error-item\">\n                第{{ error.row }}行：{{ error.message }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-table\">\n            <el-table :data=\"parsedData.slice(0, 10)\" border style=\"width: 100%\">\n              <el-table-column prop=\"questionType\" label=\"题型\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getQuestionTypeColor(scope.row.questionType)\" size=\"mini\">\n                    {{ getQuestionTypeName(scope.row.questionType) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题目内容\" min-width=\"200\" show-overflow-tooltip />\n              <el-table-column prop=\"correctAnswer\" label=\"正确答案\" width=\"100\" />\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"80\" />\n            </el-table>\n            <div v-if=\"parsedData.length > 10\" class=\"table-tip\">\n              仅显示前10条数据，共{{ parsedData.length }}条\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"importData\" \n            :disabled=\"parseErrors.length > 0 || parsedData.length === 0\"\n            :loading=\"importing\"\n          >\n            确认导入\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 60px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          <div class=\"result-stats\">\n            <p>成功导入 <span class=\"success-count\">{{ importResult.successCount }}</span> 道题目</p>\n            <p v-if=\"importResult.failCount > 0\">\n              失败 <span class=\"fail-count\">{{ importResult.failCount }}</span> 道题目\n            </p>\n          </div>\n          \n          <div v-if=\"importResult.errors.length > 0\" class=\"import-errors\">\n            <el-collapse>\n              <el-collapse-item title=\"查看失败详情\" name=\"errors\">\n                <div v-for=\"(error, index) in importResult.errors\" :key=\"index\" class=\"error-detail\">\n                  第{{ error.row }}行：{{ error.message }}\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { downloadTemplate, parseImportFile, batchImportQuestions } from '@/api/biz/question'\nimport { download } from '@/utils/request'\n\nexport default {\n  name: \"BatchImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    defaultMode: {\n      type: String,\n      default: 'excel' // excel 或 document\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      importMode: 'excel', // 导入模式：excel 或 document\n      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      },\n      uploadedFile: null,\n      parsing: false,\n      importing: false,\n      parsedData: [],\n      parseErrors: [],\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    defaultMode(val) {\n      if (val) {\n        this.importMode = val\n      }\n    }\n  },\n  methods: {\n    // 下载模板\n    downloadTemplate(type) {\n      const fileName = type === 'all' ? '题目导入模板.xlsx' : `${this.getQuestionTypeName(type)}导入模板.xlsx`\n      download('/biz/question/downloadTemplate', { questionType: type }, fileName)\n    },\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 下一步\n    nextStep() {\n      this.currentStep++\n    },\n    // 上一步\n    prevStep() {\n      this.currentStep--\n    },\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (this.importMode === 'excel') {\n        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                       file.type === 'application/vnd.ms-excel'\n        if (!isExcel) {\n          this.$message.error('只能上传Excel文件!')\n          return false\n        }\n      } else if (this.importMode === 'document') {\n        const isDocument = file.type === 'text/plain' ||\n                          file.type === 'application/msword' ||\n                          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                          file.name.toLowerCase().endsWith('.txt') ||\n                          file.name.toLowerCase().endsWith('.doc') ||\n                          file.name.toLowerCase().endsWith('.docx')\n        if (!isDocument) {\n          this.$message.error('只能上传文档文件(.txt, .doc, .docx)!')\n          return false\n        }\n      }\n\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n      return true\n    },\n    // 文件上传成功\n    handleFileSuccess(response, file) {\n      if (response.code === 200) {\n        this.uploadedFile = {\n          name: file.name,\n          url: response.url,\n          fileName: response.fileName\n        }\n        this.$message.success('文件上传成功')\n      } else {\n        this.$message.error(response.msg || '文件上传失败')\n      }\n    },\n    // 文件上传失败\n    handleFileError() {\n      this.$message.error('文件上传失败')\n    },\n    // 解析文件\n    parseFile() {\n      this.parsing = true\n\n      if (this.importMode === 'excel') {\n        // Excel文件解析\n        const parseData = {\n          fileName: this.uploadedFile.fileName,\n          bankId: this.bankId\n        }\n        parseImportFile(parseData).then(response => {\n          this.parsing = false\n          this.parsedData = response.data.questions || []\n          this.parseErrors = response.data.errors || []\n          this.nextStep()\n        }).catch(error => {\n          this.parsing = false\n          console.error('解析文件失败', error)\n          this.$message.error('解析文件失败')\n        })\n      } else if (this.importMode === 'document') {\n        // 文档内容解析\n        this.parseDocumentContent()\n      }\n    },\n\n    // 解析文档内容\n    async parseDocumentContent() {\n      try {\n        // 读取文件内容\n        const fileContent = await this.readFileContent(this.uploadedFile.url)\n\n        // 解析题目\n        const parseResult = this.parseQuestionContent(fileContent)\n\n        this.parsing = false\n        this.parsedData = parseResult.questions\n        this.parseErrors = parseResult.errors\n        this.nextStep()\n      } catch (error) {\n        this.parsing = false\n        console.error('解析文档内容失败', error)\n        this.$message.error('解析文档内容失败')\n      }\n    },\n\n    // 读取文件内容\n    readFileContent(fileUrl) {\n      return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest()\n        xhr.open('GET', process.env.VUE_APP_BASE_API + fileUrl, true)\n        xhr.setRequestHeader('Authorization', 'Bearer ' + getToken())\n        xhr.responseType = 'text'\n\n        xhr.onload = function() {\n          if (xhr.status === 200) {\n            resolve(xhr.responseText)\n          } else {\n            reject(new Error('读取文件失败'))\n          }\n        }\n\n        xhr.onerror = function() {\n          reject(new Error('读取文件失败'))\n        }\n\n        xhr.send()\n      })\n    },\n\n    // 解析题目内容\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      try {\n        // 按题型分割内容\n        const sections = this.splitByQuestionType(content)\n\n        sections.forEach((section, sectionIndex) => {\n          try {\n            const parsedQuestions = this.parseSectionQuestions(section)\n            questions.push(...parsedQuestions)\n          } catch (error) {\n            errors.push(`第${sectionIndex + 1}个题型区域解析失败: ${error.message}`)\n          }\n        })\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType, questionIndex) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题号和题干\n      const firstLine = lines[0]\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．]\\s*(.*)/)\n      if (!numberMatch) {\n        throw new Error('题号格式不正确')\n      }\n\n      let questionContent = numberMatch[2]\n      let currentLineIndex = 1\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '中等',\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难)/)\n        if (difficultyMatch) {\n          question.difficulty = difficultyMatch[1]\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 解析答案\n    parseAnswer(answerText, questionType) {\n      if (questionType === 'judgment') {\n        // 判断题答案处理\n        if (answerText.includes('正确') || answerText.includes('对') || answerText.toLowerCase().includes('true')) {\n          return 'true'\n        } else if (answerText.includes('错误') || answerText.includes('错') || answerText.includes('假') || answerText.toLowerCase().includes('false')) {\n          return 'false'\n        }\n        return answerText.trim()\n      } else {\n        // 选择题答案处理\n        return answerText.replace(/[,，\\s]/g, '').toUpperCase()\n      }\n    },\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 导入数据\n    importData() {\n      this.importing = true\n      const importData = {\n        bankId: this.bankId,\n        questions: this.parsedData\n      }\n      batchImportQuestions(importData).then(response => {\n        this.importing = false\n        this.importResult = response.data\n        this.nextStep()\n      }).catch(error => {\n        this.importing = false\n        console.error('导入数据失败', error)\n        this.$message.error('导入数据失败')\n      })\n    },\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.importMode = this.defaultMode || 'excel'\n      this.uploadedFile = null\n      this.parsedData = []\n      this.parseErrors = []\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.import-container {\n  padding: 20px 0;\n}\n\n.step-content {\n  min-height: 400px;\n}\n\n.template-section h3,\n.upload-section h3,\n.preview-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-buttons {\n  margin: 20px 0;\n  display: flex;\n  gap: 15px;\n}\n\n.template-tips {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.template-tips h4 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.template-tips li {\n  margin-bottom: 5px;\n  color: #666;\n}\n\n/* 文档导入样式 */\n.import-mode-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.document-section {\n  margin-top: 20px;\n}\n\n.document-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.document-format-tips {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n}\n\n.document-format-tips h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.format-rules {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.rule-item {\n  background: #fff;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 3px solid #409eff;\n}\n\n.rule-item h5 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.rule-item p {\n  margin: 0;\n  color: #666;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.rule-item code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  color: #e74c3c;\n}\n\n.document-example {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.document-example h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.example-text {\n  background: #fff;\n  padding: 15px;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  background: #fafafa;\n  transition: border-color 0.3s;\n}\n\n.upload-area:hover {\n  border-color: #409EFF;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 0;\n  color: #666;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.uploaded-file {\n  margin-top: 15px;\n}\n\n.error-section {\n  margin-bottom: 20px;\n}\n\n.error-list {\n  max-height: 150px;\n  overflow-y: auto;\n  background: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.error-item {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.table-tip {\n  text-align: center;\n  color: #999;\n  font-size: 12px;\n  margin-top: 10px;\n}\n\n.result-section {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-stats {\n  margin: 20px 0;\n}\n\n.success-count {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.fail-count {\n  color: #f56c6c;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.import-errors {\n  margin-top: 20px;\n  text-align: left;\n}\n\n.error-detail {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.step-actions {\n  text-align: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyPA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,WAAA;MACAP,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,YAAA;MACAC,OAAA;MACAC,SAAA;MACAC,UAAA;MACAC,WAAA;MACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;EACA;EACAC,KAAA;IACA7B,OAAA,WAAAA,QAAA8B,GAAA;MACA,KAAApB,aAAA,GAAAoB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,WAAA;MACA;IACA;IACArB,aAAA,WAAAA,cAAAoB,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;IACAtB,WAAA,WAAAA,YAAAsB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAlB,UAAA,GAAAkB,GAAA;MACA;IACA;EACA;EACAG,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAjC,IAAA;MACA,IAAAkC,QAAA,GAAAlC,IAAA,gCAAAmC,MAAA,MAAAC,mBAAA,CAAApC,IAAA;MACA,IAAAqC,iBAAA;QAAAC,YAAA,EAAAtC;MAAA,GAAAkC,QAAA;IACA;IACA;IACAE,mBAAA,WAAAA,oBAAApC,IAAA;MACA,IAAAuC,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAvC,IAAA;IACA;IACA;IACAwC,oBAAA,WAAAA,qBAAAxC,IAAA;MACA,IAAAyC,QAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAzC,IAAA;IACA;IACA;IACA0C,QAAA,WAAAA,SAAA;MACA,KAAAhC,WAAA;IACA;IACA;IACAiC,QAAA,WAAAA,SAAA;MACA,KAAAjC,WAAA;IACA;IACA;IACAkC,gBAAA,WAAAA,iBAAAC,IAAA;MACA,IAAAC,OAAA,GAAAD,IAAA,CAAAE,IAAA;MAEA,SAAApC,UAAA;QACA,IAAAqC,OAAA,GAAAH,IAAA,CAAA7C,IAAA,4EACA6C,IAAA,CAAA7C,IAAA;QACA,KAAAgD,OAAA;UACA,KAAAC,QAAA,CAAAC,KAAA;UACA;QACA;MACA,gBAAAvC,UAAA;QACA,IAAAwC,UAAA,GAAAN,IAAA,CAAA7C,IAAA,qBACA6C,IAAA,CAAA7C,IAAA,6BACA6C,IAAA,CAAA7C,IAAA,kFACA6C,IAAA,CAAAhD,IAAA,CAAAuD,WAAA,GAAAC,QAAA,YACAR,IAAA,CAAAhD,IAAA,CAAAuD,WAAA,GAAAC,QAAA,YACAR,IAAA,CAAAhD,IAAA,CAAAuD,WAAA,GAAAC,QAAA;QACA,KAAAF,UAAA;UACA,KAAAF,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MAEA,KAAAJ,OAAA;QACA,KAAAG,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IACA;IACAI,iBAAA,WAAAA,kBAAAC,QAAA,EAAAV,IAAA;MACA,IAAAU,QAAA,CAAAC,IAAA;QACA,KAAArC,YAAA;UACAtB,IAAA,EAAAgD,IAAA,CAAAhD,IAAA;UACA4D,GAAA,EAAAF,QAAA,CAAAE,GAAA;UACAvB,QAAA,EAAAqB,QAAA,CAAArB;QACA;QACA,KAAAe,QAAA,CAAAS,OAAA;MACA;QACA,KAAAT,QAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAI,GAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAX,QAAA,CAAAC,KAAA;IACA;IACA;IACAW,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,KAAA1C,OAAA;MAEA,SAAAT,UAAA;QACA;QACA,IAAAoD,SAAA;UACA7B,QAAA,OAAAf,YAAA,CAAAe,QAAA;UACA/B,MAAA,OAAAA;QACA;QACA,IAAA6D,yBAAA,EAAAD,SAAA,EAAAE,IAAA,WAAAV,QAAA;UACAO,KAAA,CAAA1C,OAAA;UACA0C,KAAA,CAAAxC,UAAA,GAAAiC,QAAA,CAAA/C,IAAA,CAAA0D,SAAA;UACAJ,KAAA,CAAAvC,WAAA,GAAAgC,QAAA,CAAA/C,IAAA,CAAAmB,MAAA;UACAmC,KAAA,CAAApB,QAAA;QACA,GAAAyB,KAAA,WAAAjB,KAAA;UACAY,KAAA,CAAA1C,OAAA;UACAgD,OAAA,CAAAlB,KAAA,WAAAA,KAAA;UACAY,KAAA,CAAAb,QAAA,CAAAC,KAAA;QACA;MACA,gBAAAvC,UAAA;QACA;QACA,KAAA0D,oBAAA;MACA;IACA;IAEA;IACAA,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAArE,OAAA,mBAAAsE,aAAA,CAAAtE,OAAA,IAAAuE,CAAA,UAAAC,QAAA;QAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAtE,OAAA,IAAA4E,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGAV,MAAA,CAAAY,eAAA,CAAAZ,MAAA,CAAAnD,YAAA,CAAAsC,GAAA;YAAA;cAAAkB,WAAA,GAAAI,QAAA,CAAAI,CAAA;cAEA;cACAP,WAAA,GAAAN,MAAA,CAAAc,oBAAA,CAAAT,WAAA;cAEAL,MAAA,CAAAlD,OAAA;cACAkD,MAAA,CAAAhD,UAAA,GAAAsD,WAAA,CAAAV,SAAA;cACAI,MAAA,CAAA/C,WAAA,GAAAqD,WAAA,CAAAjD,MAAA;cACA2C,MAAA,CAAA5B,QAAA;cAAAqC,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAb,MAAA,CAAAlD,OAAA;cACAgD,OAAA,CAAAlB,KAAA,aAAA2B,EAAA;cACAP,MAAA,CAAArB,QAAA,CAAAC,KAAA;YAAA;cAAA,OAAA6B,QAAA,CAAAM,CAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IAEA;IAEA;IACAQ,eAAA,WAAAA,gBAAAI,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA,IAAAC,GAAA,OAAAC,cAAA;QACAD,GAAA,CAAAE,IAAA,QAAA/E,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAAuE,OAAA;QACAI,GAAA,CAAAG,gBAAA,kCAAA3E,cAAA;QACAwE,GAAA,CAAAI,YAAA;QAEAJ,GAAA,CAAAK,MAAA;UACA,IAAAL,GAAA,CAAAM,MAAA;YACAR,OAAA,CAAAE,GAAA,CAAAO,YAAA;UACA;YACAR,MAAA,KAAAS,KAAA;UACA;QACA;QAEAR,GAAA,CAAAS,OAAA;UACAV,MAAA,KAAAS,KAAA;QACA;QAEAR,GAAA,CAAAU,IAAA;MACA;IACA;IAEA;IACAhB,oBAAA,WAAAA,qBAAAiB,OAAA;MAAA,IAAAC,MAAA;MACA,IAAApC,SAAA;MACA,IAAAvC,MAAA;MAEA;QACA;QACA,IAAA4E,QAAA,QAAAC,mBAAA,CAAAH,OAAA;QAEAE,QAAA,CAAAE,OAAA,WAAAC,OAAA,EAAAC,YAAA;UACA;YACA,IAAAC,eAAA,GAAAN,MAAA,CAAAO,qBAAA,CAAAH,OAAA;YACAxC,SAAA,CAAA4C,IAAA,CAAAC,KAAA,CAAA7C,SAAA,MAAA8C,mBAAA,CAAA9G,OAAA,EAAA0G,eAAA;UACA,SAAA1D,KAAA;YACAvB,MAAA,CAAAmF,IAAA,UAAA3E,MAAA,CAAAwE,YAAA,kEAAAxE,MAAA,CAAAe,KAAA,CAAA+D,OAAA;UACA;QACA;MAEA,SAAA/D,KAAA;QACAvB,MAAA,CAAAmF,IAAA,0CAAA3E,MAAA,CAAAe,KAAA,CAAA+D,OAAA;MACA;MAEA;QAAA/C,SAAA,EAAAA,SAAA;QAAAvC,MAAA,EAAAA;MAAA;IACA;IAEA;IACA6E,mBAAA,WAAAA,oBAAAH,OAAA;MACA,IAAAE,QAAA;MACA,IAAAW,SAAA;MAEA,IAAAC,SAAA;MACA,IAAAC,KAAA;MACA,IAAAC,WAAA;MAEA,QAAAD,KAAA,GAAAF,SAAA,CAAAI,IAAA,CAAAjB,OAAA;QACA,IAAAgB,WAAA;UACA;UACAd,QAAA,CAAAO,IAAA;YACA9G,IAAA,EAAAqH,WAAA;YACAhB,OAAA,EAAAA,OAAA,CAAAkB,SAAA,CAAAJ,SAAA,EAAAC,KAAA,CAAAI,KAAA,EAAAC,IAAA;UACA;QACA;QACAJ,WAAA,GAAAD,KAAA;QACAD,SAAA,GAAAC,KAAA,CAAAI,KAAA,GAAAJ,KAAA,IAAAM,MAAA;MACA;;MAEA;MACA,IAAAL,WAAA;QACAd,QAAA,CAAAO,IAAA;UACA9G,IAAA,EAAAqH,WAAA;UACAhB,OAAA,EAAAA,OAAA,CAAAkB,SAAA,CAAAJ,SAAA,EAAAM,IAAA;QACA;MACA;MAEA,OAAAlB,QAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAH,OAAA;MAAA,IAAAiB,MAAA;MACA,IAAAzD,SAAA;MACA,IAAA5B,YAAA,QAAAsF,mBAAA,CAAAlB,OAAA,CAAA1G,IAAA;;MAEA;MACA,IAAA6H,cAAA,QAAAC,qBAAA,CAAApB,OAAA,CAAAL,OAAA;MAEAwB,cAAA,CAAApB,OAAA,WAAAsB,KAAA,EAAAP,KAAA;QACA;UACA,IAAAQ,QAAA,GAAAL,MAAA,CAAAM,kBAAA,CAAAF,KAAA,EAAAzF,YAAA,EAAAkF,KAAA;UACA,IAAAQ,QAAA;YACA9D,SAAA,CAAA4C,IAAA,CAAAkB,QAAA;UACA;QACA,SAAA9E,KAAA;UACA,UAAAgD,KAAA,UAAA/D,MAAA,CAAAqF,KAAA,0CAAArF,MAAA,CAAAe,KAAA,CAAA+D,OAAA;QACA;MACA;MAEA,OAAA/C,SAAA;IACA;IAEA;IACA4D,qBAAA,WAAAA,sBAAAzB,OAAA;MACA,IAAA6B,MAAA;MACA,IAAAC,WAAA;MAEA,IAAAhB,SAAA;MACA,IAAAC,KAAA;MAEA,QAAAA,KAAA,GAAAe,WAAA,CAAAb,IAAA,CAAAjB,OAAA;QACA,IAAAc,SAAA;UACA;UACAe,MAAA,CAAApB,IAAA,CAAAT,OAAA,CAAAkB,SAAA,CAAAJ,SAAA,EAAAC,KAAA,CAAAI,KAAA,EAAAC,IAAA;QACA;QACAN,SAAA,GAAAC,KAAA,CAAAI,KAAA;MACA;;MAEA;MACA,IAAAL,SAAA,GAAAd,OAAA,CAAAqB,MAAA;QACAQ,MAAA,CAAApB,IAAA,CAAAT,OAAA,CAAAkB,SAAA,CAAAJ,SAAA,EAAAM,IAAA;MACA;MAEA,OAAAS,MAAA,CAAAE,MAAA,WAAAL,KAAA;QAAA,OAAAA,KAAA,CAAAL,MAAA;MAAA;IACA;IAEA;IACAO,kBAAA,WAAAA,mBAAAF,KAAA,EAAAzF,YAAA,EAAA+F,aAAA;MACA,IAAAC,KAAA,GAAAP,KAAA,CAAAQ,KAAA,OAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,IAAA;MAAA,GAAAW,MAAA,WAAAK,IAAA;QAAA,OAAAA,IAAA,CAAAf,MAAA;MAAA;MAEA,IAAAY,KAAA,CAAAZ,MAAA;QACA,UAAAxB,KAAA;MACA;;MAEA;MACA,IAAAwC,SAAA,GAAAJ,KAAA;MACA,IAAAK,WAAA,GAAAD,SAAA,CAAAtB,KAAA;MACA,KAAAuB,WAAA;QACA,UAAAzC,KAAA;MACA;MAEA,IAAA0C,eAAA,GAAAD,WAAA;MACA,IAAAE,gBAAA;;MAEA;MACA,OAAAA,gBAAA,GAAAP,KAAA,CAAAZ,MAAA;QACA,IAAAe,IAAA,GAAAH,KAAA,CAAAO,gBAAA;QACA,SAAAC,YAAA,CAAAL,IAAA;UACA;QACA;QACAG,eAAA,WAAAH,IAAA;QACAI,gBAAA;MACA;MAEA,IAAAb,QAAA;QACA1F,YAAA,EAAAA,YAAA;QACAsG,eAAA,EAAAA,eAAA,CAAAnB,IAAA;QACAsB,UAAA;QACAC,WAAA;QACAC,OAAA;QACAC,aAAA;MACA;;MAEA;MACA,IAAA5G,YAAA;QACA,IAAA6G,YAAA,QAAAC,YAAA,CAAAd,KAAA,EAAAO,gBAAA;QACAb,QAAA,CAAAiB,OAAA,GAAAE,YAAA,CAAAF,OAAA;QACAJ,gBAAA,GAAAM,YAAA,CAAAE,SAAA;MACA;;MAEA;MACA,KAAAC,iBAAA,CAAAhB,KAAA,EAAAO,gBAAA,EAAAb,QAAA;MAEA,OAAAA,QAAA;IACA;IAEA;IACAc,YAAA,WAAAA,aAAAL,IAAA;MACA,4BAAAc,IAAA,CAAAd,IAAA;IACA;IAEA;IACAW,YAAA,WAAAA,aAAAd,KAAA,EAAAkB,UAAA;MACA,IAAAP,OAAA;MACA,IAAAQ,YAAA,GAAAD,UAAA;MAEA,OAAAC,YAAA,GAAAnB,KAAA,CAAAZ,MAAA;QACA,IAAAe,IAAA,GAAAH,KAAA,CAAAmB,YAAA;QACA,IAAAC,WAAA,GAAAjB,IAAA,CAAArB,KAAA;QAEA,KAAAsC,WAAA;UACA;QACA;QAEAT,OAAA,CAAAnC,IAAA;UACA6C,SAAA,EAAAD,WAAA,IAAAE,WAAA;UACAC,aAAA,EAAAH,WAAA,IAAAjC,IAAA;QACA;QAEAgC,YAAA;MACA;MAEA;QAAAR,OAAA,EAAAA,OAAA;QAAAI,SAAA,EAAAI;MAAA;IACA;IAEA;IACAH,iBAAA,WAAAA,kBAAAhB,KAAA,EAAAkB,UAAA,EAAAxB,QAAA;MACA,SAAA8B,CAAA,GAAAN,UAAA,EAAAM,CAAA,GAAAxB,KAAA,CAAAZ,MAAA,EAAAoC,CAAA;QACA,IAAArB,IAAA,GAAAH,KAAA,CAAAwB,CAAA;;QAEA;QACA,IAAAC,WAAA,GAAAtB,IAAA,CAAArB,KAAA;QACA,IAAA2C,WAAA;UACA/B,QAAA,CAAAkB,aAAA,QAAAc,WAAA,CAAAD,WAAA,KAAA/B,QAAA,CAAA1F,YAAA;UACA;QACA;;QAEA;QACA,IAAA2H,gBAAA,GAAAxB,IAAA,CAAArB,KAAA;QACA,IAAA6C,gBAAA;UACAjC,QAAA,CAAAgB,WAAA,GAAAiB,gBAAA,IAAAxC,IAAA;UACA;QACA;;QAEA;QACA,IAAAyC,eAAA,GAAAzB,IAAA,CAAArB,KAAA;QACA,IAAA8C,eAAA;UACAlC,QAAA,CAAAe,UAAA,GAAAmB,eAAA;UACA;QACA;MACA;;MAEA;MACA,KAAAlC,QAAA,CAAAkB,aAAA;QACAlB,QAAA,CAAAkB,aAAA,QAAAiB,wBAAA,CAAAnC,QAAA,CAAAY,eAAA,EAAAZ,QAAA,CAAA1F,YAAA;MACA;IACA;IAEA;IACA0H,WAAA,WAAAA,YAAAI,UAAA,EAAA9H,YAAA;MACA,IAAAA,YAAA;QACA;QACA,IAAA8H,UAAA,CAAAC,QAAA,UAAAD,UAAA,CAAAC,QAAA,SAAAD,UAAA,CAAAhH,WAAA,GAAAiH,QAAA;UACA;QACA,WAAAD,UAAA,CAAAC,QAAA,UAAAD,UAAA,CAAAC,QAAA,SAAAD,UAAA,CAAAC,QAAA,SAAAD,UAAA,CAAAhH,WAAA,GAAAiH,QAAA;UACA;QACA;QACA,OAAAD,UAAA,CAAA3C,IAAA;MACA;QACA;QACA,OAAA2C,UAAA,CAAAE,OAAA,gBAAAV,WAAA;MACA;IACA;IAEA;IACAO,wBAAA,WAAAA,yBAAA9D,OAAA,EAAA/D,YAAA;MACA;MACA,IAAAiI,eAAA,IACA,cACA,iBACA,cACA,eACA;MAEA,SAAAC,EAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,EAAA,GAAAC,gBAAA,CAAA/C,MAAA,EAAA8C,EAAA;QAAA,IAAAE,OAAA,GAAAD,gBAAA,CAAAD,EAAA;QACA,IAAAG,OAAA,OAAA3D,mBAAA,CAAA9G,OAAA,EAAAmG,OAAA,CAAAuE,QAAA,CAAAF,OAAA;QACA,IAAAC,OAAA,CAAAjD,MAAA;UACA,IAAAmD,MAAA,GAAAF,OAAA,CAAAA,OAAA,CAAAjD,MAAA;UACA,YAAAsC,WAAA,CAAAa,MAAA,EAAAvI,YAAA;QACA;MACA;MAEA;IACA;IAEA;IACAsF,mBAAA,WAAAA,oBAAAkD,QAAA;MACA,IAAAvI,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAuI,QAAA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA3J,SAAA;MACA,IAAA0J,UAAA;QACA5K,MAAA,OAAAA,MAAA;QACA+D,SAAA,OAAA5C;MACA;MACA,IAAA2J,8BAAA,EAAAF,UAAA,EAAA9G,IAAA,WAAAV,QAAA;QACAyH,MAAA,CAAA3J,SAAA;QACA2J,MAAA,CAAAxJ,YAAA,GAAA+B,QAAA,CAAA/C,IAAA;QACAwK,MAAA,CAAAtI,QAAA;MACA,GAAAyB,KAAA,WAAAjB,KAAA;QACA8H,MAAA,CAAA3J,SAAA;QACA+C,OAAA,CAAAlB,KAAA,WAAAA,KAAA;QACA8H,MAAA,CAAA/H,QAAA,CAAAC,KAAA;MACA;IACA;IACA;IACAgI,cAAA,WAAAA,eAAA;MACA,KAAAnJ,KAAA;MACA,KAAAoJ,WAAA;IACA;IACA;IACArJ,WAAA,WAAAA,YAAA;MACA,KAAApB,WAAA;MACA,KAAAC,UAAA,QAAAJ,WAAA;MACA,KAAAY,YAAA;MACA,KAAAG,UAAA;MACA,KAAAC,WAAA;MACA,KAAAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;IACA;IACAwJ,WAAA,WAAAA,YAAA;MACA,KAAA1K,aAAA;IACA;EACA;AACA", "ignoreList": []}]}