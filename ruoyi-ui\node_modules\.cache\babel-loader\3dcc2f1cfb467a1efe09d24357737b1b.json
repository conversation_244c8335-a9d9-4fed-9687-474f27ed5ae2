{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_questionBank", "_Editor", "_interopRequireDefault", "name", "components", "Editor", "props", "visible", "type", "Boolean", "default", "bankId", "String", "Number", "required", "bankName", "data", "dialogVisible", "currentStep", "activeTab", "uploadedFile", "uploading", "uploadProgress", "<PERSON><PERSON><PERSON><PERSON>", "previewHtml", "previewQuestionCount", "previewLoading", "parseErrors", "importing", "importOptions", "allowDuplicate", "importResult", "successCount", "failCount", "errors", "exampleContent", "watch", "val", "resetImport", "$emit", "methods", "customUpload", "option", "_this", "file", "uploadAndParse", "then", "response", "code", "edit<PERSON><PERSON><PERSON><PERSON>", "$message", "success", "onSuccess", "error", "msg", "onError", "Error", "catch", "console", "beforeFileUpload", "isDocx", "toLowerCase", "endsWith", "isLt10M", "size", "handleFileSuccess", "handleFileError", "err", "onContentChange", "_this2", "html", "clearTimeout", "previewTimer", "setTimeout", "autoPreview", "trim", "previewContent", "saveContent", "_this3", "content", "saveEditedContent", "_this4", "questionCount", "confirmImport", "_this5", "importFromEditor", "nextStep", "prevStep", "handleComplete", "handleClose", "handleTabClick", "tab", "copyExampleToEditor", "_this6", "loadDemoContent"], "sources": ["src/views/biz/questionBank/components/RichTextImport.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <div class=\"upload-options\">\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n              <el-tab-pane label=\"上传文档\" name=\"upload\">\n                <div class=\"upload-content\">\n                  <h3>上传Word文档</h3>\n                  <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n                  <div class=\"demo-section\">\n                    <el-button size=\"small\" type=\"text\" @click=\"loadDemoContent\">\n                      <i class=\"el-icon-magic-stick\"></i> 加载演示内容\n                    </el-button>\n                    <span class=\"demo-tip\">或者点击加载演示内容快速体验功能</span>\n                  </div>\n\n                  <el-upload\n                    ref=\"fileUpload\"\n                    :http-request=\"customUpload\"\n                    :on-success=\"handleFileSuccess\"\n                    :on-error=\"handleFileError\"\n                    :before-upload=\"beforeFileUpload\"\n                    :show-file-list=\"false\"\n                    accept=\".docx\"\n                    drag\n                  >\n                    <div class=\"upload-area\">\n                      <i class=\"el-icon-upload\"></i>\n                      <div class=\"upload-text\">\n                        <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                        <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n                      </div>\n                    </div>\n                  </el-upload>\n\n                  <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n                    <el-alert\n                      :title=\"`已上传文件：${uploadedFile.name}`\"\n                      type=\"success\"\n                      :closable=\"false\"\n                      show-icon\n                    />\n                  </div>\n\n                  <div v-if=\"uploading\" class=\"uploading-status\">\n                    <el-progress :percentage=\"50\" :show-text=\"false\"></el-progress>\n                    <p>正在上传并解析文档...</p>\n                  </div>\n                </div>\n              </el-tab-pane>\n\n              <el-tab-pane label=\"输入规范与范例\" name=\"rules\">\n                <div class=\"rules-content\">\n                  <h3>题目输入格式规范</h3>\n\n                  <div class=\"format-rules\">\n                    <div class=\"rule-section\">\n                      <h4>基本格式要求</h4>\n                      <ul>\n                        <li><strong>题目编号：</strong>使用\"数字、\"格式，如：1、2、3、</li>\n                        <li><strong>题型标识：</strong>[单选题]、[多选题]、[判断题]</li>\n                        <li><strong>选项格式：</strong>A、B、C、D、（大写字母+顿号）</li>\n                        <li><strong>答案格式：</strong>答案：A 或 答案：A,B,C</li>\n                        <li><strong>解析格式：</strong>解析：解析内容（可选）</li>\n                      </ul>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>标准范例</h4>\n                      <div class=\"example-container\">\n                        <div class=\"example-header\">\n                          <span>完整题目示例</span>\n                          <el-button size=\"mini\" type=\"primary\" @click=\"copyExampleToEditor\">\n                            <i class=\"el-icon-copy-document\"></i> 复制到编辑器\n                          </el-button>\n                        </div>\n                        <pre class=\"example-content\">{{ exampleContent }}</pre>\n                      </div>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>注意事项</h4>\n                      <el-alert\n                        title=\"格式要求\"\n                        type=\"warning\"\n                        :closable=\"false\"\n                        show-icon\n                      >\n                        <ul>\n                          <li>严格按照格式要求编写，格式错误会导致解析失败</li>\n                          <li>题目编号必须连续，不能跳号</li>\n                          <li>选项字母必须大写，使用中文顿号</li>\n                          <li>多选题答案用逗号分隔，如：A,B,C</li>\n                          <li>判断题答案只能是\"正确\"或\"错误\"</li>\n                        </ul>\n                      </el-alert>\n                    </div>\n                  </div>\n                </div>\n              </el-tab-pane>\n            </el-tabs>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <editor\n                  v-model=\"editContent\"\n                  :min-height=\"350\"\n                  @on-change=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\nimport Editor from '@/components/Editor'\n\nexport default {\n  name: \"RichTextImport\",\n  components: {\n    Editor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n\n      // 标签页相关\n      activeTab: 'upload',\n\n      // 文件上传相关\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      },\n\n      // 示例内容\n      exampleContent: `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\nE、打印机\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器和打印机是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。\n\n4、[单选题]以下哪个不是操作系统？\nA、Windows\nB、Linux\nC、Office\nD、macOS\n答案：C\n解析：Office是办公软件套件，不是操作系统。Windows、Linux和macOS都是操作系统。\n\n5、[判断题]计算机病毒可以通过网络传播。\n答案：正确\n解析：计算机病毒可以通过多种途径传播，包括网络、移动存储设备、电子邮件等。`\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 自定义上传方法\n    customUpload(option) {\n      const file = option.file\n      this.uploading = true\n      this.uploadProgress = 0\n\n      uploadAndParse(file, this.bankId).then(response => {\n        this.uploading = false\n        if (response.code === 200) {\n          this.uploadedFile = {\n            name: file.name,\n            response: response\n          }\n          this.editContent = response.data.editableContent || ''\n          this.parseErrors = response.data.errors || []\n          this.$message.success('文档上传成功')\n          option.onSuccess(response, file)\n        } else {\n          this.$message.error(response.msg || '文档上传失败')\n          option.onError(new Error(response.msg || '上传失败'), file)\n        }\n      }).catch(error => {\n        this.uploading = false\n        console.error('文件上传失败', error)\n        this.$message.error('文件上传失败')\n        option.onError(error, file)\n      })\n    },\n\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功（由customUpload调用）\n    handleFileSuccess(response, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 文件上传失败（由customUpload调用）\n    handleFileError(err, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 编辑器内容变化\n    onContentChange(data) {\n      // data包含 { html, text, quill }\n      if (data && data.html) {\n        this.editContent = data.html\n      }\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent  // 现在直接传递文本内容，不需要HTML转换\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.activeTab = 'upload'\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    },\n\n    // 标签页切换\n    handleTabClick(tab) {\n      this.activeTab = tab.name\n    },\n\n    // 复制示例到编辑器\n    copyExampleToEditor() {\n      this.editContent = this.exampleContent\n      this.uploadedFile = {\n        name: '示例内容.docx',\n        response: { code: 200 }\n      }\n      this.parseErrors = []\n      this.activeTab = 'upload'\n      this.$message.success('示例内容已复制到编辑器，可以直接进入下一步体验功能')\n\n      // 自动预览示例内容\n      setTimeout(() => {\n        this.autoPreview()\n      }, 500)\n    },\n\n    // 加载演示内容\n    loadDemoContent() {\n      this.copyExampleToEditor()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.rich-text-import-dialog {\n  .el-dialog {\n    margin-top: 5vh !important;\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n\n.import-container {\n  min-height: 600px;\n}\n\n.step-content {\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n}\n\n.step-actions {\n  margin-top: auto;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n  text-align: right;\n}\n\n/* 文件上传样式 */\n.upload-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.upload-section p {\n  margin-bottom: 30px;\n  color: #666;\n  text-align: center;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 10px 0;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n.uploaded-file {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.uploading-status {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n  text-align: center;\n}\n\n.demo-section {\n  margin: 20px 0;\n  text-align: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px dashed #d9ecff;\n}\n\n.demo-tip {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #666;\n}\n\n/* 标签页样式 */\n.upload-options {\n  width: 100%;\n}\n\n.upload-content,\n.rules-content {\n  padding: 20px 0;\n}\n\n.rules-content h3 {\n  margin-bottom: 20px;\n  color: #333;\n  text-align: center;\n}\n\n.format-rules {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.rule-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.rule-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.rule-section ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin-bottom: 8px;\n  line-height: 1.6;\n  color: #555;\n}\n\n.rule-section li strong {\n  color: #333;\n}\n\n.example-container {\n  background: #fff;\n  border: 1px solid #d9ecff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.example-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #ecf5ff;\n  border-bottom: 1px solid #d9ecff;\n}\n\n.example-header span {\n  font-weight: 500;\n  color: #409eff;\n}\n\n.example-content {\n  margin: 0;\n  padding: 16px;\n  background: #fafafa;\n  font-family: 'Courier New', Consolas, monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n/* 规则提示样式 */\n.rule-section .el-alert {\n  margin-top: 15px;\n}\n\n.rule-section .el-alert ul {\n  margin: 10px 0 0 0;\n  padding-left: 20px;\n}\n\n.rule-section .el-alert li {\n  margin-bottom: 5px;\n  font-size: 13px;\n}\n\n/* 编辑器样式 */\n.editor-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.editor-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.editor-preview-layout {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  min-height: 400px;\n}\n\n.editor-panel,\n.preview-panel {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.panel-header {\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.panel-tip {\n  font-size: 12px;\n  color: #666;\n}\n\n.editor-wrapper,\n.preview-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-editor {\n  flex: 1;\n  min-height: 350px;\n}\n\n.preview-wrapper {\n  position: relative;\n}\n\n.preview-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n}\n\n.preview-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n  max-height: 350px;\n}\n\n.preview-empty {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 确认导入样式 */\n.confirm-section {\n  flex: 1;\n}\n\n.confirm-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.import-summary {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.error-section {\n  margin-bottom: 30px;\n}\n\n.error-section h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n.import-options {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n}\n\n.import-options h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.final-preview {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.final-preview h4 {\n  margin: 0;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  color: #333;\n}\n\n.final-preview .preview-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n/* 完成页面样式 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-section h3 {\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.import-result {\n  margin-bottom: 30px;\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors {\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n/* 预览内容样式 */\n.preview-content .questions-preview {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.preview-content .question-item {\n  margin-bottom: 20px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.preview-content .question-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.preview-content .question-number {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.preview-content .question-type {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.preview-content .question-difficulty {\n  color: #666;\n  font-size: 12px;\n}\n\n.preview-content .question-content {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #333;\n}\n\n.preview-content .question-options {\n  margin-bottom: 15px;\n}\n\n.preview-content .option-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.preview-content .option-key {\n  font-weight: 500;\n  color: #409eff;\n  min-width: 20px;\n}\n\n.preview-content .option-content {\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-content .question-answer {\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background: #e8f5e8;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.preview-content .question-analysis {\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .editor-preview-layout {\n    flex-direction: column;\n  }\n\n  .editor-panel,\n  .preview-panel {\n    min-height: 300px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAkRA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IACAC,MAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,QAAA;MACAP,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MAEA;MACAC,SAAA;MAEA;MACAC,YAAA;MACAC,SAAA;MACAC,cAAA;MAEA;MACAC,WAAA;MAEA;MACAC,WAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,WAAA;MAEA;MACAC,SAAA;MACAC,aAAA;QACAC,cAAA;MACA;MACAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;MAEA;MACAC,cAAA;IAgCA;EACA;EACAC,KAAA;IACA7B,OAAA,WAAAA,QAAA8B,GAAA;MACA,KAAApB,aAAA,GAAAoB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,WAAA;MACA;IACA;IACArB,aAAA,WAAAA,cAAAoB,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;EACA;EACAG,OAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,GAAAF,MAAA,CAAAE,IAAA;MACA,KAAAvB,SAAA;MACA,KAAAC,cAAA;MAEA,IAAAuB,4BAAA,EAAAD,IAAA,OAAAjC,MAAA,EAAAmC,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAtB,SAAA;QACA,IAAA0B,QAAA,CAAAC,IAAA;UACAL,KAAA,CAAAvB,YAAA;YACAjB,IAAA,EAAAyC,IAAA,CAAAzC,IAAA;YACA4C,QAAA,EAAAA;UACA;UACAJ,KAAA,CAAApB,WAAA,GAAAwB,QAAA,CAAA/B,IAAA,CAAAiC,eAAA;UACAN,KAAA,CAAAhB,WAAA,GAAAoB,QAAA,CAAA/B,IAAA,CAAAkB,MAAA;UACAS,KAAA,CAAAO,QAAA,CAAAC,OAAA;UACAT,MAAA,CAAAU,SAAA,CAAAL,QAAA,EAAAH,IAAA;QACA;UACAD,KAAA,CAAAO,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;UACAZ,MAAA,CAAAa,OAAA,KAAAC,KAAA,CAAAT,QAAA,CAAAO,GAAA,aAAAV,IAAA;QACA;MACA,GAAAa,KAAA,WAAAJ,KAAA;QACAV,KAAA,CAAAtB,SAAA;QACAqC,OAAA,CAAAL,KAAA,WAAAA,KAAA;QACAV,KAAA,CAAAO,QAAA,CAAAG,KAAA;QACAX,MAAA,CAAAa,OAAA,CAAAF,KAAA,EAAAT,IAAA;MACA;IACA;IAEA;IACAe,gBAAA,WAAAA,iBAAAf,IAAA;MACA,IAAAgB,MAAA,GAAAhB,IAAA,CAAAzC,IAAA,CAAA0D,WAAA,GAAAC,QAAA;MACA,IAAAC,OAAA,GAAAnB,IAAA,CAAAoB,IAAA;MAEA,KAAAJ,MAAA;QACA,KAAAV,QAAA,CAAAG,KAAA;QACA;MACA;MACA,KAAAU,OAAA;QACA,KAAAb,QAAA,CAAAG,KAAA;QACA;MACA;MAEA,KAAAhC,SAAA;MACA,KAAAC,cAAA;MACA;IACA;IAEA;IACA2C,iBAAA,WAAAA,kBAAAlB,QAAA,EAAAH,IAAA;MACA;IAAA,CACA;IAEA;IACAsB,eAAA,WAAAA,gBAAAC,GAAA,EAAAvB,IAAA;MACA;IAAA,CACA;IAEA;IACAwB,eAAA,WAAAA,gBAAApD,IAAA;MAAA,IAAAqD,MAAA;MACA;MACA,IAAArD,IAAA,IAAAA,IAAA,CAAAsD,IAAA;QACA,KAAA/C,WAAA,GAAAP,IAAA,CAAAsD,IAAA;MACA;MACA;MACAC,YAAA,MAAAC,YAAA;MACA,KAAAA,YAAA,GAAAC,UAAA;QACAJ,MAAA,CAAAK,WAAA;MACA;IACA;IAEA;IACAA,WAAA,WAAAA,YAAA;MACA,UAAAnD,WAAA,SAAAA,WAAA,CAAAoD,IAAA;QACA,KAAAnD,WAAA;QACA,KAAAC,oBAAA;QACA;MACA;MACA,KAAAmD,cAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAA9D,IAAA;QACAL,MAAA,OAAAA,MAAA;QACAoE,OAAA,OAAAxD;MACA;MACA,IAAAyD,+BAAA,EAAAhE,IAAA,EAAA8B,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA8B,MAAA,CAAA5B,QAAA,CAAAC,OAAA;QACA;UACA2B,MAAA,CAAA5B,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACAK,OAAA,CAAAL,KAAA,WAAAA,KAAA;QACAyB,MAAA,CAAA5B,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAuB,cAAA,WAAAA,eAAA;MAAA,IAAAK,MAAA;MACA,UAAA1D,WAAA,SAAAA,WAAA,CAAAoD,IAAA;QACA;MACA;MAEA,KAAAjD,cAAA;MACA,IAAAV,IAAA;QACA+D,OAAA,OAAAxD,WAAA;MACA;MAEA,IAAAqD,4BAAA,EAAA5D,IAAA,EAAA8B,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAAvD,cAAA;QACA,IAAAqB,QAAA,CAAAC,IAAA;UACAiC,MAAA,CAAAzD,WAAA,GAAAuB,QAAA,CAAA/B,IAAA,CAAAQ,WAAA;UACAyD,MAAA,CAAAxD,oBAAA,GAAAsB,QAAA,CAAA/B,IAAA,CAAAkE,aAAA;UACAD,MAAA,CAAAtD,WAAA,GAAAoB,QAAA,CAAA/B,IAAA,CAAAkB,MAAA;QACA;UACA+C,MAAA,CAAA/B,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACA4B,MAAA,CAAAvD,cAAA;QACAgC,OAAA,CAAAL,KAAA,SAAAA,KAAA;QACA4B,MAAA,CAAA/B,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACA8B,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAxD,SAAA;MACA,IAAAZ,IAAA;QACAL,MAAA,OAAAA,MAAA;QACAoE,OAAA,OAAAxD,WAAA;QACAO,cAAA,OAAAD,aAAA,CAAAC;MACA;MAEA,IAAAuD,8BAAA,EAAArE,IAAA,EAAA8B,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAAxD,SAAA;QACA,IAAAmB,QAAA,CAAAC,IAAA;UACAoC,MAAA,CAAArD,YAAA,GAAAgB,QAAA,CAAA/B,IAAA;UACAoE,MAAA,CAAAE,QAAA;UACAF,MAAA,CAAAlC,QAAA,CAAAC,OAAA;QACA;UACAiC,MAAA,CAAAlC,QAAA,CAAAG,KAAA,CAAAN,QAAA,CAAAO,GAAA;QACA;MACA,GAAAG,KAAA,WAAAJ,KAAA;QACA+B,MAAA,CAAAxD,SAAA;QACA8B,OAAA,CAAAL,KAAA,SAAAA,KAAA;QACA+B,MAAA,CAAAlC,QAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAiC,QAAA,WAAAA,SAAA;MACA,SAAApE,WAAA;QACA,KAAAA,WAAA;QACA,SAAAA,WAAA;UACA;UACA,KAAA0D,cAAA;QACA;MACA;IACA;IAEA;IACAW,QAAA,WAAAA,SAAA;MACA,SAAArE,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEA;IACAsE,cAAA,WAAAA,eAAA;MACA,KAAAjD,KAAA;MACA,KAAAkD,WAAA;IACA;IAEA;IACAnD,WAAA,WAAAA,YAAA;MACA,KAAApB,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,YAAA;MACA,KAAAC,SAAA;MACA,KAAAC,cAAA;MACA,KAAAC,WAAA;MACA,KAAAC,WAAA;MACA,KAAAC,oBAAA;MACA,KAAAE,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,aAAA;QACAC,cAAA;MACA;MACA,KAAAC,YAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAuD,WAAA,WAAAA,YAAA;MACA,KAAAxE,aAAA;IACA;IAEA;IACAyE,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAxE,SAAA,GAAAwE,GAAA,CAAAxF,IAAA;IACA;IAEA;IACAyF,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAtE,WAAA,QAAAY,cAAA;MACA,KAAAf,YAAA;QACAjB,IAAA;QACA4C,QAAA;UAAAC,IAAA;QAAA;MACA;MACA,KAAArB,WAAA;MACA,KAAAR,SAAA;MACA,KAAA+B,QAAA,CAAAC,OAAA;;MAEA;MACAsB,UAAA;QACAoB,MAAA,CAAAnB,WAAA;MACA;IACA;IAEA;IACAoB,eAAA,WAAAA,gBAAA;MACA,KAAAF,mBAAA;IACA;EACA;AACA", "ignoreList": []}]}