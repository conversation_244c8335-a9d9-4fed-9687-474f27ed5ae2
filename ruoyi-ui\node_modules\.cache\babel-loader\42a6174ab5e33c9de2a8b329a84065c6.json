{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quill", "_interopRequireDefault", "require", "_auth", "currentActiveEditor", "_default2", "exports", "default", "name", "props", "value", "type", "String", "minHeight", "Number", "placeholder", "editorId", "Math", "random", "toString", "substr", "data", "<PERSON><PERSON><PERSON>", "currentValue", "isFocused", "showToolbar", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "options", "theme", "bounds", "document", "body", "debug", "modules", "toolbar", "list", "indent", "align", "size", "color", "background", "readOnly", "computed", "styles", "style", "concat", "watch", "handler", "val", "clipboard", "dangerouslyPasteHTML", "immediate", "mounted", "init", "addEventListener", "handleGlobalClick", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "_this", "editor", "$refs", "hideToolbar", "getModule", "add<PERSON><PERSON><PERSON>", "imgHandler", "on", "delta", "<PERSON><PERSON><PERSON><PERSON>", "source", "html", "children", "innerHTML", "text", "getText", "quill", "$emit", "range", "oldRange", "handleEditorClick", "setActiveEditor", "focus", "showToolbarElement", "event", "$el", "contains", "target", "_this2", "$nextTick", "querySelector", "display", "_this3", "upload", "$children", "input", "click", "handleBeforeUpload", "file", "$message", "error", "handleUploadSuccess", "res", "code", "length", "getSelection", "index", "insertEmbed", "fileName", "setSelection", "handleUploadError"], "sources": ["src/views/biz/questionBank/components/OptionEditor.vue"], "sourcesContent": ["<template>\n  <div class=\"option-editor\">\n    <!-- 隐藏的上传组件 -->\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n    >\n    </el-upload>\n    <div\n      class=\"editor-container\"\n      :class=\"{ 'focused': isFocused, 'show-toolbar': showToolbar }\"\n      @click=\"handleEditorClick\"\n    >\n      <div ref=\"editor\" class=\"editor\" :style=\"styles\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport Quill from \"quill\"\nimport \"quill/dist/quill.core.css\"\nimport \"quill/dist/quill.snow.css\"\nimport { getToken } from \"@/utils/auth\"\n\n\n// 全局状态管理当前激活的编辑器\nlet currentActiveEditor = null\n\nexport default {\n  name: \"OptionEditor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: 80,\n    },\n    /* 占位符 */\n    placeholder: {\n      type: String,\n      default: \"请输入内容\",\n    },\n    /* 编辑器唯一标识 */\n    editorId: {\n      type: String,\n      default: () => 'editor_' + Math.random().toString(36).substr(2, 9)\n    }\n  },\n  data() {\n    return {\n      Quill: null,\n      currentValue: \"\",\n      isFocused: false,\n      showToolbar: false,\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置（移除视频功能）\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ align: [] }],                                 // 对齐方式\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],  // 字体大小\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [\"link\", \"image\"],                               // 链接、图片\n            [\"clean\"]                                        // 清除文本格式\n          ],\n        },\n        placeholder: this.placeholder,\n        readOnly: false,\n      },\n    }\n  },\n  computed: {\n    styles() {\n      let style = {}\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`\n      }\n      return style\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init()\n    // 添加全局点击事件监听\n    document.addEventListener('click', this.handleGlobalClick)\n  },\n  beforeDestroy() {\n    // 如果当前编辑器是激活状态，清除全局状态\n    if (currentActiveEditor === this) {\n      currentActiveEditor = null\n    }\n    this.Quill = null\n    // 移除全局点击事件监听\n    document.removeEventListener('click', this.handleGlobalClick)\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor\n      this.Quill = new Quill(editor, this.options)\n\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n\n      // 初始化时隐藏工具栏\n      this.hideToolbar()\n\n      // 图片上传处理\n      const toolbar = this.Quill.getModule(\"toolbar\")\n      toolbar.addHandler(\"image\", this.imgHandler)\n\n      // 监听内容变化\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML\n        const text = this.Quill.getText()\n        const quill = this.Quill\n        this.currentValue = html\n        this.$emit(\"input\", html)\n        this.$emit(\"on-change\", { html, text, quill })\n      })\n\n      // 监听焦点事件\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        if (range) {\n          this.isFocused = true\n        } else {\n          this.isFocused = false\n        }\n        this.$emit(\"on-selection-change\", range, oldRange, source)\n      })\n    },\n    handleEditorClick() {\n      this.setActiveEditor()\n      if (this.Quill) {\n        this.Quill.focus()\n      }\n    },\n    // 设置当前编辑器为激活状态\n    setActiveEditor() {\n      // 隐藏其他编辑器的工具栏\n      if (currentActiveEditor && currentActiveEditor !== this) {\n        currentActiveEditor.showToolbar = false\n        currentActiveEditor.hideToolbar()\n      }\n      // 设置当前编辑器为激活状态\n      currentActiveEditor = this\n      this.showToolbar = true\n      this.showToolbarElement()\n    },\n    // 处理全局点击事件\n    handleGlobalClick(event) {\n      // 检查点击是否在当前编辑器内\n      if (!this.$el.contains(event.target)) {\n        // 如果当前编辑器显示工具栏，则隐藏\n        if (this.showToolbar) {\n          this.showToolbar = false\n          this.hideToolbar()\n          if (currentActiveEditor === this) {\n            currentActiveEditor = null\n          }\n        }\n      }\n    },\n    // 显示工具栏\n    showToolbarElement() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'block'\n        }\n      })\n    },\n    // 隐藏工具栏\n    hideToolbar() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'none'\n        }\n      })\n    },\n    // 图片上传处理\n    imgHandler() {\n      this.$refs.upload.$children[0].$refs.input.click()\n    },\n\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件大小\n      if (file.size / 1024 / 1024 > 5) {\n        this.$message.error(\"上传文件大小不能超过 5MB!\")\n        return false\n      }\n      return true\n    },\n\n    // 上传成功处理\n    handleUploadSuccess(res, file) {\n      if (res.code == 200) {\n        // 获取光标所在位置\n        let length = this.Quill.getSelection().index\n        // 插入图片\n        this.Quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName)\n        // 调整光标到最后\n        this.Quill.setSelection(length + 1)\n      } else {\n        this.$message.error(\"图片插入失败\")\n      }\n    },\n\n    // 上传失败处理\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\")\n    }\n  }\n}\n</script>\n\n<style>\n.option-editor {\n  position: relative;\n}\n\n.editor-container {\n  position: relative;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.editor-container:hover {\n  border-color: #c0c4cc;\n}\n\n.editor-container.focused {\n  border-color: #409EFF;\n}\n\n.editor-container.show-toolbar {\n  border-color: #409EFF;\n}\n\n.editor {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n\n/* 工具栏样式 */\n.option-editor .ql-toolbar {\n  border: 1px solid #e4e7ed;\n  border-top: none;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.option-editor .ql-container {\n  border: none;\n  font-size: 14px;\n}\n\n.option-editor .ql-editor {\n  padding: 12px;\n  min-height: inherit;\n  line-height: 1.5;\n}\n\n.option-editor .ql-editor.ql-blank::before {\n  font-style: normal;\n  color: #c0c4cc;\n  left: 12px;\n}\n\n/* 当显示工具栏时，调整编辑器容器的圆角 */\n.editor-container.show-toolbar .ql-container {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n/* 工具栏按钮样式优化 */\n.option-editor .ql-toolbar .ql-formats {\n  margin-right: 15px;\n}\n\n.option-editor .ql-toolbar .ql-formats:last-child {\n  margin-right: 0;\n}\n\n/* 工具栏过渡动画 */\n.option-editor .ql-toolbar {\n  transition: all 0.3s ease;\n}\n</style>\n"], "mappings": ";;;;;;;;;;AA0BA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA,IAAAE,mBAAA;AAAA,IAAAC,SAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAL,OAAA;IACA;IACA;IACAM,SAAA;MACAF,IAAA,EAAAG,MAAA;MACAP,OAAA;IACA;IACA;IACAQ,WAAA;MACAJ,IAAA,EAAAC,MAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAL,IAAA,EAAAC,MAAA;MACAL,OAAA,WAAAA,SAAA;QAAA,mBAAAU,IAAA,CAAAC,MAAA,GAAAC,QAAA,KAAAC,MAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,YAAA;MACAC,SAAA;MACAC,WAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,OAAA;QACAC,KAAA;QACAC,MAAA,EAAAC,QAAA,CAAAC,IAAA;QACAC,KAAA;QACAC,OAAA;UACA;UACAC,OAAA,GACA;UAAA;UACA;UAAA;UACA;YAAAC,IAAA;UAAA;YAAAA,IAAA;UAAA;UAAA;UACA;YAAAC,MAAA;UAAA;YAAAA,MAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;UAAA;UACA;YAAAC,IAAA;UAAA;UAAA;UACA;YAAAC,KAAA;UAAA;YAAAC,UAAA;UAAA;UAAA;UACA;UAAA;UACA;UAAA;QAEA;QACA/B,WAAA,OAAAA,WAAA;QACAgC,QAAA;MACA;IACA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,IAAAC,KAAA;MACA,SAAArC,SAAA;QACAqC,KAAA,CAAArC,SAAA,MAAAsC,MAAA,MAAAtC,SAAA;MACA;MACA,OAAAqC,KAAA;IACA;EACA;EACAE,KAAA;IACA1C,KAAA;MACA2C,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA,UAAA/B,YAAA;UACA,KAAAA,YAAA,GAAA+B,GAAA,iBAAAA,GAAA;UACA,SAAAhC,KAAA;YACA,KAAAA,KAAA,CAAAiC,SAAA,CAAAC,oBAAA,MAAAjC,YAAA;UACA;QACA;MACA;MACAkC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA;IACAvB,QAAA,CAAAwB,gBAAA,eAAAC,iBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,IAAA1D,mBAAA;MACAA,mBAAA;IACA;IACA,KAAAkB,KAAA;IACA;IACAc,QAAA,CAAA2B,mBAAA,eAAAF,iBAAA;EACA;EACAG,OAAA;IACAL,IAAA,WAAAA,KAAA;MAAA,IAAAM,KAAA;MACA,IAAAC,MAAA,QAAAC,KAAA,CAAAD,MAAA;MACA,KAAA5C,KAAA,OAAAA,cAAA,CAAA4C,MAAA,OAAAjC,OAAA;MAEA,KAAAX,KAAA,CAAAiC,SAAA,CAAAC,oBAAA,MAAAjC,YAAA;;MAEA;MACA,KAAA6C,WAAA;;MAEA;MACA,IAAA5B,OAAA,QAAAlB,KAAA,CAAA+C,SAAA;MACA7B,OAAA,CAAA8B,UAAA,eAAAC,UAAA;;MAEA;MACA,KAAAjD,KAAA,CAAAkD,EAAA,0BAAAC,KAAA,EAAAC,QAAA,EAAAC,MAAA;QACA,IAAAC,IAAA,GAAAX,KAAA,CAAAE,KAAA,CAAAD,MAAA,CAAAW,QAAA,IAAAC,SAAA;QACA,IAAAC,IAAA,GAAAd,KAAA,CAAA3C,KAAA,CAAA0D,OAAA;QACA,IAAAC,KAAA,GAAAhB,KAAA,CAAA3C,KAAA;QACA2C,KAAA,CAAA1C,YAAA,GAAAqD,IAAA;QACAX,KAAA,CAAAiB,KAAA,UAAAN,IAAA;QACAX,KAAA,CAAAiB,KAAA;UAAAN,IAAA,EAAAA,IAAA;UAAAG,IAAA,EAAAA,IAAA;UAAAE,KAAA,EAAAA;QAAA;MACA;;MAEA;MACA,KAAA3D,KAAA,CAAAkD,EAAA,+BAAAW,KAAA,EAAAC,QAAA,EAAAT,MAAA;QACA,IAAAQ,KAAA;UACAlB,KAAA,CAAAzC,SAAA;QACA;UACAyC,KAAA,CAAAzC,SAAA;QACA;QACAyC,KAAA,CAAAiB,KAAA,wBAAAC,KAAA,EAAAC,QAAA,EAAAT,MAAA;MACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAC,eAAA;MACA,SAAAhE,KAAA;QACA,KAAAA,KAAA,CAAAiE,KAAA;MACA;IACA;IACA;IACAD,eAAA,WAAAA,gBAAA;MACA;MACA,IAAAlF,mBAAA,IAAAA,mBAAA;QACAA,mBAAA,CAAAqB,WAAA;QACArB,mBAAA,CAAAgE,WAAA;MACA;MACA;MACAhE,mBAAA;MACA,KAAAqB,WAAA;MACA,KAAA+D,kBAAA;IACA;IACA;IACA3B,iBAAA,WAAAA,kBAAA4B,KAAA;MACA;MACA,UAAAC,GAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAG,MAAA;QACA;QACA,SAAAnE,WAAA;UACA,KAAAA,WAAA;UACA,KAAA2C,WAAA;UACA,IAAAhE,mBAAA;YACAA,mBAAA;UACA;QACA;MACA;IACA;IACA;IACAoF,kBAAA,WAAAA,mBAAA;MAAA,IAAAK,MAAA;MACA,KAAAC,SAAA;QACA,IAAAtD,OAAA,GAAAqD,MAAA,CAAAH,GAAA,CAAAK,aAAA;QACA,IAAAvD,OAAA;UACAA,OAAA,CAAAU,KAAA,CAAA8C,OAAA;QACA;MACA;IACA;IACA;IACA5B,WAAA,WAAAA,YAAA;MAAA,IAAA6B,MAAA;MACA,KAAAH,SAAA;QACA,IAAAtD,OAAA,GAAAyD,MAAA,CAAAP,GAAA,CAAAK,aAAA;QACA,IAAAvD,OAAA;UACAA,OAAA,CAAAU,KAAA,CAAA8C,OAAA;QACA;MACA;IACA;IACA;IACAzB,UAAA,WAAAA,WAAA;MACA,KAAAJ,KAAA,CAAA+B,MAAA,CAAAC,SAAA,IAAAhC,KAAA,CAAAiC,KAAA,CAAAC,KAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA;MACA,IAAAA,IAAA,CAAA3D,IAAA;QACA,KAAA4D,QAAA,CAAAC,KAAA;QACA;MACA;MACA;IACA;IAEA;IACAC,mBAAA,WAAAA,oBAAAC,GAAA,EAAAJ,IAAA;MACA,IAAAI,GAAA,CAAAC,IAAA;QACA;QACA,IAAAC,MAAA,QAAAvF,KAAA,CAAAwF,YAAA,GAAAC,KAAA;QACA;QACA,KAAAzF,KAAA,CAAA0F,WAAA,CAAAH,MAAA,WAAAlF,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GAAA8E,GAAA,CAAAM,QAAA;QACA;QACA,KAAA3F,KAAA,CAAA4F,YAAA,CAAAL,MAAA;MACA;QACA,KAAAL,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAX,QAAA,CAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}]}