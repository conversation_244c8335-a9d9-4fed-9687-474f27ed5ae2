{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_QuestionCard", "_interopRequireDefault", "require", "_QuestionForm", "_BatchImport", "_question", "_questionBank", "_methods", "name", "components", "QuestionCard", "QuestionForm", "BatchImport", "data", "_ref", "bankId", "bankName", "statistics", "total", "singleChoice", "multipleChoice", "judgment", "questionList", "queryParams", "pageNum", "pageSize", "questionType", "difficulty", "questionContent", "expandAll", "selectedQuestions", "_defineProperty2", "default", "reverse", "allowDuplicate", "process", "env", "VUE_APP_BASE_API", "Authorization", "$store", "getters", "token", "watch", "documentContent", "handler", "newVal", "isSettingFromBackend", "trim", "debounceParseDocument", "parsedQuestions", "parseErrors", "immediate", "importDrawerVisible", "_this", "$nextTick", "initRichEditor", "rich<PERSON><PERSON><PERSON>", "destroy", "editorInitialized", "created", "initPage", "debounce", "parseDocument", "uploadData", "uploadHeaders", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "_this$$route$query", "$route", "query", "$message", "error", "goBack", "getQuestionList", "getStatistics", "$router", "back", "_this2", "params", "convertQueryParams", "listQuestion", "then", "response", "rows", "catch", "convertedParams", "_objectSpread2", "typeMap", "difficultyMap", "Object", "keys", "for<PERSON>ach", "key", "undefined", "_this3", "getQuestionStatistics", "handleBatchImport", "batchImportVisible", "handleAddQuestion", "type", "currentQuestionType", "currentQuestionData", "questionFormVisible", "toggleExpandAll", "expandedQuestions", "handleExportQuestions", "length", "warning", "info", "concat", "handleToggleSelectAll", "isAllSelected", "map", "q", "questionId", "success", "handleBatchDelete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "deletePromises", "delQuestion", "Promise", "all", "allSelected", "_this5", "handleQuestionSelect", "selected", "includes", "push", "index", "indexOf", "splice", "handleToggleExpand", "handleEditQuestion", "question", "handleCopyQuestion", "copiedQuestion", "createTime", "updateTime", "createBy", "updateBy", "convertQuestionTypeToString", "handleDeleteQuestion", "_this6", "replace", "displayContent", "substring", "handleQuestionFormSuccess", "handleBatchImportSuccess", "handleDrawerClose", "done", "showDocumentImportDialog", "_this7", "isUploading", "isParsing", "uploadComponent", "$refs", "documentUpload", "clearFiles", "documentImportDialogVisible", "showRulesDialog", "activeRuleTab", "rulesDialogVisible", "copyExampleToEditor", "_this8", "htmlTemplate", "setData", "downloadExcelTemplate", "download", "downloadWordTemplate", "beforeUpload", "file", "isValidType", "endsWith", "isLt10M", "size", "handleUploadSuccess", "_this9", "code", "setTimeout", "questions", "collapsed", "allExpanded", "errors", "errorCount", "originalContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentHtmlContent", "msg", "handleUploadError", "collapseAll", "_this0", "$set", "toggleQuestion", "toggleAllQuestions", "_this1", "confirmImport", "_this10", "importQuestions", "_this11", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "questionsToImport", "importData", "_t", "w", "_context", "n", "p", "_toConsumableArray2", "importOptions", "batchImportQuestions", "v", "Error", "a", "_this12", "window", "CKEDITOR", "fallbackToTextarea", "<PERSON><PERSON><PERSON><PERSON>", "document", "getElementById", "innerHTML", "showFallbackEditor", "height", "toolbar", "items", "removeButtons", "language", "removePlugins", "resize_enabled", "extraPlugins", "<PERSON><PERSON><PERSON><PERSON>", "fontSize_sizes", "fontSize_defaultLabel", "colorButton_enableMore", "colorButton_colors", "filebrowserUploadUrl", "image_previewText", "baseHref", "pluginsLoaded", "instanceReady", "editor", "evt", "on", "dialog", "getName", "checkInterval", "setInterval", "urlField", "getContentElement", "getValue", "startsWith", "clearInterval", "selectPage", "e", "removeDialogTabs", "fallback<PERSON><PERSON>r", "rawContent", "getData", "contentWithRelativeUrls", "convertUrlsToRelative", "preserveRichTextFormatting", "stripHtmlTagsKeepImages", "_this13", "textarea", "createElement", "className", "placeholder", "value", "style", "cssText", "addEventListener", "target", "append<PERSON><PERSON><PERSON>", "stripHtmlTags", "html", "div", "textContent", "innerText", "content", "func", "wait", "timeout", "executedFunction", "_len", "arguments", "args", "Array", "_key", "later", "clearTimeout", "apply", "<PERSON><PERSON><PERSON><PERSON>", "location", "origin", "urlRegex", "RegExp", "parseResult", "parseQuestionContent", "message", "lines", "split", "line", "filter", "currentQuestionLines", "questionNumber", "i", "isQuestionStart", "isQuestionStartLine", "isQuestionTypeStart", "questionText", "join", "parsedQuestion", "parseQuestionFromLines", "console", "log", "test", "contentStartIndex", "typeMatch", "match", "typeText", "remainingContent", "isOptionLine", "isAnswerLine", "isExplanationLine", "isDifficultyLine", "cleanLine", "finalQ<PERSON>ionContent", "removeQuestionNumber", "typeName", "getTypeDisplayName", "explanation", "options", "<PERSON><PERSON><PERSON><PERSON>", "optionResult", "parseOptionsFromLines", "parseQuestionMetaFromLines", "processImagePaths", "processedContent", "before", "src", "after", "fullSrc", "result", "_this14", "images", "imageIndex", "contentWithPlaceholders", "finalContent", "img", "startIndex", "isArray", "warn", "optionMatch", "optionKey", "toUpperCase", "optionContent", "label", "multipleOptionsMatch", "singleOptions", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "singleOption", "err", "f", "answerMatch", "parseAnswerValue", "explanationMatch", "difficultyMatch", "extractAnswerFromQuestionContent", "patterns", "_i3", "_patterns", "pattern", "matches", "lastMatch", "answer", "answerText", "trimmedAnswer", "splitByQuestionType", "sections", "typeRegex", "lastIndex", "currentType", "exec", "parseSectionQuestions", "section", "_this15", "convertQuestionType", "questionBlocks", "splitByQuestionNumber", "block", "parseQuestionBlock", "blocks", "numberRegex", "firstLine", "currentLineIndex", "numberMatch", "parseOptions", "nextIndex", "parseQuestionMeta", "currentIndex", "parseAnswer", "extractAnswerFromContent", "bracketPatterns", "_i4", "_bracketPatterns", "matchAll", "getQuestionTypeName", "getQuestionTypeColor", "colorMap", "getFormattedQuestionContent", "htmlContent", "extractQuestionFromHtml", "plainContent", "plainText", "paragraphs", "_iterator2", "_step2", "paragraph", "paragraphText", "cleanParagraphText", "handleSearch", "resetSearch"], "sources": ["src/views/biz/questionBank/detail.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n\n            } catch (fallbackError) {\n\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            // 编辑器初始化完成\n          })\n        })\n\n      } catch (error) {\n\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n\n      } catch (error) {\n\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n\n        if (lines.length === 0) {\n\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAugBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AAAA,IAAAK,QAAA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,MAAA;MACAC,QAAA;MACA;MACAC,UAAA;QACAC,KAAA;QACAC,YAAA;QACAC,cAAA;QACAC,QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAJ,KAAA;MACAK,WAAA;QACAC,OAAA;QACAC,QAAA;QACAV,MAAA;QACAW,YAAA;QACAC,UAAA;QACAC,eAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,iBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,uBAEA,sBACA,6BACA,4BAEA,+BACA,kCACA,8BAEA,8BACA,6BACA,6BAEA,SAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,yBACA,wBACA,oBACA,oBAEA,+BAEA,uCACA,8BACA,yBAEA,4BAEA,qBACA,YAAAiB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAlB,IAAA,mBACA;MACAmB,OAAA;MACAC,cAAA;IACA,iBAEAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,yDACA;MACAC,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA,kBACA,mBAEA,4BACA;EAEA;EAEAC,KAAA;IACA;IACAC,eAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA,SAAAC,oBAAA;UACA;QACA;QAIA,IAAAD,MAAA,IAAAA,MAAA,CAAAE,IAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAAC,eAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAC,mBAAA;MACAR,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAQ,KAAA;QACA,IAAAR,MAAA;UACA;UACA,KAAAS,SAAA;YACAD,KAAA,CAAAE,cAAA;UACA;QACA;UACA;UACA,SAAAC,UAAA;YACA,KAAAA,UAAA,CAAAC,OAAA;YACA,KAAAD,UAAA;YACA,KAAAE,iBAAA;UACA;QACA;MACA;MACAP,SAAA;IACA;EACA;EAEAQ,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA;IACA,KAAAZ,qBAAA,QAAAa,QAAA,MAAAC,aAAA;IACA;IACA,KAAAC,UAAA;MACAhD,MAAA,OAAAA;IACA;IACA,KAAAiD,aAAA;MACA1B,aAAA,mBAAAC,MAAA,CAAAC,OAAA,CAAAC;IACA;EACA;EAEAwB,OAAA,WAAAA,QAAA;IACA;EAAA,CAEA;EAEAC,aAAA,WAAAA,cAAA;IAGA;IACA,SAAAV,UAAA;MACA,KAAAA,UAAA,CAAAC,OAAA;MACA,KAAAD,UAAA;IACA;EACA;EACAW,OAAA,GAAA5D,QAAA;IACA;IACAqD,QAAA,WAAAA,SAAA;MACA,IAAAQ,kBAAA,QAAAC,MAAA,CAAAC,KAAA;QAAAvD,MAAA,GAAAqD,kBAAA,CAAArD,MAAA;QAAAC,QAAA,GAAAoD,kBAAA,CAAApD,QAAA;MACA,KAAAD,MAAA;QACA,KAAAwD,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MACA,KAAA1D,MAAA,GAAAA,MAAA;MACA,KAAAC,QAAA,GAAAA,QAAA;MACA,KAAAO,WAAA,CAAAR,MAAA,GAAAA,MAAA;MACA,KAAA2D,eAAA;MACA,KAAAC,aAAA;IACA;IACA;IACAF,MAAA,WAAAA,OAAA;MACA,KAAAG,OAAA,CAAAC,IAAA;IACA;IACA;IACAH,eAAA,WAAAA,gBAAA;MAAA,IAAAI,MAAA;MACA;MACA,IAAAC,MAAA,QAAAC,kBAAA,MAAAzD,WAAA;MACA,IAAA0D,sBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,MAAA,CAAAxD,YAAA,GAAA6D,QAAA,CAAAC,IAAA;QACAN,MAAA,CAAA5D,KAAA,GAAAiE,QAAA,CAAAjE,KAAA;MACA,GAAAmE,KAAA,WAAAb,KAAA;QAEAM,MAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAQ,kBAAA,WAAAA,mBAAAD,MAAA;MACA,IAAAO,eAAA,OAAAC,cAAA,CAAAvD,OAAA,MAAA+C,MAAA;;MAEA;MACA,IAAAO,eAAA,CAAA5D,YAAA;QACA,IAAA8D,OAAA;UACA;UACA;UACA;QACA;QACAF,eAAA,CAAA5D,YAAA,GAAA8D,OAAA,CAAAF,eAAA,CAAA5D,YAAA,KAAA4D,eAAA,CAAA5D,YAAA;MACA;;MAEA;MACA,IAAA4D,eAAA,CAAA3D,UAAA;QACA,IAAA8D,aAAA;UACA;UACA;UACA;QACA;QACAH,eAAA,CAAA3D,UAAA,GAAA8D,aAAA,CAAAH,eAAA,CAAA3D,UAAA,KAAA2D,eAAA,CAAA3D,UAAA;MACA;;MAEA;MACA+D,MAAA,CAAAC,IAAA,CAAAL,eAAA,EAAAM,OAAA,WAAAC,GAAA;QACA,IAAAP,eAAA,CAAAO,GAAA,YAAAP,eAAA,CAAAO,GAAA,cAAAP,eAAA,CAAAO,GAAA,MAAAC,SAAA;UACA,OAAAR,eAAA,CAAAO,GAAA;QACA;MACA;MAEA,OAAAP,eAAA;IACA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,+BAAA,OAAAjF,MAAA,EAAAmE,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAA9E,UAAA,GAAAkE,QAAA,CAAAtE,IAAA;MACA,GAAAwE,KAAA,WAAAb,KAAA;QAEA;QACAuB,MAAA,CAAA9E,UAAA;UACAC,KAAA;UACAC,YAAA;UACAC,cAAA;UACAC,QAAA;QACA;MACA;IACA;IACA;IACA4E,iBAAA,WAAAA,kBAAA;MACA,KAAAC,kBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAC,mBAAA,GAAAD,IAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,mBAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAA3E,SAAA,SAAAA,SAAA;MACA,UAAAA,SAAA;QACA,KAAA4E,iBAAA;MACA;IACA;IAIA;IACAC,qBAAA,WAAAA,sBAAA;MACA,SAAA5E,iBAAA,CAAA6E,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MACA,KAAArC,QAAA,CAAAsC,IAAA,6BAAAC,MAAA,MAAAhF,iBAAA,CAAA6E,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAA;MACA,KAAAC,aAAA,SAAAA,aAAA;MACA,SAAAA,aAAA;QACA;QACA,KAAAlF,iBAAA,QAAAR,YAAA,CAAA2F,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAC,UAAA;QAAA;QACA,KAAA5C,QAAA,CAAA6C,OAAA,uBAAAN,MAAA,MAAAhF,iBAAA,CAAA6E,MAAA;MACA;QACA;QACA,KAAA7E,iBAAA;QACA,KAAAyC,QAAA,CAAA6C,OAAA;MACA;IACA;IAIA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAAxF,iBAAA,CAAA6E,MAAA;QACA,KAAApC,QAAA,CAAAqC,OAAA;QACA;MACA;MAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAhF,iBAAA,CAAA6E,MAAA;QACAa,iBAAA;QACAC,gBAAA;QACArB,IAAA;MACA,GAAAlB,IAAA;QACA;QACA;QACA,IAAAwC,cAAA,GAAAJ,MAAA,CAAAxF,iBAAA,CAAAmF,GAAA,WAAAE,UAAA;UAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;QAAA,CACA;QAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;UACAoC,MAAA,CAAA/C,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAQ,MAAA,CAAAxF,iBAAA,CAAA6E,MAAA;UACAW,MAAA,CAAAxF,iBAAA;UACAwF,MAAA,CAAAQ,WAAA;UACAR,MAAA,CAAA5C,eAAA;UACA4C,MAAA,CAAA3C,aAAA;QACA,GAAAU,KAAA,WAAAb,KAAA;UAEA8C,MAAA,CAAA/C,QAAA,CAAAC,KAAA;QACA;MACA;IACA;EAAA,OAAAzC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,gCAAA8G,kBAAA,EAGA;IAAA,IAAAU,MAAA;IACA,SAAAjG,iBAAA,CAAA6E,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,+CAAAT,MAAA,MAAAhF,iBAAA,CAAA6E,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA;MACA,IAAAwC,cAAA,GAAAK,MAAA,CAAAjG,iBAAA,CAAAmF,GAAA,WAAAE,UAAA;QAAA,OACA,IAAAQ,qBAAA,EAAAR,UAAA;MAAA,CACA;MAEAS,OAAA,CAAAC,GAAA,CAAAH,cAAA,EAAAxC,IAAA;QACA6C,MAAA,CAAAxD,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAiB,MAAA,CAAAjG,iBAAA,CAAA6E,MAAA;QACAoB,MAAA,CAAAjG,iBAAA;QACAiG,MAAA,CAAAf,aAAA;QACAe,MAAA,CAAArD,eAAA;QACAqD,MAAA,CAAApD,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEAuD,MAAA,CAAAxD,QAAA,CAAAC,KAAA;MACA;IACA,GAAAa,KAAA;MACA0C,MAAA,CAAAxD,QAAA,CAAAsC,IAAA;IACA;EACA,qCAGAmB,qBAAAb,UAAA,EAAAc,QAAA;IACA,IAAAA,QAAA;MACA,UAAAnG,iBAAA,CAAAoG,QAAA,CAAAf,UAAA;QACA,KAAArF,iBAAA,CAAAqG,IAAA,CAAAhB,UAAA;MACA;IACA;MACA,IAAAiB,KAAA,QAAAtG,iBAAA,CAAAuG,OAAA,CAAAlB,UAAA;MACA,IAAAiB,KAAA;QACA,KAAAtG,iBAAA,CAAAwG,MAAA,CAAAF,KAAA;MACA;IACA;;IAEA;IACA,KAAApB,aAAA,QAAAlF,iBAAA,CAAA6E,MAAA,UAAArF,YAAA,CAAAqF,MAAA;EACA,mCAEA4B,mBAAApB,UAAA;IACA,IAAAiB,KAAA,QAAA3B,iBAAA,CAAA4B,OAAA,CAAAlB,UAAA;IACA,IAAAiB,KAAA;MACA,KAAA3B,iBAAA,CAAA6B,MAAA,CAAAF,KAAA;IACA;MACA,KAAA3B,iBAAA,CAAA0B,IAAA,CAAAhB,UAAA;IACA;EACA,mCAEAqB,mBAAAC,QAAA;IACA,KAAAnC,mBAAA,GAAAmC,QAAA;IACA,KAAApC,mBAAA,GAAAoC,QAAA,CAAA/G,YAAA;IACA,KAAA6E,mBAAA;EACA,mCAEAmC,mBAAAD,QAAA;IACA;IACA,IAAAE,cAAA,OAAApD,cAAA,CAAAvD,OAAA,MAAAuD,cAAA,CAAAvD,OAAA,MACAyG,QAAA;MACAtB,UAAA;MAAA;MACAyB,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;IAAA,EACA;;IAEA;IACA,KAAAzC,mBAAA,GAAAqC,cAAA;IACA,KAAAtC,mBAAA,QAAA2C,2BAAA,CAAAP,QAAA,CAAA/G,YAAA;IACA,KAAA6E,mBAAA;EACA,4CAGAyC,4BAAA5C,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA,KAAAA,IAAA;EACA,qCAEA6C,qBAAAR,QAAA;IAAA,IAAAS,MAAA;IACA,IAAAtH,eAAA,GAAA6G,QAAA,CAAA7G,eAAA,CAAAuH,OAAA;IACA,IAAAC,cAAA,GAAAxH,eAAA,CAAA+E,MAAA,QAAA/E,eAAA,CAAAyH,SAAA,kBAAAzH,eAAA;IACA,KAAA2F,QAAA,0CAAAT,MAAA,CAAAsC,cAAA;MACA5B,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACA,IAAAyC,qBAAA,EAAAc,QAAA,CAAAtB,UAAA,EAAAjC,IAAA;QACAgE,MAAA,CAAA3E,QAAA,CAAA6C,OAAA;QACA8B,MAAA,CAAAxE,eAAA;QACAwE,MAAA,CAAAvE,aAAA;MACA,GAAAU,KAAA,WAAAb,KAAA;QAEA0E,MAAA,CAAA3E,QAAA,CAAAC,KAAA;MACA;IACA;EACA,0CAEA8E,0BAAA;IACA,KAAA/C,mBAAA;IACA,KAAA7B,eAAA;IACA,KAAAC,aAAA;EACA,yCAEA4E,yBAAA;IACA,KAAArD,kBAAA;IACA,KAAA9C,mBAAA;IACA,KAAAsB,eAAA;IACA,KAAAC,aAAA;EACA,kCAKA6E,kBAAAC,IAAA;IACAA,IAAA;EACA,QAAA1H,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,uCAGAmJ,yBAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;;IAEA;IACA,KAAAvG,SAAA;MACA,IAAAwG,eAAA,GAAAH,MAAA,CAAAI,KAAA,CAAAC,cAAA;MACA,IAAAF,eAAA;QACAA,eAAA,CAAAG,UAAA;MACA;IACA;IAEA,KAAAC,2BAAA;EAEA,gCAGAC,gBAAA;IACA,KAAAC,aAAA;IACA,KAAAC,kBAAA;EACA,oCAGAC,oBAAA;IAAA,IAAAC,MAAA;IACA;IACA,IAAAC,YAAA,muDAuBAzH,IAAA;;IAEA;IACA,SAAAS,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAiH,OAAA,CAAAD,YAAA;IAEA;MACA;MACA,KAAAlH,SAAA;QACA,IAAAiH,MAAA,CAAA/G,UAAA,IAAA+G,MAAA,CAAA7G,iBAAA;UACA6G,MAAA,CAAA/G,UAAA,CAAAiH,OAAA,CAAAD,YAAA;QAEA;MACA;IACA;;IAEA;IACA,KAAAH,kBAAA;;IAEA;IACA,KAAA9F,QAAA,CAAA6C,OAAA;EAGA,sCAGAsD,sBAAA;IACA,KAAAC,QAAA;EACA,qCAGAC,qBAAA;IACA,KAAAD,QAAA;EACA,6BAGAE,aAAAC,IAAA;IAGA,IAAAC,WAAA,GAAAD,IAAA,CAAA1E,IAAA,kFACA0E,IAAA,CAAA1E,IAAA,4EACA0E,IAAA,CAAAtK,IAAA,CAAAwK,QAAA,aAAAF,IAAA,CAAAtK,IAAA,CAAAwK,QAAA;IACA,IAAAC,OAAA,GAAAH,IAAA,CAAAI,IAAA;IAEA,KAAAH,WAAA;MACA,KAAAxG,QAAA,CAAAC,KAAA;MACA;IACA;IACA,KAAAyG,OAAA;MACA,KAAA1G,QAAA,CAAAC,KAAA;MACA;IACA;;IAEA;IACA,KAAAT,UAAA,CAAAhD,MAAA,QAAAA,MAAA;;IAEA;IACA,KAAA6I,WAAA;IACA,KAAAC,SAAA;IAIA;EACA,oCAGAsB,oBAAAhG,QAAA,EAAA2F,IAAA;IAAA,IAAAM,MAAA;IAGA,IAAAjG,QAAA,CAAAkG,IAAA;MACA;MACA,KAAAzB,WAAA;MACA,KAAAC,SAAA;;MAIA;MACA,KAAA5G,eAAA;MACA,KAAAC,WAAA;;MAEA;MACAoI,UAAA;QACAF,MAAA,CAAAlB,2BAAA;QACAkB,MAAA,CAAAvB,SAAA;MACA;;MAEA;MACA,KAAA/G,oBAAA;;MAEA;MACA,IAAAqC,QAAA,CAAAoG,SAAA,IAAApG,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA,KAAA1D,eAAA,GAAAkC,QAAA,CAAAoG,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;UAAA,WAAAlD,cAAA,CAAAvD,OAAA,MAAAuD,cAAA,CAAAvD,OAAA,MACAyG,QAAA;YACA+C,SAAA;UAAA;QAAA,CACA;QACA;QACA,KAAAC,WAAA;QACA,KAAAvI,WAAA,GAAAiC,QAAA,CAAAuG,MAAA;;QAEA;QACA,IAAAC,UAAA,GAAAxG,QAAA,CAAAuG,MAAA,GAAAvG,QAAA,CAAAuG,MAAA,CAAA/E,MAAA;QACA,IAAAgF,UAAA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA,sCAAAG,MAAA,CAAA6E,UAAA;QACA;UACA,KAAApH,QAAA,CAAA6C,OAAA,mCAAAN,MAAA,CAAA3B,QAAA,CAAAoG,SAAA,CAAA5E,MAAA;QACA;MAGA;QACA,KAAApC,QAAA,CAAAC,KAAA;QACA,KAAAvB,eAAA;QACA,KAAAC,WAAA,GAAAiC,QAAA,CAAAuG,MAAA;MAGA;;MAEA;MACA,IAAAvG,QAAA,CAAAyG,eAAA;QACA,KAAAC,gBAAA,CAAA1G,QAAA,CAAAyG,eAAA;QACA,KAAAjJ,eAAA,GAAAwC,QAAA,CAAAyG,eAAA;QACA,KAAAE,mBAAA,GAAA3G,QAAA,CAAAyG,eAAA;MAEA;;MAEA;MACAN,UAAA;QACAF,MAAA,CAAAtI,oBAAA;MACA;IACA;MAEA,KAAAyB,QAAA,CAAAC,KAAA,CAAAW,QAAA,CAAA4G,GAAA;MACA;MACA,KAAAnC,WAAA;MACA,KAAAC,SAAA;IACA;EACA,kCAGAmC,kBAAAxH,KAAA,EAAAsG,IAAA;IAEA,KAAAvG,QAAA,CAAAC,KAAA;;IAEA;IACA,KAAAoF,WAAA;IACA,KAAAC,SAAA;EACA,4BAGAoC,YAAA;IAAA,IAAAC,MAAA;IACA,KAAAjJ,eAAA,CAAA2C,OAAA,WAAA6C,QAAA;MACAyD,MAAA,CAAAC,IAAA,CAAA1D,QAAA;IACA;EACA,+BAGA2D,eAAAhE,KAAA;IACA,IAAAK,QAAA,QAAAxF,eAAA,CAAAmF,KAAA;IACA,KAAA+D,IAAA,CAAA1D,QAAA,gBAAAA,QAAA,CAAA+C,SAAA;EACA,QAAAzJ,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,iCAGA8L,mBAAA;IAAA,IAAAC,MAAA;IACA,KAAAb,WAAA,SAAAA,WAAA;IACA,KAAAxI,eAAA,CAAA2C,OAAA,WAAA6C,QAAA;MACA6D,MAAA,CAAAH,IAAA,CAAA1D,QAAA,gBAAA6D,MAAA,CAAAb,WAAA;IACA;EAEA,8BAGAc,cAAA;IAAA,IAAAC,OAAA;IACA,SAAAvJ,eAAA,CAAA0D,MAAA;MACA,KAAApC,QAAA,CAAAqC,OAAA;MACA;IACA;IAEA,KAAAW,QAAA,6BAAAT,MAAA,MAAA7D,eAAA,CAAA0D,MAAA;MACAa,iBAAA;MACAC,gBAAA;MACArB,IAAA;IACA,GAAAlB,IAAA;MACAsH,OAAA,CAAAC,eAAA;IACA,GAAApH,KAAA;EACA,gCAGAoH,gBAAA;IAAA,IAAAC,OAAA;IAAA,WAAAC,kBAAA,CAAA3K,OAAA,mBAAA4K,aAAA,CAAA5K,OAAA,IAAA6K,CAAA,UAAAC,QAAA;MAAA,IAAAC,iBAAA,EAAAC,UAAA,EAAA7H,QAAA,EAAA8H,EAAA;MAAA,WAAAL,aAAA,CAAA5K,OAAA,IAAAkL,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEA;YACAN,iBAAA,OAAAO,mBAAA,CAAAtL,OAAA,EAAA0K,OAAA,CAAAzJ,eAAA;YAEA,IAAAyJ,OAAA,CAAAa,aAAA,CAAAtL,OAAA;cACA8K,iBAAA,CAAA9K,OAAA;YACA;;YAEA;YACA+K,UAAA;cACAjM,MAAA,EAAA2L,OAAA,CAAA3L,MAAA;cACAwK,SAAA,EAAAwB,iBAAA;cACA7K,cAAA,EAAAwK,OAAA,CAAAa,aAAA,CAAArL;YACA;YAAAiL,QAAA,CAAAC,CAAA;YAAA,OAEA,IAAAI,kCAAA,EAAAR,UAAA;UAAA;YAAA7H,QAAA,GAAAgI,QAAA,CAAAM,CAAA;YAAA,MAEAtI,QAAA,CAAAkG,IAAA;cAAA8B,QAAA,CAAAC,CAAA;cAAA;YAAA;YACAV,OAAA,CAAAnI,QAAA,CAAA6C,OAAA,6BAAAN,MAAA,CAAAiG,iBAAA,CAAApG,MAAA;YAAAwG,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAA,MAEA,IAAAM,KAAA,CAAAvI,QAAA,CAAA4G,GAAA;UAAA;YAEAW,OAAA,CAAAtJ,mBAAA;YACAsJ,OAAA,CAAA/J,eAAA;YACA+J,OAAA,CAAAZ,mBAAA;YACAY,OAAA,CAAAzJ,eAAA;YACAyJ,OAAA,CAAAxJ,WAAA;YAIAwJ,OAAA,CAAAhI,eAAA;YACAgI,OAAA,CAAA/H,aAAA;YAAAwI,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;YAGAf,OAAA,CAAAnI,QAAA,CAAAC,KAAA;UAAA;YAAA,OAAA2I,QAAA,CAAAQ,CAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EAEA,+BAGAvJ,eAAA;IAAA,IAAAqK,OAAA;IACA,SAAAlK,iBAAA;MACA;IACA;;IAEA;IACA,KAAAmK,MAAA,CAAAC,QAAA;MAEA,KAAAC,kBAAA;MACA;IACA;IAEA;MACA;MACA,SAAAvK,UAAA;QACA,KAAAA,UAAA,CAAAC,OAAA;QACA,KAAAD,UAAA;MACA;;MAEA;MACA,IAAAwK,eAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,eAAA;QAEA;MACA;;MAEA;MACAA,eAAA,CAAAG,SAAA;;MAEA;MACA,KAAA7K,SAAA;QACA;QACA,KAAAuK,MAAA,CAAAC,QAAA,KAAAD,MAAA,CAAAC,QAAA,CAAA3E,OAAA;UAEAyE,OAAA,CAAAQ,kBAAA;UACA;QACA;QAEA;UACA;UACAR,OAAA,CAAApK,UAAA,GAAAqK,MAAA,CAAAC,QAAA,CAAA3E,OAAA,6BAAApH,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;YACAqM,MAAA;YAAA;YACAC,OAAA,GACA;cAAA9N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,GACA;cAAA/N,IAAA;cAAA+N,KAAA;YAAA,EACA;YACAC,aAAA;YACAC,QAAA;YACAC,aAAA;YACAC,cAAA;YACAC,YAAA;YACAC,cAAA;YACA;YACAC,cAAA;YACAC,qBAAA;YACA;YACAC,sBAAA;YACAC,kBAAA;YACA;YACAC,oBAAA,EAAA/M,OAAA,CAAAC,GAAA,CAAAC,gBAAA;YACA8M,iBAAA;YACA;YACAC,QAAA;UAAA,wBAEA,uCACA,2BAEA,oCAEA;YACAC,aAAA,WAAAA,cAAA,GAEA;YACAC,aAAA,WAAAA,cAAA;cAGA,IAAAC,MAAA,GAAAC,GAAA,CAAAD,MAAA;;cAEA;cACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;gBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA3O,IAAA;gBACA,IAAA6O,MAAA,CAAAC,OAAA;kBAGA;kBACArE,UAAA;oBACA,IAAAsE,aAAA,GAAAC,WAAA;sBACA;wBACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;wBACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;0BACAC,aAAA,CAAAN,aAAA;;0BAEA;0BACAF,MAAA,CAAAS,UAAA;wBACA;sBACA,SAAAC,CAAA;wBACA;sBAAA;oBAEA;;oBAEA;oBACA9E,UAAA;sBAAA,OAAA4E,aAAA,CAAAN,aAAA;oBAAA;kBACA;gBACA;cACA;YACA;UAEA,EACA;QACA,SAAApL,KAAA;UAGA;UACA;YACAoJ,OAAA,CAAApK,UAAA,GAAAqK,MAAA,CAAAC,QAAA,CAAA3E,OAAA;cACAkF,MAAA;cACAC,OAAA,GACA,2CACA,kCACA,uBACA,kBACA,oBACA,sCACA;cACAE,aAAA;cACAC,QAAA;cACAC,aAAA;cACAC,cAAA;cACAC,YAAA;cACAC,cAAA;cACA;cACAK,oBAAA,EAAA/M,OAAA,CAAAC,GAAA,CAAAC,gBAAA;cACA8M,iBAAA;cACA;cACAC,QAAA;cACA;cACAiB,gBAAA;cACA;cACAZ,EAAA;gBACAH,aAAA,WAAAA,cAAAE,GAAA;kBAGA,IAAAD,MAAA,GAAAC,GAAA,CAAAD,MAAA;;kBAEA;kBACAA,MAAA,CAAAE,EAAA,yBAAAD,GAAA;oBACA,IAAAE,MAAA,GAAAF,GAAA,CAAA3O,IAAA;oBACA,IAAA6O,MAAA,CAAAC,OAAA;sBAGA;sBACArE,UAAA;wBACA,IAAAsE,aAAA,GAAAC,WAAA;0BACA;4BACA,IAAAC,QAAA,GAAAJ,MAAA,CAAAK,iBAAA;4BACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,QAAA,MAAAF,QAAA,CAAAE,QAAA,GAAAC,UAAA;8BACAC,aAAA,CAAAN,aAAA;;8BAEA;8BACAF,MAAA,CAAAS,UAAA;4BACA;0BACA,SAAAC,CAAA;4BACA;0BAAA;wBAEA;;wBAEA;wBACA9E,UAAA;0BAAA,OAAA4E,aAAA,CAAAN,aAAA;wBAAA;sBACA;oBACA;kBACA;gBAGA;cACA;YACA;UAEA,SAAAU,aAAA;YAEA1C,OAAA,CAAAQ,kBAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAR,OAAA,CAAApK,UAAA,IAAAoK,OAAA,CAAApK,UAAA,CAAAiM,EAAA;UACA7B,OAAA,CAAApK,UAAA,CAAAiM,EAAA;YACA,IAAAc,UAAA,GAAA3C,OAAA,CAAApK,UAAA,CAAAgN,OAAA;YACA,IAAAC,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;;YAEA;YACA3C,OAAA,CAAA9B,mBAAA,GAAA8B,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAAjL,eAAA,GAAAiL,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;UAGA;QACA;;QAEA;QACA7C,OAAA,CAAApK,UAAA,CAAAiM,EAAA;UACAnE,UAAA;YACA,IAAAiF,UAAA,GAAA3C,OAAA,CAAApK,UAAA,CAAAgN,OAAA;YACA,IAAAC,uBAAA,GAAA7C,OAAA,CAAA8C,qBAAA,CAAAH,UAAA;;YAEA;YACA3C,OAAA,CAAA9B,mBAAA,GAAA8B,OAAA,CAAA+C,0BAAA,CAAAF,uBAAA;YACA;YACA7C,OAAA,CAAAjL,eAAA,GAAAiL,OAAA,CAAAgD,uBAAA,CAAAH,uBAAA;UAGA;QACA;;QAEA;QACA7C,OAAA,CAAApK,UAAA,CAAAiM,EAAA;UACA7B,OAAA,CAAAlK,iBAAA;UACA;QACA;MACA;IAEA,SAAAc,KAAA;MAEA;MACA,KAAAuJ,kBAAA;IACA;EACA,mCAGAA,mBAAA;IAAA,IAAA8C,OAAA;IACA,IAAA7C,eAAA,GAAAC,QAAA,CAAAC,cAAA;IACA,IAAAF,eAAA;MACA,IAAA8C,QAAA,GAAA7C,QAAA,CAAA8C,aAAA;MACAD,QAAA,CAAAE,SAAA;MACAF,QAAA,CAAAG,WAAA;MACAH,QAAA,CAAAI,KAAA,QAAAvO,eAAA;MACAmO,QAAA,CAAAK,KAAA,CAAAC,OAAA;;MAEA;MACAN,QAAA,CAAAO,gBAAA,oBAAAjB,CAAA;QACAS,OAAA,CAAAlO,eAAA,GAAAyN,CAAA,CAAAkB,MAAA,CAAAJ,KAAA;QACAL,OAAA,CAAA/E,mBAAA,GAAAsE,CAAA,CAAAkB,MAAA,CAAAJ,KAAA;MAEA;MAEAlD,eAAA,CAAAG,SAAA;MACAH,eAAA,CAAAuD,WAAA,CAAAT,QAAA;MACA,KAAApN,iBAAA;IACA;EACA,8BAGA8N,cAAAC,IAAA;IACA,IAAAC,GAAA,GAAAzD,QAAA,CAAA8C,aAAA;IACAW,GAAA,CAAAvD,SAAA,GAAAsD,IAAA;IACA,OAAAC,GAAA,CAAAC,WAAA,IAAAD,GAAA,CAAAE,SAAA;EACA,iCAGA/F,iBAAAgG,OAAA;IAEA,SAAArO,UAAA,SAAAE,iBAAA;MACA,KAAAF,UAAA,CAAAiH,OAAA,CAAAoH,OAAA;IACA;MACA;MACA,KAAAlP,eAAA,GAAAkP,OAAA;MACA,KAAA/F,mBAAA,GAAA+F,OAAA;IACA;EACA,yBAKAhO,SAAAiO,IAAA,EAAAC,IAAA;IACA,IAAAC,OAAA;IACA,gBAAAC,iBAAA;MAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAxL,MAAA,EAAAyL,IAAA,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;QAAAF,IAAA,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;MAAA;MACA,IAAAC,KAAA,YAAAA,MAAA;QACAC,YAAA,CAAAR,OAAA;QACAF,IAAA,CAAAW,KAAA,SAAAL,IAAA;MACA;MACAI,YAAA,CAAAR,OAAA;MACAA,OAAA,GAAA1G,UAAA,CAAAiH,KAAA,EAAAR,IAAA;IACA;EACA,sCAGArB,sBAAAmB,OAAA;IACA,KAAAA,OAAA,SAAAA,OAAA;;IAEA;IACA,IAAAa,aAAA,GAAA7E,MAAA,CAAA8E,QAAA,CAAAC,MAAA;IACA,IAAAC,QAAA,OAAAC,MAAA,CAAAJ,aAAA,CAAAvJ,OAAA;IAEA,OAAA0I,OAAA,CAAA1I,OAAA,CAAA0J,QAAA;EACA,8BAGA/O,cAAA;IACA,UAAAnB,eAAA,CAAAI,IAAA;MACA,KAAAE,eAAA;MACA,KAAAC,WAAA;MACA;IACA;IAEA;MACA,IAAA6P,WAAA,QAAAC,oBAAA,MAAArQ,eAAA;MACA;MACA,KAAAM,eAAA,GAAA8P,WAAA,CAAAxH,SAAA,CAAAtE,GAAA,WAAAwB,QAAA;QAAA,WAAAlD,cAAA,CAAAvD,OAAA,MAAAuD,cAAA,CAAAvD,OAAA,MACAyG,QAAA;UACA+C,SAAA;QAAA;MAAA,CACA;MACA,KAAAtI,WAAA,GAAA6P,WAAA,CAAArH,MAAA;IAGA,SAAAlH,KAAA;MAEA,KAAAtB,WAAA,cAAAsB,KAAA,CAAAyO,OAAA;MACA,KAAAhQ,eAAA;IACA;EACA,QAAAlB,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,mCAGAyS,qBAAAnB,OAAA;IACA,IAAAtG,SAAA;IACA,IAAAG,MAAA;IAEA,KAAAmG,OAAA,WAAAA,OAAA;MAEA;QAAAtG,SAAA,EAAAA,SAAA;QAAAG,MAAA;MAAA;IACA;IAEA;MAGA;MACA,IAAAiG,WAAA,QAAAf,uBAAA,CAAAiB,OAAA;MAEA,KAAAF,WAAA,IAAAA,WAAA,CAAA5O,IAAA,GAAA4D,MAAA;QAEA;UAAA4E,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;;MAEA;MACA,IAAAwH,KAAA,GAAAvB,WAAA,CAAAwB,KAAA,OAAAlM,GAAA,WAAAmM,IAAA;QAAA,OAAAA,IAAA,CAAArQ,IAAA;MAAA,GAAAsQ,MAAA,WAAAD,IAAA;QAAA,OAAAA,IAAA,CAAAzM,MAAA;MAAA;MAGA,IAAAuM,KAAA,CAAAvM,MAAA;QAEA;UAAA4E,SAAA,EAAAA,SAAA;UAAAG,MAAA;QAAA;MACA;MAIA,IAAA4H,oBAAA;MACA,IAAAC,cAAA;MAEA,SAAAC,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;QAEA;QACA,IAAAC,eAAA,QAAAC,mBAAA,CAAAN,IAAA,UAAAO,mBAAA,CAAAP,IAAA;QAEA,IAAAK,eAAA;UACA;UACA,IAAAH,oBAAA,CAAA3M,MAAA;YACA;cACA,IAAAiN,YAAA,GAAAN,oBAAA,CAAAO,IAAA;cACA,IAAAC,cAAA,QAAAC,sBAAA,CAAAH,YAAA,EAAAL,cAAA;cACA,IAAAO,cAAA;gBACAvI,SAAA,CAAApD,IAAA,CAAA2L,cAAA;cACA;YACA,SAAAtP,KAAA;cACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAyM,cAAA,uCAAAzM,MAAA,CAAAtC,KAAA,CAAAyO,OAAA;YAEA;UACA;;UAEA;UACAK,oBAAA,IAAAF,IAAA;UACAG,cAAA;QACA;UACA;UACA,IAAAD,oBAAA,CAAA3M,MAAA;YACA2M,oBAAA,CAAAnL,IAAA,CAAAiL,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAE,oBAAA,CAAA3M,MAAA;QACA;UACA,IAAAiN,aAAA,GAAAN,oBAAA,CAAAO,IAAA;UACA,IAAAC,eAAA,QAAAC,sBAAA,CAAAH,aAAA,EAAAL,cAAA;UACA,IAAAO,eAAA;YACAvI,SAAA,CAAApD,IAAA,CAAA2L,eAAA;UACA;QACA,SAAAtP,KAAA;UACAkH,MAAA,CAAAvD,IAAA,WAAArB,MAAA,CAAAyM,cAAA,uCAAAzM,MAAA,CAAAtC,KAAA,CAAAyO,OAAA;QAEA;MACA;IAEA,SAAAzO,KAAA;MACAkH,MAAA,CAAAvD,IAAA,0CAAArB,MAAA,CAAAtC,KAAA,CAAAyO,OAAA;MACAe,OAAA,CAAAxP,KAAA,cAAAA,KAAA;IACA;IAEAwP,OAAA,CAAAC,GAAA,WAAA1I,SAAA,CAAA5E,MAAA,UAAA+E,MAAA,CAAA/E,MAAA;IACA;MAAA4E,SAAA,EAAAA,SAAA;MAAAG,MAAA,EAAAA;IAAA;EACA,oCAGAgI,oBAAAN,IAAA;IACA;IACA;IACA;IACA,wBAAAc,IAAA,CAAAd,IAAA;EACA,oCAGAO,oBAAAP,IAAA;IACA;IACA;IACA,mBAAAc,IAAA,CAAAd,IAAA;EACA,uCAGAW,uBAAAH,YAAA;IACA,IAAAV,KAAA,GAAAU,YAAA,CAAAT,KAAA,OAAAlM,GAAA,WAAAmM,IAAA;MAAA,OAAAA,IAAA,CAAArQ,IAAA;IAAA,GAAAsQ,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAAzM,MAAA;IAAA;IAEA,IAAAuM,KAAA,CAAAvM,MAAA;MACA,UAAA+G,KAAA;IACA;IAEA,IAAAhM,YAAA;IACA,IAAAE,eAAA;IACA,IAAAuS,iBAAA;;IAEA;IACA,SAAAX,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;MACA,IAAAY,SAAA,GAAAhB,IAAA,CAAAiB,KAAA;MACA,IAAAD,SAAA;QACA,IAAAE,QAAA,GAAAF,SAAA;;QAEA;QACA,IAAAE,QAAA,CAAApM,QAAA;UACAxG,YAAA;QACA,WAAA4S,QAAA,CAAApM,QAAA;UACAxG,YAAA;QACA,WAAA4S,QAAA,CAAApM,QAAA;UACAxG,YAAA;QACA,WAAA4S,QAAA,CAAApM,QAAA;UACAxG,YAAA;QACA,WAAA4S,QAAA,CAAApM,QAAA;UACAxG,YAAA;QACA;;QAEA;QACA,IAAA6S,gBAAA,GAAAnB,IAAA,CAAAjK,OAAA,iBAAApG,IAAA;QACA,IAAAwR,gBAAA;UACA3S,eAAA,GAAA2S,gBAAA;UACAJ,iBAAA,GAAAX,CAAA;QACA;UACAW,iBAAA,GAAAX,CAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,IAAAW,iBAAA;MACAA,iBAAA;IACA;;IAEA;IACA,SAAAX,EAAA,GAAAW,iBAAA,EAAAX,EAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,EAAA;MACA,IAAAJ,KAAA,GAAAF,KAAA,CAAAM,EAAA;;MAEA;MACA,SAAAE,mBAAA,CAAAN,KAAA;QACA;QACAxR,eAAA,GAAAwR,KAAA,CAAAjK,OAAA,uBAAApG,IAAA;QACAoR,iBAAA,GAAAX,EAAA;QACA;MACA,YAAA5R,eAAA;QACA;QACAA,eAAA,GAAAwR,KAAA;QACAe,iBAAA,GAAAX,EAAA;QACA;MACA;IACA;;IAEA;IACA,SAAAA,GAAA,GAAAW,iBAAA,EAAAX,GAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,GAAA;MACA,IAAAJ,MAAA,GAAAF,KAAA,CAAAM,GAAA;;MAEA;MACA,SAAAgB,YAAA,CAAApB,MAAA,UAAAqB,YAAA,CAAArB,MAAA,KACA,KAAAsB,iBAAA,CAAAtB,MAAA,UAAAuB,gBAAA,CAAAvB,MAAA;QACA;MACA;;MAEA;MACA,IAAAwB,SAAA,GAAAxB,MAAA;MACA;MACA,SAAAM,mBAAA,CAAAN,MAAA;QACAwB,SAAA,GAAAxB,MAAA,CAAAjK,OAAA,uBAAApG,IAAA;MACA;MAEA,IAAA6R,SAAA;QACA,IAAAhT,eAAA;UACAA,eAAA,WAAAgT,SAAA;QACA;UACAhT,eAAA,GAAAgT,SAAA;QACA;MACA;IACA;IAEA,KAAAhT,eAAA;MACA,UAAA8L,KAAA;IACA;;IAEA;IACA,IAAAmH,oBAAA,GAAAjT,eAAA,CAAAmB,IAAA;IACA;IACA,wBAAAmR,IAAA,CAAAW,oBAAA;MACAA,oBAAA,GAAAA,oBAAA,CAAA1L,OAAA,0BAAApG,IAAA;IACA;;IAEA;IACA,IAAA8R,oBAAA,CAAA3M,QAAA;MACA2M,oBAAA,QAAAC,oBAAA,CAAAD,oBAAA;IACA;IAEA,IAAApM,QAAA;MACA/G,YAAA,EAAAA,YAAA;MACA0E,IAAA,EAAA1E,YAAA;MACAqT,QAAA,OAAAC,kBAAA,CAAAtT,YAAA;MACAE,eAAA,EAAAiT,oBAAA;MACAhD,OAAA,EAAAgD,oBAAA;MACAlT,UAAA;MAAA;MACAsT,WAAA;MACAC,OAAA;MACAC,aAAA;MACA3J,SAAA;IACA;;IAEA;IACA,IAAA4J,YAAA,QAAAC,qBAAA,CAAAnC,KAAA;IACAzK,QAAA,CAAAyM,OAAA,GAAAE,YAAA,CAAAF,OAAA;;IAEA;IACA,IAAAxT,YAAA,mBAAA+G,QAAA,CAAAyM,OAAA,CAAAvO,MAAA;MACA;MACAjF,YAAA;MACA+G,QAAA,CAAA/G,YAAA,GAAAA,YAAA;MACA+G,QAAA,CAAArC,IAAA,GAAA1E,YAAA;MACA+G,QAAA,CAAAsM,QAAA,QAAAC,kBAAA,CAAAtT,YAAA;IACA;;IAEA;IACA,KAAA4T,0BAAA,CAAApC,KAAA,EAAAzK,QAAA;;IAEA;IACA,IAAA/G,YAAA,iBAAA+G,QAAA,CAAA0M,aAAA,IAAA1M,QAAA,CAAA0M,aAAA,CAAAxO,MAAA;MACA;MACA,kBAAAuN,IAAA,CAAAzL,QAAA,CAAA0M,aAAA;QACAzT,YAAA;QACA+G,QAAA,CAAA/G,YAAA,GAAAA,YAAA;QACA+G,QAAA,CAAArC,IAAA,GAAA1E,YAAA;QACA+G,QAAA,CAAAsM,QAAA,QAAAC,kBAAA,CAAAtT,YAAA;MACA;IACA;;IAEA;IACA+G,QAAA,CAAA7G,eAAA,QAAAkT,oBAAA,CAAArM,QAAA,CAAA7G,eAAA;IACA6G,QAAA,CAAAoJ,OAAA,GAAApJ,QAAA,CAAA7G,eAAA;IAEA,OAAA6G,QAAA;EACA,6BAGA+L,aAAApB,IAAA;IACA;IACA,6BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAqB,aAAArB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,kCAGAsB,kBAAAtB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,iCAGAuB,iBAAAvB,IAAA;IACA;IACA,sBAAAc,IAAA,CAAAd,IAAA;EACA,mCAGA4B,mBAAA5O,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,kCAGAmP,kBAAA1D,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAA2D,gBAAA,GAAA3D,OAAA,CAAA1I,OAAA,mDAAAkL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,SAAArB,KAAA;;QAGA;QACA,IAAAqB,GAAA,CAAAzF,UAAA,eAAAyF,GAAA,CAAAzF,UAAA,gBAAAyF,GAAA,CAAAzF,UAAA;UACA,OAAAoE,KAAA;QACA;;QAEA;QACA,IAAAuB,OAAA,8BAAAF,GAAA,CAAAzF,UAAA,QAAAyF,GAAA,SAAAA,GAAA;QACA,IAAAG,MAAA,UAAA/O,MAAA,CAAA2O,MAAA,YAAA3O,MAAA,CAAA8O,OAAA,QAAA9O,MAAA,CAAA6O,KAAA;QACA,OAAAE,MAAA;MACA;MAEA,OAAAL,gBAAA;IACA,SAAAhR,KAAA;MACAwP,OAAA,CAAAxP,KAAA,iBAAAA,KAAA;MACA,OAAAqN,OAAA;IACA;EACA,QAAA9P,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,yCAGAoQ,2BAAAkB,OAAA;IAAA,IAAAiE,OAAA;IACA,KAAAjE,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MACA;MACA,IAAA2D,gBAAA,GAAA3D;MACA;MAAA,CACA1I,OAAA,oDAAAkL,KAAA,EAAAoB,MAAA,EAAAC,GAAA,EAAAC,KAAA;QACA,KAAAD,GAAA,CAAAzF,UAAA,aAAAyF,GAAA,CAAAzF,UAAA;UACA,IAAA2F,OAAA,GAAAE,OAAA,CAAAP,iBAAA,CAAAG,GAAA;UACA,cAAA5O,MAAA,CAAA2O,MAAA,YAAA3O,MAAA,CAAA8O,OAAA,QAAA9O,MAAA,CAAA6O,KAAA;QACA;QACA,OAAAtB,KAAA;MACA;MACA;MAAA,CACAlL,OAAA,sBACAA,OAAA;MACA;MAAA,CACAA,OAAA;MACA;MAAA,CACAA,OAAA,sBACAA,OAAA;MAEA,OAAAqM,gBAAA,CAAAzS,IAAA;IACA,SAAAyB,KAAA;MACAwP,OAAA,CAAAxP,KAAA,qCAAAA,KAAA;MACA,OAAAqN,OAAA;IACA;EACA,wCAGAjB,wBAAAiB,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA;IACA;IAEA;MAEA;MACA,IAAAkE,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,uBAAA,GAAApE,OAAA,CAAA1I,OAAA,2BAAAkL,KAAA;QACA0B,MAAA,CAAA5N,IAAA,CAAAkM,KAAA;QACA,gCAAAvN,MAAA,CAAAkP,UAAA;MACA;;MAEA;MACA,IAAArE,WAAA,GAAAsE,uBAAA,CACA9M,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;MAAA,CACAA,OAAA;;MAEA;MACA,IAAA+M,YAAA,GAAAvE,WAAA;MACAoE,MAAA,CAAAnQ,OAAA,WAAAuQ,GAAA,EAAA/N,KAAA;QACA,IAAA6I,WAAA,0BAAAnK,MAAA,CAAAsB,KAAA;QACA,IAAA8N,YAAA,CAAAhO,QAAA,CAAA+I,WAAA;UACAiF,YAAA,GAAAA,YAAA,CAAA/M,OAAA,CAAA8H,WAAA,EAAAkF,GAAA;QACA;MACA;MAEA,OAAAD,YAAA,CAAAnT,IAAA;IACA,SAAAyB,KAAA;MACAwP,OAAA,CAAAxP,KAAA,kCAAAA,KAAA;MACA,OAAAqN,OAAA;IACA;EACA,sCAGAwD,sBAAAnC,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IAEA,KAAA7C,KAAA,CAAAgE,OAAA,CAAAnD,KAAA,KAAAkD,UAAA,QAAAA,UAAA,IAAAlD,KAAA,CAAAvM,MAAA;MACAqN,OAAA,CAAAsC,IAAA;MACA;QAAApB,OAAA,EAAAA;MAAA;IACA;IAEA;MACA,SAAA1B,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,CAAA;QACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;QAEA,KAAAJ,IAAA,WAAAA,IAAA;UACA;QACA;;QAEA;QACA,IAAAmD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;QACA,IAAAkC,WAAA;UACA,IAAAC,SAAA,GAAAD,WAAA,IAAAE,WAAA;UACA,IAAAC,aAAA,GAAAH,WAAA,MAAAA,WAAA,IAAAxT,IAAA;UAEA,IAAAyT,SAAA,IAAAE,aAAA;YACAxB,OAAA,CAAA/M,IAAA;cACAqO,SAAA,EAAAA,SAAA;cACAG,KAAA,EAAAH,SAAA;cACAE,aAAA,EAAAA,aAAA;cACA7E,OAAA,EAAA6E;YACA;UACA;QACA,gBAAAjC,YAAA,CAAArB,IAAA,UAAAsB,iBAAA,CAAAtB,IAAA,UAAAuB,gBAAA,CAAAvB,IAAA;UACA;UACA;QACA;UACA;UACA;UACA,IAAAwD,oBAAA,GAAAxD,IAAA,CAAAiB,KAAA;UACA,IAAAuC,oBAAA;YACA;YACA,IAAAC,aAAA,GAAAzD,IAAA,CAAAD,KAAA;YAAA,IAAA2D,SAAA,OAAAC,2BAAA,CAAA/U,OAAA,EACA6U,aAAA;cAAAG,KAAA;YAAA;cAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA1J,CAAA,IAAA3D,IAAA;gBAAA,IAAAyN,YAAA,GAAAF,KAAA,CAAA9F,KAAA;gBACA,KAAAgG,YAAA;gBAEA,IAAA7C,KAAA,GAAA6C,YAAA,CAAA7C,KAAA;gBACA,IAAAA,KAAA;kBACA,IAAAmC,UAAA,GAAAnC,KAAA,IAAAoC,WAAA;kBACA,IAAAC,cAAA,GAAArC,KAAA,MAAAA,KAAA,IAAAtR,IAAA;kBAEA,IAAAyT,UAAA,IAAAE,cAAA;oBACAxB,OAAA,CAAA/M,IAAA;sBACAqO,SAAA,EAAAA,UAAA;sBACAG,KAAA,EAAAH,UAAA;sBACAE,aAAA,EAAAA,cAAA;sBACA7E,OAAA,EAAA6E;oBACA;kBACA;gBACA;cACA;YAAA,SAAAS,GAAA;cAAAL,SAAA,CAAA1G,CAAA,CAAA+G,GAAA;YAAA;cAAAL,SAAA,CAAAM,CAAA;YAAA;UACA;QACA;MACA;IACA,SAAA5S,KAAA;MACAwP,OAAA,CAAAxP,KAAA,eAAAA,KAAA;IACA;IAEA;MAAA0Q,OAAA,EAAAA;IAAA;EACA,2CAGAI,2BAAApC,KAAA,EAAAzK,QAAA;IACA,SAAA+K,CAAA,MAAAA,CAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACA5O,QAAA,CAAA0M,aAAA,QAAAmC,gBAAA,CAAAD,WAAA,KAAA5O,QAAA,CAAA/G,YAAA;QACA;MACA;;MAEA;MACA,IAAA6V,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACA9O,QAAA,CAAAwM,WAAA,GAAAsC,gBAAA,IAAAxU,IAAA;QACA;MACA;;MAEA;MACA,IAAAyU,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAA7V,UAAA,GAAA6V,eAAA;QACA;QACA,IAAA7V,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAuG,QAAA,CAAAvG,UAAA;UACA8G,QAAA,CAAA9G,UAAA,GAAAA,UAAA;QACA;UACAqS,OAAA,CAAAsC,IAAA,iBAAA3U,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA;IACA,KAAA8G,QAAA,CAAA0M,aAAA;MACA1M,QAAA,CAAA0M,aAAA,QAAAsC,gCAAA,CAAAhP,QAAA,CAAA7G,eAAA,EAAA6G,QAAA,CAAA/G,YAAA;IACA;EACA,iDAGA+V,iCAAA7V,eAAA,EAAAF,YAAA;IACA,KAAAE,eAAA,WAAAA,eAAA;MACA;IACA;IAEA;MACA;MACA,IAAA8V,QAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MAEA,SAAAC,GAAA,MAAAC,SAAA,GAAAF,QAAA,EAAAC,GAAA,GAAAC,SAAA,CAAAjR,MAAA,EAAAgR,GAAA;QAAA,IAAAE,OAAA,GAAAD,SAAA,CAAAD,GAAA;QACA,IAAAG,OAAA,GAAAlW,eAAA,CAAAyS,KAAA,CAAAwD,OAAA;QACA,IAAAC,OAAA,IAAAA,OAAA,CAAAnR,MAAA;UACA;UACA,IAAAoR,SAAA,GAAAD,OAAA,CAAAA,OAAA,CAAAnR,MAAA;UACA,IAAAqR,MAAA,GAAAD,SAAA,CAAA5O,OAAA,sBAAApG,IAAA;UAEA,IAAAiV,MAAA;YACA,YAAAV,gBAAA,CAAAU,MAAA,EAAAtW,YAAA;UACA;QACA;MACA;IACA,SAAA8C,KAAA;MACAwP,OAAA,CAAAxP,KAAA,kBAAAA,KAAA;IACA;IAEA;EACA,iCAGA8S,iBAAAW,UAAA,EAAAvW,YAAA;IACA,KAAAuW,UAAA,WAAAA,UAAA;MACA;IACA;IAEA;MACA,IAAAC,aAAA,GAAAD,UAAA,CAAAlV,IAAA;MAEA,KAAAmV,aAAA;QACA;MACA;MAEA,IAAAxW,YAAA;QACA;QACA,OAAAwW,aAAA;MACA;QACA;QACA,OAAAA,aAAA,CAAAzB,WAAA;MACA;IACA,SAAAjS,KAAA;MACAwP,OAAA,CAAAxP,KAAA,gBAAAA,KAAA;MACA,OAAAyT,UAAA;IACA;EACA,oCAGAE,oBAAAtG,OAAA;IACA,IAAAuG,QAAA;IACA,IAAAC,SAAA;IAEA,IAAAC,SAAA;IACA,IAAAjE,KAAA;IACA,IAAAkE,WAAA;IAEA,QAAAlE,KAAA,GAAAgE,SAAA,CAAAG,IAAA,CAAA3G,OAAA;MACA,IAAA0G,WAAA;QACA;QACAH,QAAA,CAAAjQ,IAAA;UACA/B,IAAA,EAAAmS,WAAA;UACA1G,OAAA,EAAAA,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAjE,KAAA,CAAAjM,KAAA,EAAArF,IAAA;QACA;MACA;MACAwV,WAAA,GAAAlE,KAAA;MACAiE,SAAA,GAAAjE,KAAA,CAAAjM,KAAA,GAAAiM,KAAA,IAAA1N,MAAA;IACA;;IAEA;IACA,IAAA4R,WAAA;MACAH,QAAA,CAAAjQ,IAAA;QACA/B,IAAA,EAAAmS,WAAA;QACA1G,OAAA,EAAAA,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAvV,IAAA;MACA;IACA;IAEA,OAAAqV,QAAA;EACA,sCAGAK,sBAAAC,OAAA;IAAA,IAAAC,OAAA;IACA,IAAApN,SAAA;IACA,IAAA7J,YAAA,QAAAkX,mBAAA,CAAAF,OAAA,CAAAtS,IAAA;;IAEA;IACA,IAAAyS,cAAA,QAAAC,qBAAA,CAAAJ,OAAA,CAAA7G,OAAA;IAEAgH,cAAA,CAAAjT,OAAA,WAAAmT,KAAA,EAAA3Q,KAAA;MACA;QACA,IAAAK,QAAA,GAAAkQ,OAAA,CAAAK,kBAAA,CAAAD,KAAA,EAAArX,YAAA,EAAA0G,KAAA;QACA,IAAAK,QAAA;UACA8C,SAAA,CAAApD,IAAA,CAAAM,QAAA;QACA;MACA,SAAAjE,KAAA;QACA,UAAAkJ,KAAA,UAAA5G,MAAA,CAAAsB,KAAA,0CAAAtB,MAAA,CAAAtC,KAAA,CAAAyO,OAAA;MACA;IACA;IAEA,OAAA1H,SAAA;EACA,sCAGAuN,sBAAAjH,OAAA;IACA,IAAAoH,MAAA;IACA,IAAAC,WAAA;IAEA,IAAAZ,SAAA;IACA,IAAAjE,KAAA;IAEA,QAAAA,KAAA,GAAA6E,WAAA,CAAAV,IAAA,CAAA3G,OAAA;MACA,IAAAyG,SAAA;QACA;QACAW,MAAA,CAAA9Q,IAAA,CAAA0J,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAjE,KAAA,CAAAjM,KAAA,EAAArF,IAAA;MACA;MACAuV,SAAA,GAAAjE,KAAA,CAAAjM,KAAA;IACA;;IAEA;IACA,IAAAkQ,SAAA,GAAAzG,OAAA,CAAAlL,MAAA;MACAsS,MAAA,CAAA9Q,IAAA,CAAA0J,OAAA,CAAAxI,SAAA,CAAAiP,SAAA,EAAAvV,IAAA;IACA;IAEA,OAAAkW,MAAA,CAAA5F,MAAA,WAAA0F,KAAA;MAAA,OAAAA,KAAA,CAAApS,MAAA;IAAA;EACA,mCAGAqS,mBAAAD,KAAA,EAAArX,YAAA;IACA,IAAAwR,KAAA,GAAA6F,KAAA,CAAA5F,KAAA,OAAAlM,GAAA,WAAAmM,IAAA;MAAA,OAAAA,IAAA,CAAArQ,IAAA;IAAA,GAAAsQ,MAAA,WAAAD,IAAA;MAAA,OAAAA,IAAA,CAAAzM,MAAA;IAAA;IAEA,IAAAuM,KAAA,CAAAvM,MAAA;MACA,UAAA+G,KAAA;IACA;;IAEA;IACA,IAAAyL,SAAA,GAAAjG,KAAA;IACA,IAAAtR,eAAA;IACA,IAAAwX,gBAAA;;IAEA;IACA,IAAAC,WAAA,GAAAF,SAAA,CAAA9E,KAAA;IACA,IAAAgF,WAAA;MACAzX,eAAA,GAAAyX,WAAA,IAAAtW,IAAA;MACAqW,gBAAA;IACA;MACA;MACAxX,eAAA,QAAAkT,oBAAA,CAAAqE,SAAA,EAAApW,IAAA;MACAqW,gBAAA;IACA;;IAEA;IACA,OAAAA,gBAAA,GAAAlG,KAAA,CAAAvM,MAAA;MACA,IAAAyM,IAAA,GAAAF,KAAA,CAAAkG,gBAAA;MACA,SAAA5E,YAAA,CAAApB,IAAA;QACA;MACA;MACAxR,eAAA,WAAAwR,IAAA;MACAgG,gBAAA;IACA;IAEA,IAAA3Q,QAAA;MACA/G,YAAA,EAAAA,YAAA;MACAE,eAAA,EAAAA,eAAA,CAAAmB,IAAA;MACApB,UAAA;MAAA;MACAsT,WAAA;MACAC,OAAA;MACAC,aAAA;IACA;;IAEA;IACA,IAAAzT,YAAA;MACA,IAAA0T,YAAA,QAAAkE,YAAA,CAAApG,KAAA,EAAAkG,gBAAA;MACA3Q,QAAA,CAAAyM,OAAA,GAAAE,YAAA,CAAAF,OAAA;MACAkE,gBAAA,GAAAhE,YAAA,CAAAmE,SAAA;IACA;;IAEA;IACA,KAAAC,iBAAA,CAAAtG,KAAA,EAAAkG,gBAAA,EAAA3Q,QAAA;;IAEA;IACAA,QAAA,CAAA7G,eAAA,QAAAkT,oBAAA,CAAArM,QAAA,CAAA7G,eAAA;IAEA,OAAA6G,QAAA;EACA,QAAA1G,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,2BAAAiU,aAGApB,IAAA;IACA,4BAAAc,IAAA,CAAAd,IAAA;EACA,6BAGAkG,aAAApG,KAAA,EAAAkD,UAAA;IACA,IAAAlB,OAAA;IACA,IAAAuE,YAAA,GAAArD,UAAA;IAEA,OAAAqD,YAAA,GAAAvG,KAAA,CAAAvM,MAAA;MACA,IAAAyM,IAAA,GAAAF,KAAA,CAAAuG,YAAA;MACA,IAAAlD,WAAA,GAAAnD,IAAA,CAAAiB,KAAA;MAEA,KAAAkC,WAAA;QACA;MACA;MAEArB,OAAA,CAAA/M,IAAA;QACAqO,SAAA,EAAAD,WAAA,IAAAE,WAAA;QACAC,aAAA,EAAAH,WAAA,IAAAxT,IAAA;MACA;MAEA0W,YAAA;IACA;IAEA;MAAAvE,OAAA,EAAAA,OAAA;MAAAqE,SAAA,EAAAE;IAAA;EACA,kCAGAD,kBAAAtG,KAAA,EAAAkD,UAAA,EAAA3N,QAAA;IACA,SAAA+K,CAAA,GAAA4C,UAAA,EAAA5C,CAAA,GAAAN,KAAA,CAAAvM,MAAA,EAAA6M,CAAA;MACA,IAAAJ,IAAA,GAAAF,KAAA,CAAAM,CAAA;;MAEA;MACA,IAAA6D,WAAA,GAAAjE,IAAA,CAAAiB,KAAA;MACA,IAAAgD,WAAA;QACA5O,QAAA,CAAA0M,aAAA,QAAAuE,WAAA,CAAArC,WAAA,KAAA5O,QAAA,CAAA/G,YAAA;QACA;MACA;;MAEA;MACA,IAAA6V,gBAAA,GAAAnE,IAAA,CAAAiB,KAAA;MACA,IAAAkD,gBAAA;QACA9O,QAAA,CAAAwM,WAAA,GAAAsC,gBAAA,IAAAxU,IAAA;QACA;MACA;;MAEA;MACA,IAAAyU,eAAA,GAAApE,IAAA,CAAAiB,KAAA;MACA,IAAAmD,eAAA;QACA,IAAA7V,UAAA,GAAA6V,eAAA;QACA;QACA,IAAA7V,UAAA;UACAA,UAAA;QACA;QACA;QACA,uBAAAuG,QAAA,CAAAvG,UAAA;UACA8G,QAAA,CAAA9G,UAAA,GAAAA,UAAA;QACA;QACA;MACA;IACA;;IAEA;IACA,KAAA8G,QAAA,CAAA0M,aAAA;MACA1M,QAAA,CAAA0M,aAAA,QAAAwE,wBAAA,CAAAlR,QAAA,CAAA7G,eAAA,EAAA6G,QAAA,CAAA/G,YAAA;IACA;EACA,yCAKAiY,yBAAA9H,OAAA,EAAAnQ,YAAA;IACA;IACA,IAAAkY,eAAA,IACA,cACA,iBACA,cACA,eACA;IAEA,SAAAC,GAAA,MAAAC,gBAAA,GAAAF,eAAA,EAAAC,GAAA,GAAAC,gBAAA,CAAAnT,MAAA,EAAAkT,GAAA;MAAA,IAAAhC,OAAA,GAAAiC,gBAAA,CAAAD,GAAA;MACA,IAAA/B,OAAA,OAAAxK,mBAAA,CAAAtL,OAAA,EAAA6P,OAAA,CAAAkI,QAAA,CAAAlC,OAAA;MACA,IAAAC,OAAA,CAAAnR,MAAA;QACA,IAAAqR,MAAA,GAAAF,OAAA,CAAAA,OAAA,CAAAnR,MAAA;QACA,YAAA+S,WAAA,CAAA1B,MAAA,EAAAtW,YAAA;MACA;IACA;IAEA;EACA,oCAGAkX,oBAAAtE,QAAA;IACA,IAAA9O,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAA8O,QAAA;EACA,oCAGA0F,oBAAA5T,IAAA;IACA,IAAAZ,OAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,OAAA,CAAAY,IAAA;EACA,qCAGA6T,qBAAA7T,IAAA;IACA,IAAA8T,QAAA;MACA;MACA;MACA;IACA;IACA,OAAAA,QAAA,CAAA9T,IAAA;EACA,4CAOA+T,4BAAA1R,QAAA;IACA,KAAAA,QAAA,KAAAA,QAAA,CAAA7G,eAAA;MACA;IACA;IAEA,IAAAiQ,OAAA,GAAApJ,QAAA,CAAA7G,eAAA;;IAEA;IACA,SAAAkK,mBAAA,SAAAA,mBAAA,CAAA5D,QAAA;MACA;MACA,IAAAkS,WAAA,QAAAC,uBAAA,CAAA5R,QAAA,CAAA7G,eAAA,OAAAkK,mBAAA;MACA,IAAAsO,WAAA;QACAvI,OAAA,GAAAuI,WAAA;MACA;IACA;;IAEA;IACAvI,OAAA,QAAAiD,oBAAA,CAAAjD,OAAA;IAEA,YAAA0D,iBAAA,CAAA1D,OAAA;EACA,qCAGAiD,qBAAAjD,OAAA;IACA,KAAAA,OAAA,WAAAA,OAAA;MACA,OAAAA,OAAA;IACA;;IAEA;IACA,IAAAA,OAAA,CAAA3J,QAAA;MACA;MACA,OAAA2J,OAAA,CAAA1I,OAAA,wDACAA,OAAA;MAAA,CACAA,OAAA;IACA;MACA;MACA,OAAA0I,OAAA,CAAA1I,OAAA,0BAAApG,IAAA;IACA;EACA,wCAGAsX,wBAAAC,YAAA,EAAAF,WAAA;IACA,KAAAE,YAAA,KAAAF,WAAA;MACA,OAAAE,YAAA;IACA;IAEA;MACA;MACA,IAAAC,SAAA,GAAAD,YAAA,CAAAnR,OAAA,uBAAApG,IAAA;;MAEA;MACA,IAAAyX,UAAA,GAAAJ,WAAA,CAAA/F,KAAA;MAAA,IAAAoG,UAAA,OAAA1D,2BAAA,CAAA/U,OAAA,EAEAwY,UAAA;QAAAE,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAxD,CAAA,MAAAyD,MAAA,GAAAD,UAAA,CAAArN,CAAA,IAAA3D,IAAA;UAAA,IAAAkR,SAAA,GAAAD,MAAA,CAAAxJ,KAAA;UACA,IAAA0J,aAAA,GAAAD,SAAA,CAAAxR,OAAA,iBAAApG,IAAA;UACA;UACA,IAAA8X,kBAAA,GAAAD,aAAA,CAAAzR,OAAA,0BAAApG,IAAA;UACA,IAAA8X,kBAAA,CAAA3S,QAAA,CAAAqS,SAAA,CAAAlR,SAAA;YACA;YACA,YAAAyL,oBAAA,CAAA6F,SAAA;UACA;QACA;;QAEA;MAAA,SAAAxD,GAAA;QAAAsD,UAAA,CAAArK,CAAA,CAAA+G,GAAA;MAAA;QAAAsD,UAAA,CAAArD,CAAA;MAAA;MACA,OAAAkD,YAAA;IACA,SAAA9V,KAAA;MACAwP,OAAA,CAAAxP,KAAA,kBAAAA,KAAA;MACA,OAAA8V,YAAA;IACA;EACA,QAAAvY,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzB,QAAA,2BAIAua,aAAA;IACA,KAAAvZ,WAAA,CAAAC,OAAA;IACA,KAAAkD,eAAA;EACA,4BAEAqW,YAAA;IACA,KAAAxZ,WAAA,CAAAG,YAAA;IACA,KAAAH,WAAA,CAAAI,UAAA;IACA,KAAAJ,WAAA,CAAAK,eAAA;IACA,KAAAL,WAAA,CAAAC,OAAA;IACA,KAAAkD,eAAA;EACA;AAEA", "ignoreList": []}]}