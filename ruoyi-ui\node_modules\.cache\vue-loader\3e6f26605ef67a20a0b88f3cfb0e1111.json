{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue?vue&type=style&index=0&id=640f7d0c&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmVkaXRvci1jb250YWluZXIgewogIGJvcmRlcjogMXB4IHNvbGlkICNkY2RmZTY7CiAgYm9yZGVyLXJhZGl1czogNHB4Owp9CgouZWRpdG9yIHsKICBtaW4taGVpZ2h0OiAyMDBweDsKfQoKLm9wdGlvbnMtY29udGFpbmVyIHsKICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiAxNXB4Owp9Cgoub3B0aW9uLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5vcHRpb24taXRlbTpsYXN0LWNoaWxkIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9Cgoub3B0aW9uLXJvdyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICBnYXA6IDE1cHg7Cn0KCi5vcHRpb24tYW5zd2VyIHsKICBmbGV4LXNocmluazogMDsKICB3aWR0aDogNjBweDsKICBwYWRkaW5nLXRvcDogOHB4Owp9Cgoub3B0aW9uLWNvbnRlbnQgewogIGZsZXg6IDE7Cn0KCi5vcHRpb24tYWN0aW9ucyB7CiAgZmxleC1zaHJpbms6IDA7CiAgd2lkdGg6IDYwcHg7CiAgcGFkZGluZy10b3A6IDhweDsKICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCgo="}, {"version": 3, "sources": ["QuestionForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "QuestionForm.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"dialogTitle\"\n    :visible.sync=\"dialogVisible\"\n    width=\"55%\"\n    :before-close=\"handleBeforeClose\"\n    :close-on-click-modal=\"false\"\n    :close-on-press-escape=\"false\"\n    append-to-body\n  >\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n      <!-- 基本信息 -->\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"题型\" prop=\"questionType\">\n            <el-select v-model=\"form.questionType\" placeholder=\"请选择题型\" style=\"width: 100%;\" :disabled=\"isEdit\">\n              <el-option label=\"单选题\" value=\"single\"></el-option>\n              <el-option label=\"多选题\" value=\"multiple\"></el-option>\n              <el-option label=\"判断题\" value=\"judgment\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"难度\" prop=\"difficulty\">\n            <el-radio-group v-model=\"form.difficulty\">\n              <el-radio-button label=\"简单\">简单</el-radio-button>\n              <el-radio-button label=\"中等\">中等</el-radio-button>\n              <el-radio-button label=\"困难\">困难</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 题干 -->\n      <el-form-item label=\"题干\" prop=\"questionContent\">\n        <option-editor\n          v-model=\"form.questionContent\"\n          :min-height=\"100\"\n          placeholder=\"请输入题干内容\"\n          editor-id=\"question-content\"\n        />\n      </el-form-item>\n\n\n\n      <!-- 选择题选项 -->\n      <div v-if=\"isChoiceQuestion\">\n        <el-form-item label=\"选项设置\" required>\n          <div class=\"options-container\">\n            <div\n              v-for=\"(option, index) in form.options\"\n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <div class=\"option-row\">\n                <div class=\"option-answer\">\n                  <el-checkbox\n                    v-if=\"form.questionType === 'single'\"\n                    :value=\"form.correctAnswer === option.optionKey\"\n                    @change=\"handleSingleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                  <el-checkbox\n                    v-else-if=\"form.questionType === 'multiple'\"\n                    :value=\"isMultipleCorrect(option.optionKey)\"\n                    @change=\"handleMultipleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                </div>\n                <div class=\"option-content\">\n                  <option-editor\n                    :key=\"`option-${option.optionKey}-${form.questionType}`\"\n                    v-model=\"option.optionContent\"\n                    :min-height=\"50\"\n                    :placeholder=\"`请输入选项${option.optionKey}内容`\"\n                    :editor-id=\"`option-${option.optionKey}-${form.questionType}`\"\n                  />\n                </div>\n                <div class=\"option-actions\">\n                  <el-button\n                    v-if=\"form.options.length > 2\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"removeOption(index)\"\n                    style=\"color: #F56C6C;\"\n                    size=\"mini\"\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </div>\n            <el-button\n              v-if=\"form.options.length < 8\"\n              type=\"dashed\"\n              icon=\"el-icon-plus\"\n              @click=\"addOption\"\n              style=\"width: 100%; margin-top: 10px;\"\n            >\n              添加选项\n            </el-button>\n          </div>\n        </el-form-item>\n      </div>\n\n      <!-- 判断题答案 -->\n      <div v-if=\"form.questionType === 'judgment'\">\n        <el-form-item label=\"正确答案\" prop=\"correctAnswer\">\n          <el-radio-group v-model=\"form.correctAnswer\">\n            <el-radio label=\"true\">正确</el-radio>\n            <el-radio label=\"false\">错误</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </div>\n\n      <!-- 题目解析 -->\n      <el-form-item label=\"解析\">\n        <option-editor\n          v-model=\"form.explanation\"\n          :min-height=\"100\"\n          placeholder=\"请输入解析内容\"\n          editor-id=\"question-explanation\"\n        />\n      </el-form-item>\n\n\n    </el-form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleCancel\" :disabled=\"submitting\">取消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n        {{ isEdit ? '更新' : '保存' }}\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { addQuestion, updateQuestion } from '@/api/biz/question'\nimport OptionEditor from './OptionEditor'\n\nexport default {\n  name: \"QuestionForm\",\n  components: {\n    OptionEditor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    questionType: {\n      type: String,\n      default: 'single'\n    },\n    questionData: {\n      type: Object,\n      default: null\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      form: {\n        questionId: null,\n        bankId: null,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      },\n      rules: {\n        questionType: [\n          { required: true, message: '请选择题型', trigger: 'change' }\n        ],\n        questionContent: [\n          { required: true, message: '请输入题目内容', trigger: 'blur' }\n        ],\n        difficulty: [\n          { required: true, message: '请选择难度系数', trigger: 'change' }\n        ],\n        correctAnswer: [\n          { required: true, message: '请设置正确答案', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  computed: {\n    dialogTitle() {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      const typeName = typeMap[this.form.questionType] || '题目'\n      return this.isEdit ? `编辑${typeName}` : `创建${typeName}`\n    },\n    isEdit() {\n      return this.questionData && this.questionData.questionId\n    },\n    isChoiceQuestion() {\n      return ['single', 'multiple'].includes(this.form.questionType)\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    'form.questionType'(newType) {\n      this.handleQuestionTypeChange(newType)\n    },\n    questionData: {\n      handler(newData) {\n        if (newData && this.dialogVisible) {\n          this.initForm()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    // 初始化表单\n    initForm() {\n      if (this.questionData) {\n        this.form = this.convertQuestionDataForForm(this.questionData)\n      } else {\n        this.resetForm()\n        this.form.questionType = this.questionType\n        this.form.bankId = this.bankId\n      }\n      this.handleQuestionTypeChange(this.form.questionType)\n    },\n\n    // 转换后端数据为表单格式\n    convertQuestionDataForForm(questionData) {\n      const formData = {\n        questionId: questionData.questionId,\n        bankId: questionData.bankId,\n        questionType: this.convertQuestionTypeToString(questionData.questionType),\n        difficulty: this.convertDifficultyToString(questionData.difficulty),\n        questionContent: questionData.questionContent,\n        explanation: questionData.explanation || questionData.analysis || '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 处理选择题选项\n      if (questionData.options) {\n        let options = questionData.options\n\n        // 如果options是字符串，需要解析为JSON\n        if (typeof options === 'string') {\n          try {\n            options = JSON.parse(options)\n          } catch (e) {\n            console.error('解析选项JSON失败:', e)\n            options = []\n          }\n        }\n\n        if (Array.isArray(options)) {\n          formData.options = options.map(option => ({\n            optionKey: option.key || option.optionKey,\n            optionContent: option.content || option.optionContent\n          }))\n\n          // 设置正确答案\n          const correctOptions = options\n            .filter(option => option.isCorrect)\n            .map(option => option.key || option.optionKey)\n\n          if (questionData.questionType === 'single' || questionData.questionType === 1) {\n            formData.correctAnswer = correctOptions[0] || ''\n          } else if (questionData.questionType === 'multiple' || questionData.questionType === 2) {\n            formData.correctAnswer = correctOptions\n          }\n        }\n      }\n\n      // 处理判断题答案\n      if ((questionData.questionType === 'judgment' || questionData.questionType === 3) && questionData.correctAnswer) {\n        formData.correctAnswer = questionData.correctAnswer\n      }\n\n      return formData\n    },\n\n    // 题型数字转字符串\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n\n    // 难度数字转字符串\n    convertDifficultyToString(difficulty) {\n      const difficultyMap = {\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || difficulty\n    },\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        questionId: null,\n        bankId: this.bankId,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      }\n    },\n    // 题型变化处理\n    handleQuestionTypeChange(type) {\n      if (type === 'single' || type === 'multiple') {\n        if (this.form.options.length === 0) {\n          this.form.options = [\n            { optionKey: 'A', optionContent: '' },\n            { optionKey: 'B', optionContent: '' },\n            { optionKey: 'C', optionContent: '' },\n            { optionKey: 'D', optionContent: '' }\n          ]\n        }\n        // 设置默认答案\n        if (type === 'single') {\n          this.form.correctAnswer = 'A'\n        } else if (type === 'multiple') {\n          this.form.correctAnswer = 'A,B'\n        }\n      } else if (type === 'judgment') {\n        this.form.options = []\n        this.form.correctAnswer = 'true'  // 判断题默认选择\"正确\"\n      } else {\n        this.form.options = []\n        this.form.correctAnswer = ''\n      }\n    },\n    // 添加选项\n    addOption() {\n      const nextKey = String.fromCharCode(65 + this.form.options.length)\n      this.form.options.push({\n        optionKey: nextKey,\n        optionContent: ''\n      })\n    },\n    // 删除选项\n    removeOption(index) {\n      this.form.options.splice(index, 1)\n      // 重新分配选项键\n      this.form.options.forEach((option, idx) => {\n        option.optionKey = String.fromCharCode(65 + idx)\n      })\n      // 更新正确答案\n      this.updateCorrectAnswerAfterRemove()\n    },\n    // 删除选项后更新正确答案\n    updateCorrectAnswerAfterRemove() {\n      if (this.form.questionType === 'single') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        if (!validKeys.includes(this.form.correctAnswer)) {\n          this.form.correctAnswer = ''\n        }\n      } else if (this.form.questionType === 'multiple') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        const currentAnswers = this.form.correctAnswer.split(',').filter(Boolean)\n        const validAnswers = currentAnswers.filter(ans => validKeys.includes(ans))\n        this.form.correctAnswer = validAnswers.join(',')\n      }\n    },\n    // 单选题正确答案变化\n    handleSingleCorrectChange(optionKey, checked) {\n      this.form.correctAnswer = checked ? optionKey : ''\n    },\n    // 多选题正确答案变化\n    handleMultipleCorrectChange(optionKey, checked) {\n      let answers = this.form.correctAnswer ? this.form.correctAnswer.split(',') : []\n      if (checked) {\n        if (!answers.includes(optionKey)) {\n          answers.push(optionKey)\n        }\n      } else {\n        answers = answers.filter(ans => ans !== optionKey)\n      }\n      this.form.correctAnswer = answers.join(',')\n    },\n    // 判断多选题是否为正确答案\n    isMultipleCorrect(optionKey) {\n      if (!this.form.correctAnswer) return false\n      return this.form.correctAnswer.split(',').includes(optionKey)\n    },\n\n    // 判断选项是否为正确答案\n    isCorrectOption(optionKey) {\n      if (this.form.questionType === 'single') {\n        return this.form.correctAnswer === optionKey\n      } else if (this.form.questionType === 'multiple') {\n        if (!this.form.correctAnswer) return false\n        return this.form.correctAnswer.split(',').includes(optionKey)\n      }\n      return false\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          this.submitting = true\n\n          // 转换表单数据为后端需要的格式\n          const submitData = this.convertFormDataForSubmit()\n\n          const apiCall = this.isEdit ? updateQuestion(submitData) : addQuestion(submitData)\n          apiCall.then(response => {\n            this.submitting = false\n            this.$message.success(this.isEdit ? '更新成功' : '保存成功')\n            this.$emit('success')\n          }).catch(error => {\n            this.submitting = false\n            console.error('保存题目失败', error)\n            this.$message.error('保存题目失败')\n          })\n        }\n      })\n    },\n\n    // 转换表单数据为提交格式\n    convertFormDataForSubmit() {\n      const data = {\n        questionId: this.form.questionId,\n        bankId: this.form.bankId,\n        questionType: this.form.questionType,\n        difficulty: this.form.difficulty,\n        questionContent: this.form.questionContent,\n        explanation: this.form.explanation\n      }\n\n      // 处理选择题选项\n      if (this.isChoiceQuestion) {\n        data.options = this.form.options.map(option => ({\n          key: option.optionKey,\n          content: option.optionContent,\n          isCorrect: this.isCorrectOption(option.optionKey)\n        }))\n      }\n\n      // 处理判断题答案\n      if (this.form.questionType === 'judgment') {\n        data.correctAnswer = this.form.correctAnswer\n        // 判断题也需要设置options格式，以便后端统一处理\n        data.options = [\n          {\n            key: 'true',\n            content: '正确',\n            isCorrect: this.form.correctAnswer === 'true'\n          },\n          {\n            key: 'false',\n            content: '错误',\n            isCorrect: this.form.correctAnswer === 'false'\n          }\n        ]\n      }\n\n      return data\n    },\n    // 关闭前确认\n    handleBeforeClose(done) {\n      // 如果正在提交，不允许关闭\n      if (this.submitting) {\n        this.$message.warning('正在保存中，请稍候...')\n        return\n      }\n\n      // 检查表单是否有内容\n      const hasContent = this.form.questionContent ||\n                        (this.form.options && this.form.options.length > 0) ||\n                        this.form.explanation\n\n      if (hasContent) {\n        this.$confirm('确认关闭？未保存的内容将会丢失', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.handleClose()\n          done()\n        }).catch(() => {\n          // 用户取消关闭\n        })\n      } else {\n        this.handleClose()\n        done()\n      }\n    },\n\n    // 取消按钮点击\n    handleCancel() {\n      this.handleClose()\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.form.resetFields()\n      this.resetForm()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.editor-container {\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n}\n\n.editor {\n  min-height: 200px;\n}\n\n.options-container {\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n}\n\n.option-item {\n  margin-bottom: 20px;\n}\n\n.option-item:last-child {\n  margin-bottom: 0;\n}\n\n.option-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n}\n\n.option-answer {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-actions {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n  text-align: center;\n}\n\n\n</style>\n"]}]}