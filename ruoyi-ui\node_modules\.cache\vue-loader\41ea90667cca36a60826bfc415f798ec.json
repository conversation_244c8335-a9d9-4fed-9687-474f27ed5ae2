{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBRdWVzdGlvbkNhcmQgZnJvbSAnLi9jb21wb25lbnRzL1F1ZXN0aW9uQ2FyZCcKaW1wb3J0IFF1ZXN0aW9uRm9ybSBmcm9tICcuL2NvbXBvbmVudHMvUXVlc3Rpb25Gb3JtJwppbXBvcnQgQmF0Y2hJbXBvcnQgZnJvbSAnLi9jb21wb25lbnRzL0JhdGNoSW1wb3J0JwppbXBvcnQgeyBsaXN0UXVlc3Rpb24sIGRlbFF1ZXN0aW9uLCBnZXRRdWVzdGlvblN0YXRpc3RpY3MgfSBmcm9tICdAL2FwaS9iaXovcXVlc3Rpb24nCmltcG9ydCB7IGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25CYW5rRGV0YWlsIiwKICBjb21wb25lbnRzOiB7CiAgICBRdWVzdGlvbkNhcmQsCiAgICBRdWVzdGlvbkZvcm0sCiAgICBCYXRjaEltcG9ydAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmimOW6k+S/oeaBrwogICAgICBiYW5rSWQ6IG51bGwsCiAgICAgIGJhbmtOYW1lOiAnJywKICAgICAgLy8g57uf6K6h5pWw5o2uCiAgICAgIHN0YXRpc3RpY3M6IHsKICAgICAgICB0b3RhbDogMCwKICAgICAgICBzaW5nbGVDaG9pY2U6IDAsCiAgICAgICAgbXVsdGlwbGVDaG9pY2U6IDAsCiAgICAgICAganVkZ21lbnQ6IDAKICAgICAgfSwKICAgICAgLy8g6aKY55uu5YiX6KGoCiAgICAgIHF1ZXN0aW9uTGlzdDogW10sCiAgICAgIC8vIOWIhumhteWPguaVsAogICAgICB0b3RhbDogMCwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBiYW5rSWQ6IG51bGwsCiAgICAgICAgcXVlc3Rpb25UeXBlOiBudWxsLAogICAgICAgIGRpZmZpY3VsdHk6IG51bGwsCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWxleW8gOeKtuaAgQogICAgICBleHBhbmRBbGw6IGZhbHNlLAogICAgICAvLyDpgInmi6nnirbmgIEKICAgICAgc2VsZWN0ZWRRdWVzdGlvbnM6IFtdLAogICAgICAvLyDpgInmi6nnm7jlhbMKICAgICAgc2VsZWN0ZWRRdWVzdGlvbnM6IFtdLAogICAgICBpc0FsbFNlbGVjdGVkOiBmYWxzZSwKICAgICAgZXhwYW5kZWRRdWVzdGlvbnM6IFtdLAogICAgICAvLyDooajljZXnm7jlhbMKICAgICAgcXVlc3Rpb25Gb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRRdWVzdGlvblR5cGU6ICdzaW5nbGUnLAogICAgICBjdXJyZW50UXVlc3Rpb25EYXRhOiBudWxsLAogICAgICAvLyDmibnph4/lr7zlhaUKICAgICAgaW1wb3J0RHJhd2VyVmlzaWJsZTogZmFsc2UsCiAgICAgIGJhdGNoSW1wb3J0VmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRJbXBvcnRNb2RlOiAnZXhjZWwnLCAvLyDlvZPliY3lr7zlhaXmqKHlvI8KICAgICAgLy8g5paH5qGj5a+85YWl5oq95bGJCiAgICAgIGRvY3VtZW50Q29udGVudDogJycsCiAgICAgIGRvY3VtZW50SHRtbENvbnRlbnQ6ICcnLCAvLyDlrZjlgqjlr4zmlofmnKxIVE1M5YaF5a6555So5LqO6aKE6KeICiAgICAgIHBhcnNlZFF1ZXN0aW9uczogW10sCiAgICAgIHBhcnNlRXJyb3JzOiBbXSwKICAgICAgLy8g5YWo6YOo5bGV5byAL+aUtui1t+eKtuaAgQogICAgICBhbGxFeHBhbmRlZDogdHJ1ZSwKICAgICAgLy8g5qCH5b+X5L2N77ya5piv5ZCm5q2j5Zyo5LuO5ZCO56uv6K6+572u5YaF5a6577yI6YG/5YWN6Kem5Y+R5YmN56uv6YeN5paw6Kej5p6Q77yJCiAgICAgIGlzU2V0dGluZ0Zyb21CYWNrZW5kOiBmYWxzZSwKICAgICAgZG9jdW1lbnRJbXBvcnREaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgcnVsZXNEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy8g6KeE6IyD5a+56K+d5qGG5qCH562+6aG1CiAgICAgIGFjdGl2ZVJ1bGVUYWI6ICdleGFtcGxlcycsCiAgICAgIC8vIOS4iuS8oOWSjOino+aekOeKtuaAgQogICAgICBpc1VwbG9hZGluZzogZmFsc2UsCiAgICAgIGlzUGFyc2luZzogZmFsc2UsCiAgICAgIGltcG9ydE9wdGlvbnM6IHsKICAgICAgICByZXZlcnNlOiBmYWxzZSwKICAgICAgICBhbGxvd0R1cGxpY2F0ZTogZmFsc2UKICAgICAgfSwKICAgICAgLy8g5paH5Lu25LiK5LygCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvYml6L3F1ZXN0aW9uQmFuay91cGxvYWREb2N1bWVudCcsCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyB0aGlzLiRzdG9yZS5nZXR0ZXJzLnRva2VuCiAgICAgIH0sCiAgICAgIHVwbG9hZERhdGE6IHt9LAogICAgICAvLyDlr4zmlofmnKznvJbovpHlmagKICAgICAgcmljaEVkaXRvcjogbnVsbCwKICAgICAgZWRpdG9ySW5pdGlhbGl6ZWQ6IGZhbHNlCiAgICB9CiAgfSwKCiAgd2F0Y2g6IHsKICAgIC8vIOebkeWQrOaWh+aho+WGheWuueWPmOWMlu+8jOiHquWKqOino+aekAogICAgZG9jdW1lbnRDb250ZW50OiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgLy8g5aaC5p6c5piv5LuO5ZCO56uv6K6+572u5YaF5a6577yM5LiN6Kem5Y+R5YmN56uv6Kej5p6QCiAgICAgICAgaWYgKHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQpIHsKICAgICAgICAgIHJldHVybgogICAgICAgIH0KCgoKICAgICAgICBpZiAobmV3VmFsICYmIG5ld1ZhbC50cmltKCkpIHsKICAgICAgICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50KCkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IGZhbHNlCiAgICB9LAogICAgLy8g55uR5ZCs5oq95bGJ5omT5byA54q25oCBCiAgICBpbXBvcnREcmF3ZXJWaXNpYmxlOiB7CiAgICAgIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgaWYgKG5ld1ZhbCkgewogICAgICAgICAgLy8g5oq95bGJ5omT5byA5pe25Yid5aeL5YyW57yW6L6R5ZmoCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuaW5pdFJpY2hFZGl0b3IoKQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy8g5oq95bGJ5YWz6Zet5pe26ZSA5q+B57yW6L6R5ZmoCiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yKSB7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5kZXN0cm95KCkKICAgICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gZmFsc2UKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogZmFsc2UKICAgIH0KICB9LAoKICBjcmVhdGVkKCkgewogICAgdGhpcy5pbml0UGFnZSgpCiAgICAvLyDliJvlu7rpmLLmipblh73mlbAKICAgIHRoaXMuZGVib3VuY2VQYXJzZURvY3VtZW50ID0gdGhpcy5kZWJvdW5jZSh0aGlzLnBhcnNlRG9jdW1lbnQsIDEwMDApCiAgICAvLyDliJ3lp4vljJbkuIrkvKDmlbDmja4KICAgIHRoaXMudXBsb2FkRGF0YSA9IHsKICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZAogICAgfQogICAgdGhpcy51cGxvYWRIZWFkZXJzID0gewogICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyB0aGlzLiRzdG9yZS5nZXR0ZXJzLnRva2VuCiAgICB9CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIC8vIOe8lui+keWZqOWwhuWcqOaKveWxieaJk+W8gOaXtuWIneWni+WMlgoKICB9LAoKICBiZWZvcmVEZXN0cm95KCkgewoKCiAgICAvLyDplIDmr4Hlr4zmlofmnKznvJbovpHlmagKICAgIGlmICh0aGlzLnJpY2hFZGl0b3IpIHsKICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICB0aGlzLnJpY2hFZGl0b3IgPSBudWxsCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJbpobXpnaIKICAgIGluaXRQYWdlKCkgewogICAgICBjb25zdCB7IGJhbmtJZCwgYmFua05hbWUgfSA9IHRoaXMuJHJvdXRlLnF1ZXJ5CiAgICAgIGlmICghYmFua0lkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign57y65bCR6aKY5bqTSUTlj4LmlbAnKQogICAgICAgIHRoaXMuZ29CYWNrKCkKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICB0aGlzLmJhbmtJZCA9IGJhbmtJZAogICAgICB0aGlzLmJhbmtOYW1lID0gYmFua05hbWUgfHwgJ+mimOW6k+ivpuaDhScKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5iYW5rSWQgPSBiYW5rSWQKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKICAgIC8vIOi/lOWbnumimOW6k+WIl+ihqAogICAgZ29CYWNrKCkgewogICAgICB0aGlzLiRyb3V0ZXIuYmFjaygpCiAgICB9LAogICAgLy8g6I635Y+W6aKY55uu5YiX6KGoCiAgICBnZXRRdWVzdGlvbkxpc3QoKSB7CiAgICAgIC8vIOi9rOaNouafpeivouWPguaVsOagvOW8jwogICAgICBjb25zdCBwYXJhbXMgPSB0aGlzLmNvbnZlcnRRdWVyeVBhcmFtcyh0aGlzLnF1ZXJ5UGFyYW1zKQogICAgICBsaXN0UXVlc3Rpb24ocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnF1ZXN0aW9uTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewoKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bpopjnm67liJfooajlpLHotKUnKQogICAgICB9KQogICAgfSwKCiAgICAvLyDovazmjaLmn6Xor6Llj4LmlbDmoLzlvI8KICAgIGNvbnZlcnRRdWVyeVBhcmFtcyhwYXJhbXMpIHsKICAgICAgY29uc3QgY29udmVydGVkUGFyYW1zID0geyAuLi5wYXJhbXMgfQoKICAgICAgLy8g6L2s5o2i6aKY5Z6LCiAgICAgIGlmIChjb252ZXJ0ZWRQYXJhbXMucXVlc3Rpb25UeXBlKSB7CiAgICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAgICdzaW5nbGUnOiAxLAogICAgICAgICAgJ211bHRpcGxlJzogMiwKICAgICAgICAgICdqdWRnbWVudCc6IDMKICAgICAgICB9CiAgICAgICAgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZSA9IHR5cGVNYXBbY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZV0gfHwgY29udmVydGVkUGFyYW1zLnF1ZXN0aW9uVHlwZQogICAgICB9CgogICAgICAvLyDovazmjaLpmr7luqYKICAgICAgaWYgKGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5KSB7CiAgICAgICAgY29uc3QgZGlmZmljdWx0eU1hcCA9IHsKICAgICAgICAgICfnroDljZUnOiAxLAogICAgICAgICAgJ+S4reetiSc6IDIsCiAgICAgICAgICAn5Zuw6Zq+JzogMwogICAgICAgIH0KICAgICAgICBjb252ZXJ0ZWRQYXJhbXMuZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXBbY29udmVydGVkUGFyYW1zLmRpZmZpY3VsdHldIHx8IGNvbnZlcnRlZFBhcmFtcy5kaWZmaWN1bHR5CiAgICAgIH0KCiAgICAgIC8vIOa4heeQhuepuuWAvAogICAgICBPYmplY3Qua2V5cyhjb252ZXJ0ZWRQYXJhbXMpLmZvckVhY2goa2V5ID0+IHsKICAgICAgICBpZiAoY29udmVydGVkUGFyYW1zW2tleV0gPT09ICcnIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSBudWxsIHx8IGNvbnZlcnRlZFBhcmFtc1trZXldID09PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGRlbGV0ZSBjb252ZXJ0ZWRQYXJhbXNba2V5XQogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBjb252ZXJ0ZWRQYXJhbXMKICAgIH0sCiAgICAvLyDojrflj5bnu5/orqHmlbDmja4KICAgIGdldFN0YXRpc3RpY3MoKSB7CiAgICAgIGdldFF1ZXN0aW9uU3RhdGlzdGljcyh0aGlzLmJhbmtJZCkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5zdGF0aXN0aWNzID0gcmVzcG9uc2UuZGF0YQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgIC8vIOS9v+eUqOaooeaLn+aVsOaNrgogICAgICAgIHRoaXMuc3RhdGlzdGljcyA9IHsKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2luZ2xlQ2hvaWNlOiAwLAogICAgICAgICAgbXVsdGlwbGVDaG9pY2U6IDAsCiAgICAgICAgICBqdWRnbWVudDogMAogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDmibnph4/lr7zlhaUKICAgIGhhbmRsZUJhdGNoSW1wb3J0KCkgewogICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICAvLyDmt7vliqDpopjnm64KICAgIGhhbmRsZUFkZFF1ZXN0aW9uKHR5cGUpIHsKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlID0gdHlwZQogICAgICB0aGlzLmN1cnJlbnRRdWVzdGlvbkRhdGEgPSBudWxsCiAgICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICAvLyDliIfmjaLlsZXlvIDnirbmgIEKICAgIHRvZ2dsZUV4cGFuZEFsbCgpIHsKICAgICAgdGhpcy5leHBhbmRBbGwgPSAhdGhpcy5leHBhbmRBbGwKICAgICAgaWYgKCF0aGlzLmV4cGFuZEFsbCkgewogICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMgPSBbXQogICAgICB9CiAgICB9LAoKCgogICAgLy8g5a+85Ye66aKY55uuCiAgICBoYW5kbGVFeHBvcnRRdWVzdGlvbnMoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6YCJ5oup6KaB5a+85Ye655qE6aKY55uuJykKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oYOato+WcqOWvvOWHuiAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm64uLi5gKQogICAgICAvLyBUT0RPOiDlrp7njrDlr7zlh7rlip/og70KICAgIH0sCgogICAgLy8g5YiH5o2i5YWo6YCJL+WFqOS4jemAiQogICAgaGFuZGxlVG9nZ2xlU2VsZWN0QWxsKCkgewogICAgICB0aGlzLmlzQWxsU2VsZWN0ZWQgPSAhdGhpcy5pc0FsbFNlbGVjdGVkCiAgICAgIGlmICh0aGlzLmlzQWxsU2VsZWN0ZWQpIHsKICAgICAgICAvLyDlhajpgIkKICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gdGhpcy5xdWVzdGlvbkxpc3QubWFwKHEgPT4gcS5xdWVzdGlvbklkKQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey6YCJ5oupICR7dGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebrmApCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5YWo5LiN6YCJCiAgICAgICAgdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflt7Llj5bmtojpgInmi6nmiYDmnInpopjnm64nKQogICAgICB9CiAgICB9LAoKCgogICAgLy8g5om56YeP5Yig6ZmkCiAgICBoYW5kbGVCYXRjaERlbGV0ZSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flhYjpgInmi6nopoHliKDpmaTnmoTpopjnm64nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTpgInkuK3nmoQgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu5ZCX77yfYCwgJ+aJuemHj+WIoOmZpOehruiupCcsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIC8vIOi/memHjOW6lOivpeiwg+eUqOaJuemHj+WIoOmZpEFQSQogICAgICAgIC8vIOaaguaXtuS9v+eUqOWNleS4quWIoOmZpOeahOaWueW8jwogICAgICAgIGNvbnN0IGRlbGV0ZVByb21pc2VzID0gdGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5tYXAocXVlc3Rpb25JZCA9PgogICAgICAgICAgZGVsUXVlc3Rpb24ocXVlc3Rpb25JZCkKICAgICAgICApCgogICAgICAgIFByb21pc2UuYWxsKGRlbGV0ZVByb21pc2VzKS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5Yig6ZmkICR7dGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebrmApCiAgICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zID0gW10KICAgICAgICAgIHRoaXMuYWxsU2VsZWN0ZWQgPSBmYWxzZQogICAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5om56YeP5Yig6Zmk5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKCiAgICAvLyDmibnph4/liKDpmaQKICAgIGhhbmRsZUJhdGNoRGVsZXRlKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFF1ZXN0aW9ucy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WFiOmAieaLqeimgeWIoOmZpOeahOmimOebricpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oYOehruiupOWIoOmZpOmAieS4reeahCAke3RoaXMuc2VsZWN0ZWRRdWVzdGlvbnMubGVuZ3RofSDpgZPpopjnm67lkJfvvJ9gLCAn5om56YeP5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgLy8g5om56YeP5Yig6ZmkQVBJ6LCD55SoCiAgICAgICAgY29uc3QgZGVsZXRlUHJvbWlzZXMgPSB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLm1hcChxdWVzdGlvbklkID0+CiAgICAgICAgICBkZWxRdWVzdGlvbihxdWVzdGlvbklkKQogICAgICAgICkKCiAgICAgICAgUHJvbWlzZS5hbGwoZGVsZXRlUHJvbWlzZXMpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/liKDpmaQgJHt0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5pc0FsbFNlbGVjdGVkID0gZmFsc2UKICAgICAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgICAgIHRoaXMuZ2V0U3RhdGlzdGljcygpCiAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewoKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aJuemHj+WIoOmZpOWksei0pScpCiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5bey5Y+W5raI5Yig6ZmkJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g6aKY55uu6YCJ5oup54q25oCB5Y+Y5YyWCiAgICBoYW5kbGVRdWVzdGlvblNlbGVjdChxdWVzdGlvbklkLCBzZWxlY3RlZCkgewogICAgICBpZiAoc2VsZWN0ZWQpIHsKICAgICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuaW5jbHVkZXMocXVlc3Rpb25JZCkpIHsKICAgICAgICAgIHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbklkKQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBjb25zdCBpbmRleCA9IHRoaXMuc2VsZWN0ZWRRdWVzdGlvbnMuaW5kZXhPZihxdWVzdGlvbklkKQogICAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgICB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLnNwbGljZShpbmRleCwgMSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOabtOaWsOWFqOmAieeKtuaAgQogICAgICB0aGlzLmlzQWxsU2VsZWN0ZWQgPSB0aGlzLnNlbGVjdGVkUXVlc3Rpb25zLmxlbmd0aCA9PT0gdGhpcy5xdWVzdGlvbkxpc3QubGVuZ3RoCiAgICB9LAogICAgLy8g5YiH5o2i5Y2V5Liq6aKY55uu5bGV5byA54q25oCBCiAgICBoYW5kbGVUb2dnbGVFeHBhbmQocXVlc3Rpb25JZCkgewogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMuaW5kZXhPZihxdWVzdGlvbklkKQogICAgICBpZiAoaW5kZXggPiAtMSkgewogICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMuc3BsaWNlKGluZGV4LCAxKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZXhwYW5kZWRRdWVzdGlvbnMucHVzaChxdWVzdGlvbklkKQogICAgICB9CiAgICB9LAogICAgLy8g57yW6L6R6aKY55uuCiAgICBoYW5kbGVFZGl0UXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25EYXRhID0gcXVlc3Rpb24KICAgICAgdGhpcy5jdXJyZW50UXVlc3Rpb25UeXBlID0gcXVlc3Rpb24ucXVlc3Rpb25UeXBlCiAgICAgIHRoaXMucXVlc3Rpb25Gb3JtVmlzaWJsZSA9IHRydWUKICAgIH0sCiAgICAvLyDlpI3liLbpopjnm64KICAgIGhhbmRsZUNvcHlRdWVzdGlvbihxdWVzdGlvbikgewogICAgICAvLyDliJvlu7rlpI3liLbnmoTpopjnm67mlbDmja7vvIjnp7vpmaRJROebuOWFs+Wtl+aute+8iQogICAgICBjb25zdCBjb3BpZWRRdWVzdGlvbiA9IHsKICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICBxdWVzdGlvbklkOiBudWxsLCAgLy8g5riF6ZmkSUTvvIzooajnpLrmlrDlop4KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsCiAgICAgICAgY3JlYXRlQnk6IG51bGwsCiAgICAgICAgdXBkYXRlQnk6IG51bGwKICAgICAgfQoKICAgICAgLy8g6K6+572u5Li657yW6L6R5qih5byP5bm25omT5byA6KGo5Y2VCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uRGF0YSA9IGNvcGllZFF1ZXN0aW9uCiAgICAgIHRoaXMuY3VycmVudFF1ZXN0aW9uVHlwZSA9IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZVRvU3RyaW5nKHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gdHJ1ZQogICAgfSwKCiAgICAvLyDpopjlnovmlbDlrZfovazlrZfnrKbkuLLvvIjnlKjkuo7lpI3liLblip/og73vvIkKICAgIGNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgMTogJ3NpbmdsZScsCiAgICAgICAgMjogJ211bHRpcGxlJywKICAgICAgICAzOiAnanVkZ21lbnQnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgdHlwZQogICAgfSwKICAgIC8vIOWIoOmZpOmimOebrgogICAgaGFuZGxlRGVsZXRlUXVlc3Rpb24ocXVlc3Rpb24pIHsKICAgICAgY29uc3QgcXVlc3Rpb25Db250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpCiAgICAgIGNvbnN0IGRpc3BsYXlDb250ZW50ID0gcXVlc3Rpb25Db250ZW50Lmxlbmd0aCA+IDUwID8gcXVlc3Rpb25Db250ZW50LnN1YnN0cmluZygwLCA1MCkgKyAnLi4uJyA6IHF1ZXN0aW9uQ29udGVudAogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTliKDpmaTpopjnm64iJHtkaXNwbGF5Q29udGVudH0i5ZCX77yfYCwgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIGRlbFF1ZXN0aW9uKHF1ZXN0aW9uLnF1ZXN0aW9uSWQpLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CgogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk6aKY55uu5aSx6LSlJykKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8vIOmimOebruihqOWNleaIkOWKn+WbnuiwgwogICAgaGFuZGxlUXVlc3Rpb25Gb3JtU3VjY2VzcygpIHsKICAgICAgdGhpcy5xdWVzdGlvbkZvcm1WaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgICB0aGlzLmdldFN0YXRpc3RpY3MoKQogICAgfSwKICAgIC8vIOaJuemHj+WvvOWFpeaIkOWKn+WbnuiwgwogICAgaGFuZGxlQmF0Y2hJbXBvcnRTdWNjZXNzKCkgewogICAgICB0aGlzLmJhdGNoSW1wb3J0VmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuaW1wb3J0RHJhd2VyVmlzaWJsZSA9IGZhbHNlCiAgICAgIHRoaXMuZ2V0UXVlc3Rpb25MaXN0KCkKICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgIH0sCgoKCiAgICAvLyDmir3lsYnlhbPpl63liY3lpITnkIYKICAgIGhhbmRsZURyYXdlckNsb3NlKGRvbmUpIHsKICAgICAgZG9uZSgpCiAgICB9LAoKICAgIC8vIOaYvuekuuaWh+aho+WvvOWFpeWvueivneahhgogICAgc2hvd0RvY3VtZW50SW1wb3J0RGlhbG9nKCkgewogICAgICAvLyDmuIXpmaTkuIrkuIDmrKHnmoTkuIrkvKDnirbmgIHlkozlhoXlrrkKICAgICAgdGhpcy5pc1VwbG9hZGluZyA9IGZhbHNlCiAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKCiAgICAgIC8vIOa4hemZpOS4iuS8oOe7hOS7tueahOaWh+S7tuWIl+ihqAogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgY29uc3QgdXBsb2FkQ29tcG9uZW50ID0gdGhpcy4kcmVmcy5kb2N1bWVudFVwbG9hZAogICAgICAgIGlmICh1cGxvYWRDb21wb25lbnQpIHsKICAgICAgICAgIHVwbG9hZENvbXBvbmVudC5jbGVhckZpbGVzKCkKICAgICAgICB9CiAgICAgIH0pCgogICAgICB0aGlzLmRvY3VtZW50SW1wb3J0RGlhbG9nVmlzaWJsZSA9IHRydWUKCiAgICB9LAoKICAgIC8vIOaYvuekuuinhOiMg+WvueivneahhgogICAgc2hvd1J1bGVzRGlhbG9nKCkgewogICAgICB0aGlzLmFjdGl2ZVJ1bGVUYWIgPSAnZXhhbXBsZXMnIC8vIOm7mOiupOaYvuekuuiMg+S+i+agh+etvumhtQogICAgICB0aGlzLnJ1bGVzRGlhbG9nVmlzaWJsZSA9IHRydWUKICAgIH0sCgogICAgLy8g5bCG6IyD5L6L5aSN5Yi25Yiw57yW6L6R5Yy6IC0g5Y+q5L+d55WZ5YmNM+mimO+8muWNlemAieOAgeWkmumAieOAgeWIpOaWrQogICAgY29weUV4YW1wbGVUb0VkaXRvcigpIHsKICAgICAgLy8g5L2/55So6L6T5YWl6IyD5L6L5qCH562+6aG16YeM55qE5YmNM+mimOWGheWuue+8jOi9rOaNouS4ukhUTUzmoLzlvI8KICAgICAgY29uc3QgaHRtbFRlbXBsYXRlID0gYAo8cD4xLu+8iCAg77yJ5piv5oiR5Zu95pyA5pep55qE6K+X5q2M5oC76ZuG77yM5Y+I56ew5L2cIuivl+S4ieeZviLjgII8L3A+CjxwPkEu44CK5bem5Lyg44CLPC9wPgo8cD5CLuOAiuemu+mqmuOAizwvcD4KPHA+Qy7jgIrlnZvnu4/jgIs8L3A+CjxwPkQu44CK6K+X57uP44CLPC9wPgo8cD7nrZTmoYjvvJpEPC9wPgo8cD7op6PmnpDvvJror5fnu4/mmK/miJHlm73mnIDml6nnmoTor5fmrYzmgLvpm4bjgII8L3A+CjxwPumavuW6pu+8muS4reetiTwvcD4KPHA+PGJyPjwvcD4KCjxwPjIu5Lit5Y2O5Lq65rCR5YWx5ZKM5Zu955qE5oiQ56uL77yM5qCH5b+X552A77yIIO+8ieOAgjwvcD4KPHA+QS7kuK3lm73mlrDmsJHkuLvkuLvkuYnpnanlkb3lj5blvpfkuobln7rmnKzog5zliKk8L3A+CjxwPkIu5Lit5Zu9546w5Luj5Y+y55qE5byA5aeLPC9wPgo8cD5DLuWNiuauluawkeWcsOWNiuWwgeW7uuekvuS8mueahOe7k+adnzwvcD4KPHA+RC7kuK3lm73ov5vlhaXnpL7kvJrkuLvkuYnnpL7kvJo8L3A+CjxwPuetlOahiO+8mkFCQzwvcD4KPHA+6Kej5p6Q77ya5paw5Lit5Zu955qE5oiQ56uL77yM5qCH5b+X552A5oiR5Zu95paw5rCR5Li75Li75LmJ6Z2p5ZG96Zi25q6155qE5Z+65pys57uT5p2f5ZKM56S+5Lya5Li75LmJ6Z2p5ZG96Zi25q6155qE5byA5aeL44CCPC9wPgo8cD48YnI+PC9wPgoKPHA+My7lhYPmnYLliafnmoTlm5vlpKfmgrLliafmmK/vvJrlhbPmsYnljb/nmoTjgIrnqqblqKXlhqTjgIvvvIzpqazoh7Tov5znmoTjgIrmsYnlrqvnp4vjgIvvvIznmb3mnLTnmoTjgIrmoqfmoZDpm6jjgIvlkozpg5HlhYnnpZbnmoTjgIrotbXmsI/lraTlhL/jgIvjgII8L3A+CjxwPuetlOahiO+8mumUmeivrzwvcD4KPHA+6Kej5p6Q77ya5YWD5p2C5Ymn44CK6LW15rCP5a2k5YS/44CL5YWo5ZCN44CK5Yak5oql5Yak6LW15rCP5a2k5YS/44CL77yM5Li657qq5ZCb56Wl5omA5L2c44CCPC9wPgogICAgICBgLnRyaW0oKQoKICAgICAgLy8g55u05o6l6K6+572u5Yiw5a+M5paH5pys57yW6L6R5ZmoCiAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGh0bWxUZW1wbGF0ZSkKCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo5pyq5Yid5aeL5YyW77yM562J5b6F5Yid5aeL5YyW5ZCO5YaN6K6+572uCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvciAmJiB0aGlzLmVkaXRvckluaXRpYWxpemVkKSB7CiAgICAgICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGh0bWxUZW1wbGF0ZSkKCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQoKICAgICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICAgIHRoaXMucnVsZXNEaWFsb2dWaXNpYmxlID0gZmFsc2UKCiAgICAgIC8vIOaPkOekuueUqOaItwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+i+k+WFpeiMg+S+i+W3suWhq+WFheWIsOe8lui+keWMuu+8jOWPs+S+p+WwhuiHquWKqOino+aekCcpCgoKICAgIH0sCgogICAgLy8g5LiL6L29RXhjZWzmqKHmnb8KICAgIGRvd25sb2FkRXhjZWxUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnYml6L3F1ZXN0aW9uQmFuay9kb3dubG9hZEV4Y2VsVGVtcGxhdGUnLCB7fSwgYOmimOebruWvvOWFpUV4Y2Vs5qih5p2/Lnhsc3hgKQogICAgfSwKCiAgICAvLyDkuIvovb1Xb3Jk5qih5p2/CiAgICBkb3dubG9hZFdvcmRUZW1wbGF0ZSgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnYml6L3F1ZXN0aW9uQmFuay9kb3dubG9hZFdvcmRUZW1wbGF0ZScsIHt9LCBg6aKY55uu5a+85YWlV29yZOaooeadvy5kb2N4YCkKICAgIH0sCgogICAgLy8g5LiK5Lyg5YmN5qOA5p+lCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewoKCiAgICAgIGNvbnN0IGlzVmFsaWRUeXBlID0gZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcgfHwKICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGUubmFtZS5lbmRzV2l0aCgnLmRvY3gnKSB8fCBmaWxlLm5hbWUuZW5kc1dpdGgoJy54bHN4JykKICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTAKCiAgICAgIGlmICghaXNWYWxpZFR5cGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKAgLmRvY3gg5oiWIC54bHN4IOagvOW8j+eahOaWh+S7tiEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIGlmICghaXNMdDEwTSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CIScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KCiAgICAgIC8vIOabtOaWsOS4iuS8oOaVsOaNrgogICAgICB0aGlzLnVwbG9hZERhdGEuYmFua0lkID0gdGhpcy5iYW5rSWQKCiAgICAgIC8vIOiuvue9ruS4iuS8oOeKtuaAgQogICAgICB0aGlzLmlzVXBsb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCgoKCiAgICAgIHJldHVybiB0cnVlCiAgICB9LAoKICAgIC8vIOS4iuS8oOaIkOWKnwogICAgaGFuZGxlVXBsb2FkU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkgewoKCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAvLyDkuIrkvKDlrozmiJDvvIzlvIDlp4vop6PmnpAKICAgICAgICB0aGlzLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgICB0aGlzLmlzUGFyc2luZyA9IHRydWUKCgoKICAgICAgICAvLyDmuIXpmaTkuYvliY3nmoTop6PmnpDnu5PmnpzvvIznoa7kv53lubLlh4DnmoTlvIDlp4sKICAgICAgICB0aGlzLnBhcnNlZFF1ZXN0aW9ucyA9IFtdCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCgogICAgICAgIC8vIOW7tui/n+WFs+mXreWvueivneahhu+8jOiuqeeUqOaIt+eci+WIsOino+aekOWKqOeUuwogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgdGhpcy5kb2N1bWVudEltcG9ydERpYWxvZ1Zpc2libGUgPSBmYWxzZQogICAgICAgICAgdGhpcy5pc1BhcnNpbmcgPSBmYWxzZQogICAgICAgIH0sIDE1MDApCgogICAgICAgIC8vIOiuvue9ruagh+W/l+S9je+8jOmBv+WFjeinpuWPkeWJjeerr+mHjeaWsOino+aekAogICAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSB0cnVlCgogICAgICAgIC8vIOWwhuino+aekOe7k+aenOaYvuekuuWcqOWPs+S+pwogICAgICAgIGlmIChyZXNwb25zZS5xdWVzdGlvbnMgJiYgcmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gcmVzcG9uc2UucXVlc3Rpb25zLm1hcChxdWVzdGlvbiA9PiAoewogICAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgICAgY29sbGFwc2VkOiBmYWxzZSAgLy8g6buY6K6k5bGV5byACiAgICAgICAgICB9KSkKICAgICAgICAgIC8vIOmHjee9ruWFqOmDqOWxleW8gOeKtuaAgQogICAgICAgICAgdGhpcy5hbGxFeHBhbmRlZCA9IHRydWUKICAgICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSByZXNwb25zZS5lcnJvcnMgfHwgW10KCiAgICAgICAgICAvLyDmmL7npLror6bnu4bnmoTop6PmnpDnu5PmnpwKICAgICAgICAgIGNvbnN0IGVycm9yQ291bnQgPSByZXNwb25zZS5lcnJvcnMgPyByZXNwb25zZS5lcnJvcnMubGVuZ3RoIDogMAogICAgICAgICAgaWYgKGVycm9yQ291bnQgPiAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf6Kej5p6Q5Ye6ICR7cmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uu77yM5pyJICR7ZXJyb3JDb3VudH0g5Liq6ZSZ6K+v5oiW6K2m5ZGKYCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf6Kej5p6Q5Ye6ICR7cmVzcG9uc2UucXVlc3Rpb25zLmxlbmd0aH0g6YGT6aKY55uuYCkKICAgICAgICAgIH0KCgogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrop6PmnpDlh7rku7vkvZXpopjnm67vvIzor7fmo4Dmn6Xmlofku7bmoLzlvI8nKQogICAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmVycm9ycyB8fCBbJ+acquiDveino+aekOWHuumimOebruWGheWuuSddCgoKICAgICAgICB9CgogICAgICAgIC8vIOWwhuWOn+Wni+WGheWuueWhq+WFheWIsOWvjOaWh+acrOe8lui+keWZqOS4rQogICAgICAgIGlmIChyZXNwb25zZS5vcmlnaW5hbENvbnRlbnQpIHsKICAgICAgICAgIHRoaXMuc2V0RWRpdG9yQ29udGVudChyZXNwb25zZS5vcmlnaW5hbENvbnRlbnQpCiAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IHJlc3BvbnNlLm9yaWdpbmFsQ29udGVudAogICAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gcmVzcG9uc2Uub3JpZ2luYWxDb250ZW50IC8vIOWIneWni+WMlkhUTUzlhoXlrrkKCiAgICAgICAgfQoKICAgICAgICAvLyDlu7bov5/ph43nva7moIflv5fkvY3vvIznoa7kv53miYDmnInlvILmraXmk43kvZzlrozmiJAKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRoaXMuaXNTZXR0aW5nRnJvbUJhY2tlbmQgPSBmYWxzZQogICAgICAgIH0sIDIwMDApCiAgICAgIH0gZWxzZSB7CgogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfmlofku7bkuIrkvKDlpLHotKUnKQogICAgICAgIC8vIOmHjee9rueKtuaAgQogICAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaXNQYXJzaW5nID0gZmFsc2UKICAgICAgfQogICAgfSwKCiAgICAvLyDkuIrkvKDlpLHotKUKICAgIGhhbmRsZVVwbG9hZEVycm9yKGVycm9yLCBmaWxlKSB7CgogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKXvvIzor7fmo4Dmn6XnvZHnu5zov57mjqXmiJbogZTns7vnrqHnkIblkZgnKQoKICAgICAgLy8g6YeN572u54q25oCBCiAgICAgIHRoaXMuaXNVcGxvYWRpbmcgPSBmYWxzZQogICAgICB0aGlzLmlzUGFyc2luZyA9IGZhbHNlCiAgICB9LAoKICAgIC8vIOWFqOmDqOaUtui1twogICAgY29sbGFwc2VBbGwoKSB7CiAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zLmZvckVhY2gocXVlc3Rpb24gPT4gewogICAgICAgIHRoaXMuJHNldChxdWVzdGlvbiwgJ2NvbGxhcHNlZCcsIHRydWUpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOWIh+aNoumimOebruWxleW8gC/mlLbotbcKICAgIHRvZ2dsZVF1ZXN0aW9uKGluZGV4KSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9uID0gdGhpcy5wYXJzZWRRdWVzdGlvbnNbaW5kZXhdCiAgICAgIHRoaXMuJHNldChxdWVzdGlvbiwgJ2NvbGxhcHNlZCcsICFxdWVzdGlvbi5jb2xsYXBzZWQpCiAgICB9LAoKICAgIC8vIOWFqOmDqOWxleW8gC/mlLbotbcKICAgIHRvZ2dsZUFsbFF1ZXN0aW9ucygpIHsKICAgICAgdGhpcy5hbGxFeHBhbmRlZCA9ICF0aGlzLmFsbEV4cGFuZGVkCiAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zLmZvckVhY2gocXVlc3Rpb24gPT4gewogICAgICAgIHRoaXMuJHNldChxdWVzdGlvbiwgJ2NvbGxhcHNlZCcsICF0aGlzLmFsbEV4cGFuZGVkKQogICAgICB9KQoKICAgIH0sCgogICAgLy8g56Gu6K6k5a+85YWlCiAgICBjb25maXJtSW1wb3J0KCkgewogICAgICBpZiAodGhpcy5wYXJzZWRRdWVzdGlvbnMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmsqHmnInlj6/lr7zlhaXnmoTpopjnm64nKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKGDnoa7orqTlr7zlhaUgJHt0aGlzLnBhcnNlZFF1ZXN0aW9ucy5sZW5ndGh9IOmBk+mimOebruWQl++8n2AsICfnoa7orqTlr7zlhaUnLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICdpbmZvJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLmltcG9ydFF1ZXN0aW9ucygpCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQogICAgfSwKCiAgICAvLyDlr7zlhaXpopjnm64KICAgIGFzeW5jIGltcG9ydFF1ZXN0aW9ucygpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDlpITnkIblr7zlhaXpgInpobkKICAgICAgICBsZXQgcXVlc3Rpb25zVG9JbXBvcnQgPSBbLi4udGhpcy5wYXJzZWRRdWVzdGlvbnNdCgogICAgICAgIGlmICh0aGlzLmltcG9ydE9wdGlvbnMucmV2ZXJzZSkgewogICAgICAgICAgcXVlc3Rpb25zVG9JbXBvcnQucmV2ZXJzZSgpCiAgICAgICAgfQoKICAgICAgICAvLyDosIPnlKjlrp7pmYXnmoTlr7zlhaVBUEkKICAgICAgICBjb25zdCBpbXBvcnREYXRhID0gewogICAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICAgIHF1ZXN0aW9uczogcXVlc3Rpb25zVG9JbXBvcnQsCiAgICAgICAgICBhbGxvd0R1cGxpY2F0ZTogdGhpcy5pbXBvcnRPcHRpb25zLmFsbG93RHVwbGljYXRlCiAgICAgICAgfQoKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGJhdGNoSW1wb3J0UXVlc3Rpb25zKGltcG9ydERhdGEpCgogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5oiQ5Yqf5a+85YWlICR7cXVlc3Rpb25zVG9JbXBvcnQubGVuZ3RofSDpgZPpopjnm65gKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKQogICAgICAgIH0KICAgICAgICB0aGlzLmltcG9ydERyYXdlclZpc2libGUgPSBmYWxzZQogICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gJycKICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSAnJwogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KCgoKICAgICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICAgICAgdGhpcy5nZXRTdGF0aXN0aWNzKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5a+85YWl5aSx6LSlJykKICAgICAgfQogICAgfSwKCiAgICAvLyDliJ3lp4vljJblr4zmlofmnKznvJbovpHlmagKICAgIGluaXRSaWNoRWRpdG9yKCkgewogICAgICBpZiAodGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmo4Dmn6VDS0VkaXRvcuaYr+WQpuWPr+eUqAogICAgICBpZiAoIXdpbmRvdy5DS0VESVRPUikgewoKICAgICAgICB0aGlzLmZhbGxiYWNrVG9UZXh0YXJlYSgpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo5bey5a2Y5Zyo77yM5YWI6ZSA5q+BCiAgICAgICAgaWYgKHRoaXMucmljaEVkaXRvcikgewogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLmRlc3Ryb3koKQogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yID0gbnVsbAogICAgICAgIH0KCiAgICAgICAgLy8g56Gu5L+d5a655Zmo5a2Y5ZyoCiAgICAgICAgY29uc3QgZWRpdG9yQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3JpY2gtZWRpdG9yJykKICAgICAgICBpZiAoIWVkaXRvckNvbnRhaW5lcikgewoKICAgICAgICAgIHJldHVybgogICAgICAgIH0KCiAgICAgICAgLy8g5Yib5bu6dGV4dGFyZWHlhYPntKAKICAgICAgICBlZGl0b3JDb250YWluZXIuaW5uZXJIVE1MID0gJzx0ZXh0YXJlYSBpZD0icmljaC1lZGl0b3ItdGV4dGFyZWEiIG5hbWU9InJpY2gtZWRpdG9yLXRleHRhcmVhIj48L3RleHRhcmVhPicKCiAgICAgICAgLy8g562J5b6FRE9N5pu05paw5ZCO5Yib5bu657yW6L6R5ZmoCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgLy8g5qOA5p+lQ0tFZGl0b3LmmK/lkKblj6/nlKgKICAgICAgICAgIGlmICghd2luZG93LkNLRURJVE9SIHx8ICF3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSkgewoKICAgICAgICAgICAgdGhpcy5zaG93RmFsbGJhY2tFZGl0b3IgPSB0cnVlCiAgICAgICAgICAgIHJldHVybgogICAgICAgICAgfQoKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIC8vIOWFiOWwneivleWujOaVtOmFjee9rgogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSB3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSgncmljaC1lZGl0b3ItdGV4dGFyZWEnLCB7CiAgICAgICAgICAgICAgaGVpZ2h0OiAnY2FsYygxMDB2aCAtIDIwMHB4KScsIC8vIOWFqOWxj+mrmOW6puWHj+WOu+WktOmDqOWSjOWFtuS7luWFg+e0oOeahOmrmOW6pgogICAgICAgICAgICAgIHRvb2xiYXI6IFsKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3N0eWxlcycsIGl0ZW1zOiBbJ0ZvbnRTaXplJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2Jhc2ljc3R5bGVzJywgaXRlbXM6IFsnQm9sZCcsICdJdGFsaWMnLCAnVW5kZXJsaW5lJywgJ1N0cmlrZScsICdTdXBlcnNjcmlwdCcsICdTdWJzY3JpcHQnLCAnLScsICdSZW1vdmVGb3JtYXQnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnY2xpcGJvYXJkJywgaXRlbXM6IFsnQ3V0JywgJ0NvcHknLCAnUGFzdGUnLCAnUGFzdGVUZXh0J10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2NvbG9ycycsIGl0ZW1zOiBbJ1RleHRDb2xvcicsICdCR0NvbG9yJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ3BhcmFncmFwaCcsIGl0ZW1zOiBbJ0p1c3RpZnlMZWZ0JywgJ0p1c3RpZnlDZW50ZXInLCAnSnVzdGlmeVJpZ2h0JywgJ0p1c3RpZnlCbG9jayddIH0sCiAgICAgICAgICAgICAgICB7IG5hbWU6ICdlZGl0aW5nJywgaXRlbXM6IFsnVW5kbycsICdSZWRvJ10gfSwKICAgICAgICAgICAgICAgIHsgbmFtZTogJ2xpbmtzJywgaXRlbXM6IFsnTGluaycsICdVbmxpbmsnXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAnaW5zZXJ0JywgaXRlbXM6IFsnSW1hZ2UnLCAnU3BlY2lhbENoYXInXSB9LAogICAgICAgICAgICAgICAgeyBuYW1lOiAndG9vbHMnLCBpdGVtczogWydNYXhpbWl6ZSddIH0KICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIHJlbW92ZUJ1dHRvbnM6ICcnLAogICAgICAgICAgICAgIGxhbmd1YWdlOiAnemgtY24nLAogICAgICAgICAgICAgIHJlbW92ZVBsdWdpbnM6ICdlbGVtZW50c3BhdGgnLAogICAgICAgICAgICAgIHJlc2l6ZV9lbmFibGVkOiBmYWxzZSwKICAgICAgICAgICAgICBleHRyYVBsdWdpbnM6ICdmb250LGNvbG9yYnV0dG9uLGp1c3RpZnksc3BlY2lhbGNoYXIsaW1hZ2UnLAogICAgICAgICAgICAgIGFsbG93ZWRDb250ZW50OiB0cnVlLAogICAgICAgICAgICAgIC8vIOWtl+S9k+Wkp+Wwj+mFjee9rgogICAgICAgICAgICAgIGZvbnRTaXplX3NpemVzOiAnMTIvMTJweDsxNC8xNHB4OzE2LzE2cHg7MTgvMThweDsyMC8yMHB4OzIyLzIycHg7MjQvMjRweDsyNi8yNnB4OzI4LzI4cHg7MzYvMzZweDs0OC80OHB4OzcyLzcycHgnLAogICAgICAgICAgICAgIGZvbnRTaXplX2RlZmF1bHRMYWJlbDogJzE0cHgnLAogICAgICAgICAgICAgIC8vIOminOiJsumFjee9rgogICAgICAgICAgICAgIGNvbG9yQnV0dG9uX2VuYWJsZU1vcmU6IHRydWUsCiAgICAgICAgICAgICAgY29sb3JCdXR0b25fY29sb3JzOiAnQ0Y1RDRFLDQ1NDU0NSxGRkYsQ0NDLERERCxDQ0VBRUUsNjZBQjE2JywKICAgICAgICAgICAgICAvLyDlm77lg4/kuIrkvKDphY3nva4gLSDlj4LogIPmgqjmj5DkvpvnmoTmoIflh4bphY3nva4KICAgICAgICAgICAgICBmaWxlYnJvd3NlclVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvY29tbW9uL3VwbG9hZEltYWdlJywKICAgICAgICAgICAgICBpbWFnZV9wcmV2aWV3VGV4dDogJyAnLAogICAgICAgICAgICAgIC8vIOiuvue9ruWfuuehgOi3r+W+hO+8jOiuqeebuOWvuei3r+W+hOiDveato+ehruino+aekOWIsOWQjuerr+acjeWKoeWZqAogICAgICAgICAgICAgIGJhc2VIcmVmOiAnaHR0cDovL2xvY2FsaG9zdDo4ODAyLycsCiAgICAgICAgICAgICAgLy8g5Zu+5YOP5o+S5YWl6YWN572uCiAgICAgICAgICAgICAgaW1hZ2VfcHJldmlld1RleHQ6ICfpooTop4jljLrln58nLAogICAgICAgICAgICAgIGltYWdlX3JlbW92ZUxpbmtCeUVtcHR5VVJMOiB0cnVlLAogICAgICAgICAgICAgIC8vIOmakOiXj+S4jemcgOimgeeahOagh+etvumhte+8jOWPquS/neeVmeS4iuS8oOWSjOWbvuWDj+S/oeaBrwogICAgICAgICAgICAgIHJlbW92ZURpYWxvZ1RhYnM6ICdpbWFnZTpMaW5rO2ltYWdlOmFkdmFuY2VkJywKICAgICAgICAgICAgICAvLyDplJnor6/lpITnkIblkozkuovku7bnm5HlkKwKICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgcGx1Z2luc0xvYWRlZDogZnVuY3Rpb24oKSB7CgogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIGluc3RhbmNlUmVhZHk6IGZ1bmN0aW9uKCkgewoKCiAgICAgICAgICAgICAgICAgIGNvbnN0IGVkaXRvciA9IGV2dC5lZGl0b3IKCiAgICAgICAgICAgICAgICAgIC8vIOeugOWNleeahOWvueivneahhuWkhOeQhiAtIOWPguiAg+aCqOaPkOS+m+eahOS7o+eggemjjuagvAogICAgICAgICAgICAgICAgICBlZGl0b3Iub24oJ2RpYWxvZ1Nob3cnLCBmdW5jdGlvbihldnQpIHsKICAgICAgICAgICAgICAgICAgICBjb25zdCBkaWFsb2cgPSBldnQuZGF0YQogICAgICAgICAgICAgICAgICAgIGlmIChkaWFsb2cuZ2V0TmFtZSgpID09PSAnaW1hZ2UnKSB7CgoKICAgICAgICAgICAgICAgICAgICAgIC8vIOeugOWNleajgOafpeS4iuS8oOWujOaIkOW5tuWIh+aNouagh+etvumhtQogICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHVybEZpZWxkID0gZGlhbG9nLmdldENvbnRlbnRFbGVtZW50KCdpbmZvJywgJ3R4dFVybCcpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXJsRmllbGQgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKSAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpLnN0YXJ0c1dpdGgoJy8nKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpCgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDliIfmjaLliLDlm77lg4/kv6Hmga/moIfnrb7pobUKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgICAgfSwgNTAwKQoKICAgICAgICAgICAgICAgICAgICAgICAgLy8gMTDnp5LlkI7lgZzmraLmo4Dmn6UKICAgICAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBjbGVhckludGVydmFsKGNoZWNrSW50ZXJ2YWwpLCAxMDAwMCkKICAgICAgICAgICAgICAgICAgICAgIH0sIDEwMDApCiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgICAgfSwKCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKCgogICAgICAgICAgICAvLyDlsJ3or5XnroDljJbphY3nva4KICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3IgPSB3aW5kb3cuQ0tFRElUT1IucmVwbGFjZSgncmljaC1lZGl0b3ItdGV4dGFyZWEnLCB7CiAgICAgICAgICAgICAgICBoZWlnaHQ6ICdjYWxjKDEwMHZoIC0gMjAwcHgpJywKICAgICAgICAgICAgICAgIHRvb2xiYXI6IFsKICAgICAgICAgICAgICAgICAgWydCb2xkJywgJ0l0YWxpYycsICdVbmRlcmxpbmUnLCAnU3RyaWtlJ10sCiAgICAgICAgICAgICAgICAgIFsnTnVtYmVyZWRMaXN0JywgJ0J1bGxldGVkTGlzdCddLAogICAgICAgICAgICAgICAgICBbJ091dGRlbnQnLCAnSW5kZW50J10sCiAgICAgICAgICAgICAgICAgIFsnVW5kbycsICdSZWRvJ10sCiAgICAgICAgICAgICAgICAgIFsnTGluaycsICdVbmxpbmsnXSwKICAgICAgICAgICAgICAgICAgWydJbWFnZScsICdSZW1vdmVGb3JtYXQnLCAnTWF4aW1pemUnXQogICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgIHJlbW92ZUJ1dHRvbnM6ICcnLAogICAgICAgICAgICAgICAgbGFuZ3VhZ2U6ICd6aC1jbicsCiAgICAgICAgICAgICAgICByZW1vdmVQbHVnaW5zOiAnZWxlbWVudHNwYXRoJywKICAgICAgICAgICAgICAgIHJlc2l6ZV9lbmFibGVkOiBmYWxzZSwKICAgICAgICAgICAgICAgIGV4dHJhUGx1Z2luczogJ2ltYWdlJywKICAgICAgICAgICAgICAgIGFsbG93ZWRDb250ZW50OiB0cnVlLAogICAgICAgICAgICAgICAgLy8g5Zu+5YOP5LiK5Lyg6YWN572uIC0g5Y+C6ICD5oKo5o+Q5L6b55qE5qCH5YeG6YWN572uCiAgICAgICAgICAgICAgICBmaWxlYnJvd3NlclVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvY29tbW9uL3VwbG9hZEltYWdlJywKICAgICAgICAgICAgICAgIGltYWdlX3ByZXZpZXdUZXh0OiAnICcsCiAgICAgICAgICAgICAgICAvLyDorr7nva7ln7rnoYDot6/lvoTvvIzorqnnm7jlr7not6/lvoTog73mraPnoa7op6PmnpDliLDlkI7nq6/mnI3liqHlmagKICAgICAgICAgICAgICAgIGJhc2VIcmVmOiAnaHR0cDovL2xvY2FsaG9zdDo4ODAyLycsCiAgICAgICAgICAgICAgICAvLyDpmpDol4/kuI3pnIDopoHnmoTmoIfnrb7pobXvvIzlj6rkv53nlZnkuIrkvKDlkozlm77lg4/kv6Hmga8KICAgICAgICAgICAgICAgIHJlbW92ZURpYWxvZ1RhYnM6ICdpbWFnZTpMaW5rO2ltYWdlOmFkdmFuY2VkJywKICAgICAgICAgICAgICAgIC8vIOa3u+WKoOWunuS+i+Wwsee7quS6i+S7tuWkhOeQhgogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgaW5zdGFuY2VSZWFkeTogZnVuY3Rpb24oZXZ0KSB7CgoKICAgICAgICAgICAgICAgICAgICBjb25zdCBlZGl0b3IgPSBldnQuZWRpdG9yCgogICAgICAgICAgICAgICAgICAgIC8vIOebkeWQrOWvueivneahhuaYvuekuuS6i+S7tgogICAgICAgICAgICAgICAgICAgIGVkaXRvci5vbignZGlhbG9nU2hvdycsIGZ1bmN0aW9uKGV2dCkgewogICAgICAgICAgICAgICAgICAgICAgY29uc3QgZGlhbG9nID0gZXZ0LmRhdGEKICAgICAgICAgICAgICAgICAgICAgIGlmIChkaWFsb2cuZ2V0TmFtZSgpID09PSAnaW1hZ2UnKSB7CgoKICAgICAgICAgICAgICAgICAgICAgICAgLy8g566A5Y2V5qOA5p+l5LiK5Lyg5a6M5oiQ5bm25YiH5o2i5qCH562+6aG1CiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNoZWNrSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cmxGaWVsZCA9IGRpYWxvZy5nZXRDb250ZW50RWxlbWVudCgnaW5mbycsICd0eHRVcmwnKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodXJsRmllbGQgJiYgdXJsRmllbGQuZ2V0VmFsdWUoKSAmJiB1cmxGaWVsZC5nZXRWYWx1ZSgpLnN0YXJ0c1dpdGgoJy8nKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCkKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5YiH5o2i5Yiw5Zu+5YOP5L+h5oGv5qCH562+6aG1CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlhbG9nLnNlbGVjdFBhZ2UoJ2luZm8nKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOW/veeVpemUmeivrwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDUwMCkKCiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMTDnp5LlkI7lgZzmraLmo4Dmn6UKICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IGNsZWFySW50ZXJ2YWwoY2hlY2tJbnRlcnZhbCksIDEwMDAwKQogICAgICAgICAgICAgICAgICAgICAgICB9LCAxMDAwKQogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pCgoKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pCgogICAgICAgICAgICB9IGNhdGNoIChmYWxsYmFja0Vycm9yKSB7CgogICAgICAgICAgICAgIHRoaXMuc2hvd0ZhbGxiYWNrRWRpdG9yID0gdHJ1ZQogICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgLy8g55uR5ZCs5YaF5a655Y+Y5YyWCiAgICAgICAgICBpZiAodGhpcy5yaWNoRWRpdG9yICYmIHRoaXMucmljaEVkaXRvci5vbikgewogICAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iub24oJ2NoYW5nZScsICgpID0+IHsKICAgICAgICAgICAgICBjb25zdCByYXdDb250ZW50ID0gdGhpcy5yaWNoRWRpdG9yLmdldERhdGEoKQogICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzID0gdGhpcy5jb252ZXJ0VXJsc1RvUmVsYXRpdmUocmF3Q29udGVudCkKCiAgICAgICAgICAgICAgLy8g5L+d5a2YSFRNTOWGheWuueeUqOS6jumihOiniAogICAgICAgICAgICAgIHRoaXMuZG9jdW1lbnRIdG1sQ29udGVudCA9IHRoaXMucHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcoY29udGVudFdpdGhSZWxhdGl2ZVVybHMpCiAgICAgICAgICAgICAgLy8g5L+d5a2Y57qv5paH5pys5YaF5a6555So5LqO6Kej5p6QCiAgICAgICAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSB0aGlzLnN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzKQoKCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CgogICAgICAgICAgLy8g55uR5ZCs5oyJ6ZSu5LqL5Lu2CiAgICAgICAgICB0aGlzLnJpY2hFZGl0b3Iub24oJ2tleScsICgpID0+IHsKICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgICAgY29uc3QgcmF3Q29udGVudCA9IHRoaXMucmljaEVkaXRvci5nZXREYXRhKCkKICAgICAgICAgICAgICBjb25zdCBjb250ZW50V2l0aFJlbGF0aXZlVXJscyA9IHRoaXMuY29udmVydFVybHNUb1JlbGF0aXZlKHJhd0NvbnRlbnQpCgogICAgICAgICAgICAgIC8vIOS/neWtmEhUTUzlhoXlrrnnlKjkuo7pooTop4gKICAgICAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSB0aGlzLnByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nKGNvbnRlbnRXaXRoUmVsYXRpdmVVcmxzKQogICAgICAgICAgICAgIC8vIOS/neWtmOe6r+aWh+acrOWGheWuueeUqOS6juino+aekAogICAgICAgICAgICAgIHRoaXMuZG9jdW1lbnRDb250ZW50ID0gdGhpcy5zdHJpcEh0bWxUYWdzS2VlcEltYWdlcyhjb250ZW50V2l0aFJlbGF0aXZlVXJscykKCgogICAgICAgICAgICB9LCAxMDApCiAgICAgICAgICB9KQoKICAgICAgICAgIC8vIOebkeWQrOWunuS+i+WHhuWkh+Wwsee7qgogICAgICAgICAgdGhpcy5yaWNoRWRpdG9yLm9uKCdpbnN0YW5jZVJlYWR5JywgKCkgPT4gewogICAgICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZQogICAgICAgICAgICAvLyDnvJbovpHlmajliJ3lp4vljJblrozmiJAKICAgICAgICAgIH0pCiAgICAgICAgfSkKCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CgogICAgICAgIC8vIOWmguaenENLRWRpdG9y5Yid5aeL5YyW5aSx6LSl77yM5Zue6YCA5Yiw5pmu6YCa5paH5pys5qGGCiAgICAgICAgdGhpcy5mYWxsYmFja1RvVGV4dGFyZWEoKQogICAgICB9CiAgICB9LAoKICAgIC8vIOWbnumAgOWIsOaZrumAmuaWh+acrOahhgogICAgZmFsbGJhY2tUb1RleHRhcmVhKCkgewogICAgICBjb25zdCBlZGl0b3JDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncmljaC1lZGl0b3InKQogICAgICBpZiAoZWRpdG9yQ29udGFpbmVyKSB7CiAgICAgICAgY29uc3QgdGV4dGFyZWEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCd0ZXh0YXJlYScpCiAgICAgICAgdGV4dGFyZWEuY2xhc3NOYW1lID0gJ2ZhbGxiYWNrLXRleHRhcmVhJwogICAgICAgIHRleHRhcmVhLnBsYWNlaG9sZGVyID0gJ+ivt+WcqOatpOWkhOeymOi0tOaIlui+k+WFpemimOebruWGheWuuS4uLicKICAgICAgICB0ZXh0YXJlYS52YWx1ZSA9IHRoaXMuZG9jdW1lbnRDb250ZW50IHx8ICcnCiAgICAgICAgdGV4dGFyZWEuc3R5bGUuY3NzVGV4dCA9ICd3aWR0aDogMTAwJTsgaGVpZ2h0OiA0MDBweDsgYm9yZGVyOiAxcHggc29saWQgI2RkZDsgcGFkZGluZzogMTBweDsgZm9udC1mYW1pbHk6ICJDb3VyaWVyIE5ldyIsIG1vbm9zcGFjZTsgZm9udC1zaXplOiAxNHB4OyBsaW5lLWhlaWdodDogMS42OyByZXNpemU6IG5vbmU7JwoKICAgICAgICAvLyDnm5HlkKzlhoXlrrnlj5jljJYKICAgICAgICB0ZXh0YXJlYS5hZGRFdmVudExpc3RlbmVyKCdpbnB1dCcsIChlKSA9PiB7CiAgICAgICAgICB0aGlzLmRvY3VtZW50Q29udGVudCA9IGUudGFyZ2V0LnZhbHVlCiAgICAgICAgICB0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgPSBlLnRhcmdldC52YWx1ZSAvLyDnuq/mlofmnKzmqKHlvI/kuItIVE1M5YaF5a655LiO5paH5pys5YaF5a6555u45ZCMCgogICAgICAgIH0pCgogICAgICAgIGVkaXRvckNvbnRhaW5lci5pbm5lckhUTUwgPSAnJwogICAgICAgIGVkaXRvckNvbnRhaW5lci5hcHBlbmRDaGlsZCh0ZXh0YXJlYSkKICAgICAgICB0aGlzLmVkaXRvckluaXRpYWxpemVkID0gdHJ1ZQogICAgICB9CiAgICB9LAoKICAgIC8vIOWOu+mZpEhUTUzmoIfnrb4KICAgIHN0cmlwSHRtbFRhZ3MoaHRtbCkgewogICAgICBjb25zdCBkaXYgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKQogICAgICBkaXYuaW5uZXJIVE1MID0gaHRtbAogICAgICByZXR1cm4gZGl2LnRleHRDb250ZW50IHx8IGRpdi5pbm5lclRleHQgfHwgJycKICAgIH0sCgogICAgLy8g6K6+572u57yW6L6R5Zmo5YaF5a65CiAgICBzZXRFZGl0b3JDb250ZW50KGNvbnRlbnQpIHsKCiAgICAgIGlmICh0aGlzLnJpY2hFZGl0b3IgJiYgdGhpcy5lZGl0b3JJbml0aWFsaXplZCkgewogICAgICAgIHRoaXMucmljaEVkaXRvci5zZXREYXRhKGNvbnRlbnQpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5aaC5p6c57yW6L6R5Zmo6L+Y5pyq5Yid5aeL5YyW77yM5L+d5a2Y5YaF5a65562J5b6F5Yid5aeL5YyW5a6M5oiQ5ZCO6K6+572uCiAgICAgICAgdGhpcy5kb2N1bWVudENvbnRlbnQgPSBjb250ZW50CiAgICAgICAgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50ID0gY29udGVudCAvLyDlkIzml7borr7nva5IVE1M5YaF5a65CiAgICAgIH0KICAgIH0sCgoKCiAgICAvLyDpmLLmipblh73mlbAKICAgIGRlYm91bmNlKGZ1bmMsIHdhaXQpIHsKICAgICAgbGV0IHRpbWVvdXQKICAgICAgcmV0dXJuIGZ1bmN0aW9uIGV4ZWN1dGVkRnVuY3Rpb24oLi4uYXJncykgewogICAgICAgIGNvbnN0IGxhdGVyID0gKCkgPT4gewogICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVvdXQpCiAgICAgICAgICBmdW5jKC4uLmFyZ3MpCiAgICAgICAgfQogICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KQogICAgICAgIHRpbWVvdXQgPSBzZXRUaW1lb3V0KGxhdGVyLCB3YWl0KQogICAgICB9CiAgICB9LAoKICAgIC8vIOWwhue8lui+keWZqOWGheWuueS4reeahOWujOaVtFVSTOi9rOaNouS4uuebuOWvuei3r+W+hAogICAgY29udmVydFVybHNUb1JlbGF0aXZlKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50KSByZXR1cm4gY29udGVudAoKICAgICAgLy8g5Yy56YWN5b2T5YmN5Z+f5ZCN55qE5a6M5pW0VVJM5bm26L2s5o2i5Li655u45a+56Lev5b6ECiAgICAgIGNvbnN0IGN1cnJlbnRPcmlnaW4gPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luCiAgICAgIGNvbnN0IHVybFJlZ2V4ID0gbmV3IFJlZ0V4cChjdXJyZW50T3JpZ2luLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXVxcXS9nLCAnXFwkJicpICsgJygvW14iXCdcXHM+XSopJywgJ2cnKQoKICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSh1cmxSZWdleCwgJyQxJykKICAgIH0sCgogICAgLy8g6Kej5p6Q5paH5qGjCiAgICBwYXJzZURvY3VtZW50KCkgewogICAgICBpZiAoIXRoaXMuZG9jdW1lbnRDb250ZW50LnRyaW0oKSkgewogICAgICAgIHRoaXMucGFyc2VkUXVlc3Rpb25zID0gW10KICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgICByZXR1cm4KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJzZVJlc3VsdCA9IHRoaXMucGFyc2VRdWVzdGlvbkNvbnRlbnQodGhpcy5kb2N1bWVudENvbnRlbnQpCiAgICAgICAgLy8g5Li65q+P5Liq6aKY55uu5re75YqgY29sbGFwc2Vk5bGe5oCnCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBwYXJzZVJlc3VsdC5xdWVzdGlvbnMubWFwKHF1ZXN0aW9uID0+ICh7CiAgICAgICAgICAuLi5xdWVzdGlvbiwKICAgICAgICAgIGNvbGxhcHNlZDogZmFsc2UKICAgICAgICB9KSkKICAgICAgICB0aGlzLnBhcnNlRXJyb3JzID0gcGFyc2VSZXN1bHQuZXJyb3JzCgoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKCiAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFsn6Kej5p6Q5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2VdCiAgICAgICAgdGhpcy5wYXJzZWRRdWVzdGlvbnMgPSBbXQogICAgICB9CiAgICB9LAoKICAgIC8vIOino+aekOmimOebruWGheWuuSAtIOS8mOWMlueJiOacrO+8jOabtOWKoOWBpeWjrgogICAgcGFyc2VRdWVzdGlvbkNvbnRlbnQoY29udGVudCkgewogICAgICBjb25zdCBxdWVzdGlvbnMgPSBbXQogICAgICBjb25zdCBlcnJvcnMgPSBbXQoKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewoKICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyfop6PmnpDlhoXlrrnkuLrnqbrmiJbmoLzlvI/kuI3mraPnoa4nXSB9CiAgICAgIH0KCiAgICAgIHRyeSB7CgoKICAgICAgICAvLyDkv53nlZnlm77niYfmoIfnrb7vvIzlj6rnp7vpmaTlhbbku5ZIVE1M5qCH562+CiAgICAgICAgY29uc3QgdGV4dENvbnRlbnQgPSB0aGlzLnN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnQpCgogICAgICAgIGlmICghdGV4dENvbnRlbnQgfHwgdGV4dENvbnRlbnQudHJpbSgpLmxlbmd0aCA9PT0gMCkgewoKICAgICAgICAgIHJldHVybiB7IHF1ZXN0aW9ucywgZXJyb3JzOiBbJ+WkhOeQhuWQjueahOWGheWuueS4uuepuiddIH0KICAgICAgICB9CgogICAgICAgIC8vIOaMieihjOWIhuWJsuWGheWuuQogICAgICAgIGNvbnN0IGxpbmVzID0gdGV4dENvbnRlbnQuc3BsaXQoJ1xuJykubWFwKGxpbmUgPT4gbGluZS50cmltKCkpLmZpbHRlcihsaW5lID0+IGxpbmUubGVuZ3RoID4gMCkKCgogICAgICAgIGlmIChsaW5lcy5sZW5ndGggPT09IDApIHsKCiAgICAgICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9yczogWyfmsqHmnInmnInmlYjnmoTlhoXlrrnooYwnXSB9CiAgICAgICAgfQoKCgogICAgICAgIGxldCBjdXJyZW50UXVlc3Rpb25MaW5lcyA9IFtdCiAgICAgICAgbGV0IHF1ZXN0aW9uTnVtYmVyID0gMAoKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/popjnm67lvIDlp4vooYzvvJrmlbDlrZfjgIFb6aKY55uu57G75Z6LXSDmiJYgW+mimOebruexu+Wei10KICAgICAgICAgIGNvbnN0IGlzUXVlc3Rpb25TdGFydCA9IHRoaXMuaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSB8fCB0aGlzLmlzUXVlc3Rpb25UeXBlU3RhcnQobGluZSkKCiAgICAgICAgICBpZiAoaXNRdWVzdGlvblN0YXJ0KSB7CiAgICAgICAgICAgIC8vIOWmguaenOS5i+WJjeaciemimOebruWGheWuue+8jOWFiOWkhOeQhuS5i+WJjeeahOmimOebrgogICAgICAgICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICBjb25zdCBxdWVzdGlvblRleHQgPSBjdXJyZW50UXVlc3Rpb25MaW5lcy5qb2luKCdcbicpCiAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWRRdWVzdGlvbiA9IHRoaXMucGFyc2VRdWVzdGlvbkZyb21MaW5lcyhxdWVzdGlvblRleHQsIHF1ZXN0aW9uTnVtYmVyKQogICAgICAgICAgICAgICAgaWYgKHBhcnNlZFF1ZXN0aW9uKSB7CiAgICAgICAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKHBhcnNlZFF1ZXN0aW9uKQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgICAgICBlcnJvcnMucHVzaChg56ysICR7cXVlc3Rpb25OdW1iZXJ9IOmimOino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQoKICAgICAgICAgICAgLy8g5byA5aeL5paw6aKY55uuCiAgICAgICAgICAgIGN1cnJlbnRRdWVzdGlvbkxpbmVzID0gW2xpbmVdCiAgICAgICAgICAgIHF1ZXN0aW9uTnVtYmVyKysKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIC8vIOWmguaenOW9k+WJjeWcqOWkhOeQhumimOebruS4re+8jOa3u+WKoOWIsOW9k+WJjemimOebrgogICAgICAgICAgICBpZiAoY3VycmVudFF1ZXN0aW9uTGluZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgIGN1cnJlbnRRdWVzdGlvbkxpbmVzLnB1c2gobGluZSkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLy8g5aSE55CG5pyA5ZCO5LiA5Liq6aKY55uuCiAgICAgICAgaWYgKGN1cnJlbnRRdWVzdGlvbkxpbmVzLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGNvbnN0IHF1ZXN0aW9uVGV4dCA9IGN1cnJlbnRRdWVzdGlvbkxpbmVzLmpvaW4oJ1xuJykKICAgICAgICAgICAgY29uc3QgcGFyc2VkUXVlc3Rpb24gPSB0aGlzLnBhcnNlUXVlc3Rpb25Gcm9tTGluZXMocXVlc3Rpb25UZXh0LCBxdWVzdGlvbk51bWJlcikKICAgICAgICAgICAgaWYgKHBhcnNlZFF1ZXN0aW9uKSB7CiAgICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2gocGFyc2VkUXVlc3Rpb24pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIGVycm9ycy5wdXNoKGDnrKwgJHtxdWVzdGlvbk51bWJlcn0g6aKY6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKCiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBlcnJvcnMucHVzaChg5paH5qGj6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5paH5qGj6Kej5p6Q5aSx6LSlOicsIGVycm9yKQogICAgICB9CgogICAgICBjb25zb2xlLmxvZygn6Kej5p6Q5a6M5oiQ77yM5YWxJywgcXVlc3Rpb25zLmxlbmd0aCwgJ+mBk+mimOebru+8jCcsIGVycm9ycy5sZW5ndGgsICfkuKrplJnor68nKQogICAgICByZXR1cm4geyBxdWVzdGlvbnMsIGVycm9ycyB9CiAgICB9LAoKICAgIC8vIOWIpOaWreaYr+WQpuS4uumimOebruW8gOWni+ihjCAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8muavj+mimOWJjemdoumcgOimgeWKoOS4iumimOWPt+agh+ivhu+8jOmimOWPt+WQjumdoumcgOimgeWKoOS4iuespuWPt++8iDrvvJrjgIEu77yO77yJCiAgICAgIC8vIOWMuemFjeagvOW8j++8muaVsOWtlyArIOespuWPtyg677ya44CBLu+8jikgKyDlj6/pgInnqbrmoLwKICAgICAgLy8g5L6L5aaC77yaMS4gMeOAgSAx77yaIDHvvI4g562JCiAgICAgIHJldHVybiAvXlxkK1suOu+8mu+8juOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66aKY5Z6L5qCH5rOo5byA5aeL6KGMCiAgICBpc1F1ZXN0aW9uVHlwZVN0YXJ0KGxpbmUpIHsKICAgICAgLy8g5Yy56YWN5qC85byP77yaW+mimOebruexu+Wei10KICAgICAgLy8g5L6L5aaC77yaW+WNlemAiemimF0gW+WkmumAiemimF0gW+WIpOaWremimF0g562JCiAgICAgIHJldHVybiAvXlxbLio/6aKYXF0vLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5LuO6KGM5pWw57uE6Kej5p6Q5Y2V5Liq6aKY55uuIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBwYXJzZVF1ZXN0aW9uRnJvbUxpbmVzKHF1ZXN0aW9uVGV4dCkgewogICAgICBjb25zdCBsaW5lcyA9IHF1ZXN0aW9uVGV4dC5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKGxpbmUgPT4gbGluZS5sZW5ndGggPiAwKQoKICAgICAgaWYgKGxpbmVzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRocm93IG5ldyBFcnJvcign6aKY55uu5YaF5a655Li656m6JykKICAgICAgfQoKICAgICAgbGV0IHF1ZXN0aW9uVHlwZSA9ICdqdWRnbWVudCcgLy8g6buY6K6k5Yik5pat6aKYCiAgICAgIGxldCBxdWVzdGlvbkNvbnRlbnQgPSAnJwogICAgICBsZXQgY29udGVudFN0YXJ0SW5kZXggPSAwCgogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInpopjlnovmoIfms6jvvIjlpoIgW+WNlemAiemimF3jgIFb5aSa6YCJ6aKYXeOAgVvliKTmlq3pophd77yJCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KICAgICAgICBjb25zdCB0eXBlTWF0Y2ggPSBsaW5lLm1hdGNoKC9cWyguKj/popgpXF0vKQogICAgICAgIGlmICh0eXBlTWF0Y2gpIHsKICAgICAgICAgIGNvbnN0IHR5cGVUZXh0ID0gdHlwZU1hdGNoWzFdCgogICAgICAgICAgLy8g6L2s5o2i6aKY55uu57G75Z6LCiAgICAgICAgICBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+WIpOaWrScpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdqdWRnbWVudCcKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+WNlemAiScpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdzaW5nbGUnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCflpJrpgIknKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnbXVsdGlwbGUnCiAgICAgICAgICB9IGVsc2UgaWYgKHR5cGVUZXh0LmluY2x1ZGVzKCfloavnqbonKSkgewogICAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnZmlsbCcKICAgICAgICAgIH0gZWxzZSBpZiAodHlwZVRleHQuaW5jbHVkZXMoJ+eugOetlCcpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uVHlwZSA9ICdlc3NheScKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDlpoLmnpzpopjlnovmoIfms6jlkozpopjnm67lhoXlrrnlnKjlkIzkuIDooYwKICAgICAgICAgIGNvbnN0IHJlbWFpbmluZ0NvbnRlbnQgPSBsaW5lLnJlcGxhY2UoL1xbLio/6aKYXF0vLCAnJykudHJpbSgpCiAgICAgICAgICBpZiAocmVtYWluaW5nQ29udGVudCkgewogICAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSByZW1haW5pbmdDb250ZW50CiAgICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnRlbnRTdGFydEluZGV4ID0gaSArIDEKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpoLmnpzmsqHmnInmib7liLDpopjlnovmoIfms6jvvIzku47nrKzkuIDooYzlvIDlp4vop6PmnpAKICAgICAgaWYgKGNvbnRlbnRTdGFydEluZGV4ID09PSAwKSB7CiAgICAgICAgY29udGVudFN0YXJ0SW5kZXggPSAwCiAgICAgIH0KCiAgICAgIC8vIOaPkOWPlumimOebruWGheWuue+8iOS7jumimOWPt+ihjOW8gOWni++8iQogICAgICBmb3IgKGxldCBpID0gY29udGVudFN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDlpoLmnpzmmK/popjlj7fooYzvvIzmj5Dlj5bpopjnm67lhoXlrrnvvIjnp7vpmaTpopjlj7fvvIkKICAgICAgICBpZiAodGhpcy5pc1F1ZXN0aW9uU3RhcnRMaW5lKGxpbmUpKSB7CiAgICAgICAgICAvLyDnp7vpmaTpopjlj7fvvIzmj5Dlj5bpopjnm67lhoXlrrkKICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IGxpbmUucmVwbGFjZSgvXlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICBicmVhawogICAgICAgIH0gZWxzZSBpZiAoIXF1ZXN0aW9uQ29udGVudCkgewogICAgICAgICAgLy8g5aaC5p6c6L+Y5rKh5pyJ6aKY55uu5YaF5a6577yM5b2T5YmN6KGM5bCx5piv6aKY55uu5YaF5a65CiAgICAgICAgICBxdWVzdGlvbkNvbnRlbnQgPSBsaW5lCiAgICAgICAgICBjb250ZW50U3RhcnRJbmRleCA9IGkgKyAxCiAgICAgICAgICBicmVhawogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g57un57ut5pS26ZuG6aKY55uu5YaF5a6577yI55u05Yiw6YGH5Yiw6YCJ6aG55oiW562U5qGI77yJCiAgICAgIGZvciAobGV0IGkgPSBjb250ZW50U3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgIC8vIOWmguaenOmBh+WIsOmAiemhueihjOOAgeetlOahiOihjOOAgeino+aekOihjOaIlumavuW6puihjO+8jOWBnOatouaUtumbhumimOebruWGheWuuQogICAgICAgIGlmICh0aGlzLmlzT3B0aW9uTGluZShsaW5lKSB8fCB0aGlzLmlzQW5zd2VyTGluZShsaW5lKSB8fAogICAgICAgICAgICB0aGlzLmlzRXhwbGFuYXRpb25MaW5lKGxpbmUpIHx8IHRoaXMuaXNEaWZmaWN1bHR5TGluZShsaW5lKSkgewogICAgICAgICAgYnJlYWsKICAgICAgICB9CgogICAgICAgIC8vIOe7p+e7rea3u+WKoOWIsOmimOebruWGheWuue+8jOS9huimgeehruS/neS4jeWMheWQq+mimOWPtwogICAgICAgIGxldCBjbGVhbkxpbmUgPSBsaW5lCiAgICAgICAgLy8g5aaC5p6c6L+Z6KGM6L+Y5YyF5ZCr6aKY5Y+377yM56e76Zmk5a6DCiAgICAgICAgaWYgKHRoaXMuaXNRdWVzdGlvblN0YXJ0TGluZShsaW5lKSkgewogICAgICAgICAgY2xlYW5MaW5lID0gbGluZS5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICB9CgogICAgICAgIGlmIChjbGVhbkxpbmUpIHsKICAgICAgICAgIGlmIChxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICAgICAgcXVlc3Rpb25Db250ZW50ICs9ICdcbicgKyBjbGVhbkxpbmUKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IGNsZWFuTGluZQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgaWYgKCFxdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+aXoOazleaPkOWPlumimOebruWGheWuuScpCiAgICAgIH0KCiAgICAgIC8vIOacgOe7iOa4heeQhu+8muehruS/nemimOebruWGheWuueS4jeWMheWQq+mimOWPtwogICAgICBsZXQgZmluYWxRdWVzdGlvbkNvbnRlbnQgPSBxdWVzdGlvbkNvbnRlbnQudHJpbSgpCiAgICAgIC8vIOS9v+eUqOabtOW8uueahOa4heeQhumAu+i+ke+8jOWkmuasoea4heeQhuehruS/neW9u+W6leenu+mZpOmimOWPtwogICAgICB3aGlsZSAoL15ccypcZCtbLjrvvJrvvI7jgIFdLy50ZXN0KGZpbmFsUXVlc3Rpb25Db250ZW50KSkgewogICAgICAgIGZpbmFsUXVlc3Rpb25Db250ZW50ID0gZmluYWxRdWVzdGlvbkNvbnRlbnQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgIH0KCiAgICAgIC8vIOmineWklua4heeQhu+8muenu+mZpOWPr+iDveeahEhUTUzmoIfnrb7lhoXnmoTpopjlj7cKICAgICAgaWYgKGZpbmFsUXVlc3Rpb25Db250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICBmaW5hbFF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIoZmluYWxRdWVzdGlvbkNvbnRlbnQpCiAgICAgIH0KCiAgICAgIGNvbnN0IHF1ZXN0aW9uID0gewogICAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgIHR5cGU6IHF1ZXN0aW9uVHlwZSwKICAgICAgICB0eXBlTmFtZTogdGhpcy5nZXRUeXBlRGlzcGxheU5hbWUocXVlc3Rpb25UeXBlKSwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IGZpbmFsUXVlc3Rpb25Db250ZW50LAogICAgICAgIGNvbnRlbnQ6IGZpbmFsUXVlc3Rpb25Db250ZW50LAogICAgICAgIGRpZmZpY3VsdHk6ICcnLCAvLyDkuI3orr7nva7pu5jorqTlgLwKICAgICAgICBleHBsYW5hdGlvbjogJycsCiAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgY29ycmVjdEFuc3dlcjogJycsCiAgICAgICAgY29sbGFwc2VkOiBmYWxzZSAgLy8g6buY6K6k5bGV5byACiAgICAgIH0KCiAgICAgIC8vIOino+aekOmAiemhue+8iOWvueS6jumAieaLqemimO+8iQogICAgICBjb25zdCBvcHRpb25SZXN1bHQgPSB0aGlzLnBhcnNlT3B0aW9uc0Zyb21MaW5lcyhsaW5lcywgMCkKICAgICAgcXVlc3Rpb24ub3B0aW9ucyA9IG9wdGlvblJlc3VsdC5vcHRpb25zCgogICAgICAvLyDmoLnmja7pgInpobnmlbDph4/mjqjmlq3popjnm67nsbvlnovvvIjlpoLmnpzkuYvliY3msqHmnInmmI7noa7moIfms6jvvIkKICAgICAgaWYgKHF1ZXN0aW9uVHlwZSA9PT0gJ2p1ZGdtZW50JyAmJiBxdWVzdGlvbi5vcHRpb25zLmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDlpoLmnpzmnInpgInpobnvvIzmjqjmlq3kuLrpgInmi6npopgKICAgICAgICBxdWVzdGlvblR5cGUgPSAnc2luZ2xlJyAgLy8g6buY6K6k5Li65Y2V6YCJ6aKYCiAgICAgICAgcXVlc3Rpb24ucXVlc3Rpb25UeXBlID0gcXVlc3Rpb25UeXBlCiAgICAgICAgcXVlc3Rpb24udHlwZSA9IHF1ZXN0aW9uVHlwZQogICAgICAgIHF1ZXN0aW9uLnR5cGVOYW1lID0gdGhpcy5nZXRUeXBlRGlzcGxheU5hbWUocXVlc3Rpb25UeXBlKQogICAgICB9CgogICAgICAvLyDop6PmnpDnrZTmoYjjgIHop6PmnpDjgIHpmr7luqYKICAgICAgdGhpcy5wYXJzZVF1ZXN0aW9uTWV0YUZyb21MaW5lcyhsaW5lcywgcXVlc3Rpb24pCgogICAgICAvLyDmoLnmja7nrZTmoYjplb/luqbov5vkuIDmraXmjqjmlq3pgInmi6npopjnsbvlnosKICAgICAgaWYgKHF1ZXN0aW9uVHlwZSA9PT0gJ3NpbmdsZScgJiYgcXVlc3Rpb24uY29ycmVjdEFuc3dlciAmJiBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyLmxlbmd0aCA+IDEpIHsKICAgICAgICAvLyDlpoLmnpznrZTmoYjljIXlkKvlpJrkuKrlrZfmr43vvIzmjqjmlq3kuLrlpJrpgInpopgKICAgICAgICBpZiAoL15bQS1aXXsyLH0kLy50ZXN0KHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIpKSB7CiAgICAgICAgICBxdWVzdGlvblR5cGUgPSAnbXVsdGlwbGUnCiAgICAgICAgICBxdWVzdGlvbi5xdWVzdGlvblR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICAgIHF1ZXN0aW9uLnR5cGUgPSBxdWVzdGlvblR5cGUKICAgICAgICAgIHF1ZXN0aW9uLnR5cGVOYW1lID0gdGhpcy5nZXRUeXBlRGlzcGxheU5hbWUocXVlc3Rpb25UeXBlKQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5pyA57uI5riF55CG77ya56Gu5L+d6aKY55uu5YaF5a655a6M5YWo5rKh5pyJ6aKY5Y+3CiAgICAgIHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIocXVlc3Rpb24ucXVlc3Rpb25Db250ZW50KQogICAgICBxdWVzdGlvbi5jb250ZW50ID0gcXVlc3Rpb24ucXVlc3Rpb25Db250ZW50CgogICAgICByZXR1cm4gcXVlc3Rpb24KICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66YCJ6aG56KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc09wdGlvbkxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrpgInpobnmoLzlvI/vvIhBOu+8ie+8jOWtl+avjeWPr+S7peS4ukHliLBa55qE5Lu75oSP5aSn5bCP5YaZ5a2X5q+N77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6IjrvvJrjgIEu77yOIuWFtuS4reS5i+S4gAogICAgICByZXR1cm4gL15bQS1aYS16XVsuOu+8mu+8juOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li6562U5qGI6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc0Fuc3dlckxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrmmL7lvI/moIfms6jmoLzlvI/vvIjnrZTmoYjvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXuetlOahiFsuOu+8muOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66Kej5p6Q6KGMIC0g5oyJ54Wn6L6T5YWl6KeE6IyDCiAgICBpc0V4cGxhbmF0aW9uTGluZShsaW5lKSB7CiAgICAgIC8vIOinhOiMg++8muino+aekOagvOW8j++8iOino+aekO+8mu+8ie+8jOWGkuWPt+WPr+S7peabv+aNouS4uiAiOu+8muOAgSLlhbbkuK3kuYvkuIAKICAgICAgcmV0dXJuIC9e6Kej5p6QWy4677ya44CBXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpmr7luqbooYwgLSDmjInnhafovpPlhaXop4TojIMKICAgIGlzRGlmZmljdWx0eUxpbmUobGluZSkgewogICAgICAvLyDop4TojIPvvJrpmr7luqbmoLzlvI/vvIjpmr7luqbvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgIHJldHVybiAvXumavuW6plsuOu+8muOAgV1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g6I635Y+W6aKY55uu57G75Z6L5pi+56S65ZCN56ewCiAgICBnZXRUeXBlRGlzcGxheU5hbWUodHlwZSkgewogICAgICBjb25zdCB0eXBlTWFwID0gewogICAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnLAogICAgICAgICdzaW5nbGUnOiAn5Y2V6YCJ6aKYJywKICAgICAgICAnbXVsdGlwbGUnOiAn5aSa6YCJ6aKYJywKICAgICAgICAnZmlsbCc6ICfloavnqbrpopgnLAogICAgICAgICdlc3NheSc6ICfnroDnrZTpopgnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+WIpOaWremimCcKICAgIH0sCgogICAgLy8g5aSE55CG5Zu+54mH6Lev5b6E77yM5bCG55u45a+56Lev5b6E6L2s5o2i5Li65a6M5pW06Lev5b6ECiAgICBwcm9jZXNzSW1hZ2VQYXRocyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCB8fCB0eXBlb2YgY29udGVudCAhPT0gJ3N0cmluZycpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgdHJ5IHsKCiAgICAgICAgLy8g5aSE55CGaW1n5qCH562+5Lit55qE55u45a+56Lev5b6ECiAgICAgICAgY29uc3QgcHJvY2Vzc2VkQ29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZyhbXj5dKj8pc3JjPSIoW14iXSo/KSIoW14+XSo/KT4vZywgKG1hdGNoLCBiZWZvcmUsIHNyYywgYWZ0ZXIpID0+IHsKICAgICAgICAgIGlmICghc3JjKSByZXR1cm4gbWF0Y2gKCgogICAgICAgICAgLy8g5aaC5p6c5bey57uP5piv5a6M5pW06Lev5b6E77yM5LiN5aSE55CGCiAgICAgICAgICBpZiAoc3JjLnN0YXJ0c1dpdGgoJ2h0dHA6Ly8nKSB8fCBzcmMuc3RhcnRzV2l0aCgnaHR0cHM6Ly8nKSB8fCBzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSkgewogICAgICAgICAgICByZXR1cm4gbWF0Y2gKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDlpoLmnpzmmK/nm7jlr7not6/lvoTvvIzmt7vliqDlkI7nq6/mnI3liqHlmajlnLDlnYAKICAgICAgICAgIGNvbnN0IGZ1bGxTcmMgPSAnaHR0cDovL2xvY2FsaG9zdDo4ODAyJyArIChzcmMuc3RhcnRzV2l0aCgnLycpID8gc3JjIDogJy8nICsgc3JjKQogICAgICAgICAgY29uc3QgcmVzdWx0ID0gYDxpbWcke2JlZm9yZX1zcmM9IiR7ZnVsbFNyY30iJHthZnRlcn0+YAogICAgICAgICAgcmV0dXJuIHJlc3VsdAogICAgICAgIH0pCgogICAgICAgIHJldHVybiBwcm9jZXNzZWRDb250ZW50CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWkhOeQhuWbvueJh+i3r+W+hOaXtuWHuumUmTonLCBlcnJvcikKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOS/neeVmeWvjOaWh+acrOagvOW8j+eUqOS6jumihOiniOaYvuekugogICAgcHJlc2VydmVSaWNoVGV4dEZvcm1hdHRpbmcoY29udGVudCkgewogICAgICBpZiAoIWNvbnRlbnQgfHwgdHlwZW9mIGNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g5L+d55WZ5bi455So55qE5a+M5paH5pys5qC85byP5qCH562+CiAgICAgICAgbGV0IHByb2Nlc3NlZENvbnRlbnQgPSBjb250ZW50CiAgICAgICAgICAvLyDovazmjaLnm7jlr7not6/lvoTnmoTlm77niYcKICAgICAgICAgIC5yZXBsYWNlKC88aW1nKFtePl0qPylzcmM9IihbXiJdKj8pIihbXj5dKj8pPi9naSwgKG1hdGNoLCBiZWZvcmUsIHNyYywgYWZ0ZXIpID0+IHsKICAgICAgICAgICAgaWYgKCFzcmMuc3RhcnRzV2l0aCgnaHR0cCcpICYmICFzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSkgewogICAgICAgICAgICAgIGNvbnN0IGZ1bGxTcmMgPSB0aGlzLnByb2Nlc3NJbWFnZVBhdGhzKHNyYykKICAgICAgICAgICAgICByZXR1cm4gYDxpbWcke2JlZm9yZX1zcmM9IiR7ZnVsbFNyY30iJHthZnRlcn0+YAogICAgICAgICAgICB9CiAgICAgICAgICAgIHJldHVybiBtYXRjaAogICAgICAgICAgfSkKICAgICAgICAgIC8vIOS/neeVmeauteiQvee7k+aehAogICAgICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnPHA+JykKICAgICAgICAgIC5yZXBsYWNlKC88XC9wPi9naSwgJzwvcD4nKQogICAgICAgICAgLy8g5L+d55WZ5o2i6KGMCiAgICAgICAgICAucmVwbGFjZSgvPGJyXHMqXC8/Pi9naSwgJzxicj4nKQogICAgICAgICAgLy8g5riF55CG5aSa5L2Z55qE56m655m95q616JC9CiAgICAgICAgICAucmVwbGFjZSgvPHA+XHMqPFwvcD4vZ2ksICcnKQogICAgICAgICAgLnJlcGxhY2UoLyg8cD5bXHNcbl0qPFwvcD4pL2dpLCAnJykKCiAgICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQudHJpbSgpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIHByZXNlcnZlUmljaFRleHRGb3JtYXR0aW5nIOWHuumUmTonLCBlcnJvcikKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOenu+mZpEhUTUzmoIfnrb7kvYbkv53nlZnlm77niYfmoIfnrb4KICAgIHN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiAnJwogICAgICB9CgogICAgICB0cnkgewoKICAgICAgICAvLyDlhYjkv53lrZjmiYDmnInlm77niYfmoIfnrb4KICAgICAgICBjb25zdCBpbWFnZXMgPSBbXQogICAgICAgIGxldCBpbWFnZUluZGV4ID0gMAogICAgICAgIGNvbnN0IGNvbnRlbnRXaXRoUGxhY2Vob2xkZXJzID0gY29udGVudC5yZXBsYWNlKC88aW1nW14+XSo+L2dpLCAobWF0Y2gpID0+IHsKICAgICAgICAgIGltYWdlcy5wdXNoKG1hdGNoKQogICAgICAgICAgcmV0dXJuIGBcbl9fSU1BR0VfUExBQ0VIT0xERVJfJHtpbWFnZUluZGV4Kyt9X19cbmAKICAgICAgICB9KQoKICAgICAgICAvLyDnp7vpmaTlhbbku5ZIVE1M5qCH562+77yM5L2G5L+d55WZ5o2i6KGMCiAgICAgICAgbGV0IHRleHRDb250ZW50ID0gY29udGVudFdpdGhQbGFjZWhvbGRlcnMKICAgICAgICAgIC5yZXBsYWNlKC88YnJccypcLz8+L2dpLCAnXG4nKSAgLy8gYnLmoIfnrb7ovazmjaLkuLrmjaLooYwKICAgICAgICAgIC5yZXBsYWNlKC88XC9wPi9naSwgJ1xuJykgICAgICAgLy8gcOe7k+adn+agh+etvui9rOaNouS4uuaNouihjAogICAgICAgICAgLnJlcGxhY2UoLzxwW14+XSo+L2dpLCAnXG4nKSAgICAvLyBw5byA5aeL5qCH562+6L2s5o2i5Li65o2i6KGMCiAgICAgICAgICAucmVwbGFjZSgvPFtePl0qPi9nLCAnJykgICAgICAgIC8vIOenu+mZpOWFtuS7lkhUTUzmoIfnrb4KICAgICAgICAgIC5yZXBsYWNlKC9cblxzKlxuL2csICdcbicpICAgICAgLy8g5ZCI5bm25aSa5Liq5o2i6KGMCgogICAgICAgIC8vIOaBouWkjeWbvueJh+agh+etvgogICAgICAgIGxldCBmaW5hbENvbnRlbnQgPSB0ZXh0Q29udGVudAogICAgICAgIGltYWdlcy5mb3JFYWNoKChpbWcsIGluZGV4KSA9PiB7CiAgICAgICAgICBjb25zdCBwbGFjZWhvbGRlciA9IGBfX0lNQUdFX1BMQUNFSE9MREVSXyR7aW5kZXh9X19gCiAgICAgICAgICBpZiAoZmluYWxDb250ZW50LmluY2x1ZGVzKHBsYWNlaG9sZGVyKSkgewogICAgICAgICAgICBmaW5hbENvbnRlbnQgPSBmaW5hbENvbnRlbnQucmVwbGFjZShwbGFjZWhvbGRlciwgaW1nKQogICAgICAgICAgfQogICAgICAgIH0pCgogICAgICAgIHJldHVybiBmaW5hbENvbnRlbnQudHJpbSgpCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIHN0cmlwSHRtbFRhZ3NLZWVwSW1hZ2VzIOWHuumUmTonLCBlcnJvcikKICAgICAgICByZXR1cm4gY29udGVudAogICAgICB9CiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOmAiemhuSAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VPcHRpb25zRnJvbUxpbmVzKGxpbmVzLCBzdGFydEluZGV4KSB7CiAgICAgIGNvbnN0IG9wdGlvbnMgPSBbXQoKICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGxpbmVzKSB8fCBzdGFydEluZGV4IDwgMCB8fCBzdGFydEluZGV4ID49IGxpbmVzLmxlbmd0aCkgewogICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIOino+aekOmAiemhueWPguaVsOaXoOaViCcpCiAgICAgICAgcmV0dXJuIHsgb3B0aW9ucyB9CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChsZXQgaSA9IHN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgICAgaWYgKCFsaW5lIHx8IHR5cGVvZiBsaW5lICE9PSAnc3RyaW5nJykgewogICAgICAgICAgICBjb250aW51ZQogICAgICAgICAgfQoKICAgICAgICAgIC8vIOinhOiMg++8mumAiemhueagvOW8j++8iEE677yJ77yM5a2X5q+N5Y+v5Lul5Li6QeWIsFrnmoTku7vmhI/lpKflsI/lhpnlrZfmr43vvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLoiOu+8muOAgS7vvI4i5YW25Lit5LmL5LiACiAgICAgICAgICBjb25zdCBvcHRpb25NYXRjaCA9IGxpbmUubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKQogICAgICAgICAgaWYgKG9wdGlvbk1hdGNoKSB7CiAgICAgICAgICAgIGNvbnN0IG9wdGlvbktleSA9IG9wdGlvbk1hdGNoWzFdLnRvVXBwZXJDYXNlKCkKICAgICAgICAgICAgY29uc3Qgb3B0aW9uQ29udGVudCA9IG9wdGlvbk1hdGNoWzJdID8gb3B0aW9uTWF0Y2hbMl0udHJpbSgpIDogJycKCiAgICAgICAgICAgIGlmIChvcHRpb25LZXkgJiYgb3B0aW9uQ29udGVudCkgewogICAgICAgICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgICBvcHRpb25LZXk6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgIGxhYmVsOiBvcHRpb25LZXksCiAgICAgICAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb25Db250ZW50LAogICAgICAgICAgICAgICAgY29udGVudDogb3B0aW9uQ29udGVudAogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5pc0Fuc3dlckxpbmUobGluZSkgfHwgdGhpcy5pc0V4cGxhbmF0aW9uTGluZShsaW5lKSB8fCB0aGlzLmlzRGlmZmljdWx0eUxpbmUobGluZSkpIHsKICAgICAgICAgICAgLy8g6YGH5Yiw562U5qGI44CB6Kej5p6Q5oiW6Zq+5bqm6KGM77yM5YGc5q2i6Kej5p6Q6YCJ6aG5CiAgICAgICAgICAgIGJyZWFrCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAvLyDop4TojIPvvJrpgInpobnkuI7pgInpobnkuYvpl7TvvIzlj6/ku6XmjaLooYzvvIzkuZ/lj6/ku6XlnKjlkIzkuIDooYwKICAgICAgICAgICAgLy8g5aaC5p6c6YCJ6aG55Zyo5ZCM5LiA6KGM77yM6YCJ6aG55LmL6Ze06Iez5bCR6ZyA6KaB5pyJ5LiA5Liq56m65qC8CiAgICAgICAgICAgIGNvbnN0IG11bHRpcGxlT3B0aW9uc01hdGNoID0gbGluZS5tYXRjaCgvKFtBLVphLXpdWy4677ya77yO44CBXVxzKlteXHNdKyg/OlxzK1tBLVphLXpdWy4677ya77yO44CBXVxzKlteXHNdKykqKS9nKQogICAgICAgICAgICBpZiAobXVsdGlwbGVPcHRpb25zTWF0Y2gpIHsKICAgICAgICAgICAgICAvLyDlpITnkIblkIzkuIDooYzlpJrkuKrpgInpobnnmoTmg4XlhrUKICAgICAgICAgICAgICBjb25zdCBzaW5nbGVPcHRpb25zID0gbGluZS5zcGxpdCgvXHMrKD89W0EtWmEtel1bLjrvvJrvvI7jgIFdKS8pCiAgICAgICAgICAgICAgZm9yIChjb25zdCBzaW5nbGVPcHRpb24gb2Ygc2luZ2xlT3B0aW9ucykgewogICAgICAgICAgICAgICAgaWYgKCFzaW5nbGVPcHRpb24pIGNvbnRpbnVlCgogICAgICAgICAgICAgICAgY29uc3QgbWF0Y2ggPSBzaW5nbGVPcHRpb24ubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yO44CBXVxzKiguKikvKQogICAgICAgICAgICAgICAgaWYgKG1hdGNoKSB7CiAgICAgICAgICAgICAgICAgIGNvbnN0IG9wdGlvbktleSA9IG1hdGNoWzFdLnRvVXBwZXJDYXNlKCkKICAgICAgICAgICAgICAgICAgY29uc3Qgb3B0aW9uQ29udGVudCA9IG1hdGNoWzJdID8gbWF0Y2hbMl0udHJpbSgpIDogJycKCiAgICAgICAgICAgICAgICAgIGlmIChvcHRpb25LZXkgJiYgb3B0aW9uQ29udGVudCkgewogICAgICAgICAgICAgICAgICAgIG9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25LZXk6IG9wdGlvbktleSwKICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBvcHRpb25LZXksCiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb25Db250ZW50LAogICAgICAgICAgICAgICAgICAgICAgY29udGVudDogb3B0aW9uQ29udGVudAogICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOino+aekOmAiemhueaXtuWHuumUmTonLCBlcnJvcikKICAgICAgfQoKICAgICAgcmV0dXJuIHsgb3B0aW9ucyB9CiAgICB9LAoKICAgIC8vIOS7juihjOaVsOe7hOino+aekOmimOebruWFg+S/oeaBryAtIOaMieeFp+i+k+WFpeinhOiMgwogICAgcGFyc2VRdWVzdGlvbk1ldGFGcm9tTGluZXMobGluZXMsIHF1ZXN0aW9uKSB7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGluZXMubGVuZ3RoOyBpKyspIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbaV0KCiAgICAgICAgLy8g6KeE6IyD77ya5pi+5byP5qCH5rOo5qC85byP77yI562U5qGI77ya77yJ77yM5YaS5Y+35Y+v5Lul5pu/5o2i5Li6ICI677ya44CBIuWFtuS4reS5i+S4gAogICAgICAgIGNvbnN0IGFuc3dlck1hdGNoID0gbGluZS5tYXRjaCgvXuetlOahiFsuOu+8muOAgV1ccyooLispLykKICAgICAgICBpZiAoYW5zd2VyTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLnBhcnNlQW5zd2VyVmFsdWUoYW5zd2VyTWF0Y2hbMV0sIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrop6PmnpDmoLzlvI/vvIjop6PmnpDvvJrvvInvvIzlhpLlj7flj6/ku6Xmm7/mjaLkuLogIjrvvJrjgIEi5YW25Lit5LmL5LiACiAgICAgICAgY29uc3QgZXhwbGFuYXRpb25NYXRjaCA9IGxpbmUubWF0Y2goL17op6PmnpBbLjrvvJrjgIFdXHMqKC4rKS8pCiAgICAgICAgaWYgKGV4cGxhbmF0aW9uTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmV4cGxhbmF0aW9uID0gZXhwbGFuYXRpb25NYXRjaFsxXS50cmltKCkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop4TojIPvvJrpmr7luqbmoLzlvI/vvIjpmr7luqbvvJrvvInvvIzlj6rmlK/mjIHnroDljZXjgIHkuK3nrYnjgIHlm7Dpmr7kuInkuKrnuqfliKsKICAgICAgICBjb25zdCBkaWZmaWN1bHR5TWF0Y2ggPSBsaW5lLm1hdGNoKC9e6Zq+5bqmWy4677ya44CBXVxzKijnroDljZV85Lit562JfOWbsOmavnzkuK0pLykKICAgICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgICBsZXQgZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXRjaFsxXQogICAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgICAgaWYgKGRpZmZpY3VsdHkgPT09ICfkuK0nKSB7CiAgICAgICAgICAgIGRpZmZpY3VsdHkgPSAn5Lit562JJwogICAgICAgICAgfQogICAgICAgICAgLy8g5Y+q5o6l5Y+X5qCH5YeG55qE5LiJ5Liq6Zq+5bqm57qn5YirCiAgICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uLmRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyDkuI3mlK/mjIHnmoTpmr7luqbnuqfliKs6JywgZGlmZmljdWx0eSwgJ++8jOW3suW/veeVpScpCiAgICAgICAgICB9CiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6KeE6IyD77ya562U5qGI5pSv5oyB55u05o6l5Zyo6aKY5bmy5Lit5qCH5rOo77yM5LyY5YWI5Lul5pi+5byP5qCH5rOo55qE562U5qGI5Li65YeGCiAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOaYvuW8j+etlOahiO+8jOWwneivleS7jumimOebruWGheWuueS4reaPkOWPlgogICAgICBpZiAoIXF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIpIHsKICAgICAgICBxdWVzdGlvbi5jb3JyZWN0QW5zd2VyID0gdGhpcy5leHRyYWN0QW5zd2VyRnJvbVF1ZXN0aW9uQ29udGVudChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDku47popjlubLkuK3mj5Dlj5bnrZTmoYggLSDmjInnhafovpPlhaXop4TojIMKICAgIGV4dHJhY3RBbnN3ZXJGcm9tUXVlc3Rpb25Db250ZW50KHF1ZXN0aW9uQ29udGVudCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIGlmICghcXVlc3Rpb25Db250ZW50IHx8IHR5cGVvZiBxdWVzdGlvbkNvbnRlbnQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g6KeE6IyD77ya6aKY5bmy5Lit5qC85byP77yI44CQQeOAke+8ie+8jOaLrOWPt+WPr+S7peabv+aNouS4uuS4reiLseaWh+eahOWwj+aLrOWPt+aIluiAheS4reaLrOWPtwogICAgICAgIGNvbnN0IHBhdHRlcm5zID0gWwogICAgICAgICAgL+OAkChbXuOAkV0rKeOAkS9nLCAgICAvLyDkuK3mlofmlrnmi6zlj7cKICAgICAgICAgIC9cWyhbXlxdXSspXF0vZywgICAvLyDoi7Hmlofmlrnmi6zlj7cKICAgICAgICAgIC/vvIgoW17vvIldKynvvIkvZywgICAgLy8g5Lit5paH5ZyG5ous5Y+3CiAgICAgICAgICAvXCgoW14pXSspXCkvZyAgICAgLy8g6Iux5paH5ZyG5ous5Y+3CiAgICAgICAgXQoKICAgICAgICBmb3IgKGNvbnN0IHBhdHRlcm4gb2YgcGF0dGVybnMpIHsKICAgICAgICAgIGNvbnN0IG1hdGNoZXMgPSBxdWVzdGlvbkNvbnRlbnQubWF0Y2gocGF0dGVybikKICAgICAgICAgIGlmIChtYXRjaGVzICYmIG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgICAvLyDmj5Dlj5bmnIDlkI7kuIDkuKrljLnphY3pobnkvZzkuLrnrZTmoYjvvIjpgJrluLjnrZTmoYjlnKjpopjnm67mnKvlsL7vvIkKICAgICAgICAgICAgY29uc3QgbGFzdE1hdGNoID0gbWF0Y2hlc1ttYXRjaGVzLmxlbmd0aCAtIDFdCiAgICAgICAgICAgIGNvbnN0IGFuc3dlciA9IGxhc3RNYXRjaC5yZXBsYWNlKC9b44CQ44CRXFtcXe+8iO+8iSgpXS9nLCAnJykudHJpbSgpCgogICAgICAgICAgICBpZiAoYW5zd2VyKSB7CiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VBbnN3ZXJWYWx1ZShhbnN3ZXIsIHF1ZXN0aW9uVHlwZSkKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5LuO6aKY5bmy5o+Q5Y+W562U5qGI5pe25Ye66ZSZOicsIGVycm9yKQogICAgICB9CgogICAgICByZXR1cm4gJycKICAgIH0sCgogICAgLy8g6Kej5p6Q562U5qGI5YC8CiAgICBwYXJzZUFuc3dlclZhbHVlKGFuc3dlclRleHQsIHF1ZXN0aW9uVHlwZSkgewogICAgICBpZiAoIWFuc3dlclRleHQgfHwgdHlwZW9mIGFuc3dlclRleHQgIT09ICdzdHJpbmcnKSB7CiAgICAgICAgcmV0dXJuICcnCiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdHJpbW1lZEFuc3dlciA9IGFuc3dlclRleHQudHJpbSgpCgogICAgICAgIGlmICghdHJpbW1lZEFuc3dlcikgewogICAgICAgICAgcmV0dXJuICcnCiAgICAgICAgfQoKICAgICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnKSB7CiAgICAgICAgICAvLyDliKTmlq3popjnrZTmoYjlpITnkIYgLSDkv53mjIHljp/lp4vmoLzlvI/vvIzkuI3ovazmjaLkuLp0cnVlL2ZhbHNlCiAgICAgICAgICByZXR1cm4gdHJpbW1lZEFuc3dlcgogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDpgInmi6npopjnrZTmoYjlpITnkIYKICAgICAgICAgIHJldHVybiB0cmltbWVkQW5zd2VyLnRvVXBwZXJDYXNlKCkKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOino+aekOetlOahiOWAvOaXtuWHuumUmTonLCBlcnJvcikKICAgICAgICByZXR1cm4gYW5zd2VyVGV4dCB8fCAnJwogICAgICB9CiAgICB9LAoKICAgIC8vIOaMiemimOWei+WIhuWJsuWGheWuuQogICAgc3BsaXRCeVF1ZXN0aW9uVHlwZShjb250ZW50KSB7CiAgICAgIGNvbnN0IHNlY3Rpb25zID0gW10KICAgICAgY29uc3QgdHlwZVJlZ2V4ID0gL1xbKOWNlemAiemimHzlpJrpgInpoph85Yik5pat6aKYKVxdL2cKCiAgICAgIGxldCBsYXN0SW5kZXggPSAwCiAgICAgIGxldCBtYXRjaAogICAgICBsZXQgY3VycmVudFR5cGUgPSBudWxsCgogICAgICB3aGlsZSAoKG1hdGNoID0gdHlwZVJlZ2V4LmV4ZWMoY29udGVudCkpICE9PSBudWxsKSB7CiAgICAgICAgaWYgKGN1cnJlbnRUeXBlKSB7CiAgICAgICAgICAvLyDkv53lrZjkuIrkuIDkuKrljLrln58KICAgICAgICAgIHNlY3Rpb25zLnB1c2goewogICAgICAgICAgICB0eXBlOiBjdXJyZW50VHlwZSwKICAgICAgICAgICAgY29udGVudDogY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4LCBtYXRjaC5pbmRleCkudHJpbSgpCiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgICBjdXJyZW50VHlwZSA9IG1hdGNoWzFdCiAgICAgICAgbGFzdEluZGV4ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGgKICAgICAgfQoKICAgICAgLy8g5L+d5a2Y5pyA5ZCO5LiA5Liq5Yy65Z+fCiAgICAgIGlmIChjdXJyZW50VHlwZSkgewogICAgICAgIHNlY3Rpb25zLnB1c2goewogICAgICAgICAgdHlwZTogY3VycmVudFR5cGUsCiAgICAgICAgICBjb250ZW50OiBjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgpLnRyaW0oKQogICAgICAgIH0pCiAgICAgIH0KCiAgICAgIHJldHVybiBzZWN0aW9ucwogICAgfSwKCiAgICAvLyDop6PmnpDljLrln5/lhoXnmoTpopjnm64KICAgIHBhcnNlU2VjdGlvblF1ZXN0aW9ucyhzZWN0aW9uKSB7CiAgICAgIGNvbnN0IHF1ZXN0aW9ucyA9IFtdCiAgICAgIGNvbnN0IHF1ZXN0aW9uVHlwZSA9IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZShzZWN0aW9uLnR5cGUpCgogICAgICAvLyDmjInpopjlj7fliIblibLpopjnm64KICAgICAgY29uc3QgcXVlc3Rpb25CbG9ja3MgPSB0aGlzLnNwbGl0QnlRdWVzdGlvbk51bWJlcihzZWN0aW9uLmNvbnRlbnQpCgogICAgICBxdWVzdGlvbkJsb2Nrcy5mb3JFYWNoKChibG9jaywgaW5kZXgpID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgY29uc3QgcXVlc3Rpb24gPSB0aGlzLnBhcnNlUXVlc3Rpb25CbG9jayhibG9jaywgcXVlc3Rpb25UeXBlLCBpbmRleCArIDEpCiAgICAgICAgICBpZiAocXVlc3Rpb24pIHsKICAgICAgICAgICAgcXVlc3Rpb25zLnB1c2gocXVlc3Rpb24pCiAgICAgICAgICB9CiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIHRocm93IG5ldyBFcnJvcihg56ysJHtpbmRleCArIDF96aKY6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICB9CiAgICAgIH0pCgogICAgICByZXR1cm4gcXVlc3Rpb25zCiAgICB9LAoKICAgIC8vIOaMiemimOWPt+WIhuWJsumimOebrgogICAgc3BsaXRCeVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpIHsKICAgICAgY29uc3QgYmxvY2tzID0gW10KICAgICAgY29uc3QgbnVtYmVyUmVnZXggPSAvXlxzKihcZCspWy4677ya77yOXVxzKi9nbQoKICAgICAgbGV0IGxhc3RJbmRleCA9IDAKICAgICAgbGV0IG1hdGNoCgogICAgICB3aGlsZSAoKG1hdGNoID0gbnVtYmVyUmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHsKICAgICAgICBpZiAobGFzdEluZGV4ID4gMCkgewogICAgICAgICAgLy8g5L+d5a2Y5LiK5LiA6aKYCiAgICAgICAgICBibG9ja3MucHVzaChjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgsIG1hdGNoLmluZGV4KS50cmltKCkpCiAgICAgICAgfQogICAgICAgIGxhc3RJbmRleCA9IG1hdGNoLmluZGV4CiAgICAgIH0KCiAgICAgIC8vIOS/neWtmOacgOWQjuS4gOmimAogICAgICBpZiAobGFzdEluZGV4IDwgY29udGVudC5sZW5ndGgpIHsKICAgICAgICBibG9ja3MucHVzaChjb250ZW50LnN1YnN0cmluZyhsYXN0SW5kZXgpLnRyaW0oKSkKICAgICAgfQoKICAgICAgcmV0dXJuIGJsb2Nrcy5maWx0ZXIoYmxvY2sgPT4gYmxvY2subGVuZ3RoID4gMCkKICAgIH0sCgogICAgLy8g6Kej5p6Q5Y2V5Liq6aKY55uu5Z2XCiAgICBwYXJzZVF1ZXN0aW9uQmxvY2soYmxvY2ssIHF1ZXN0aW9uVHlwZSkgewogICAgICBjb25zdCBsaW5lcyA9IGJsb2NrLnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIobGluZSA9PiBsaW5lLmxlbmd0aCA+IDApCgogICAgICBpZiAobGluZXMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfpopjnm67lhoXlrrnkuLrnqbonKQogICAgICB9CgogICAgICAvLyDmj5Dlj5bpopjlubLvvIjnp7vpmaTpopjlj7fvvIkKICAgICAgY29uc3QgZmlyc3RMaW5lID0gbGluZXNbMF0KICAgICAgbGV0IHF1ZXN0aW9uQ29udGVudCA9ICcnCiAgICAgIGxldCBjdXJyZW50TGluZUluZGV4ID0gMAoKICAgICAgLy8g5aaC5p6c56ys5LiA6KGM5YyF5ZCr6aKY5Y+377yM56e76Zmk6aKY5Y+36YOo5YiGCiAgICAgIGNvbnN0IG51bWJlck1hdGNoID0gZmlyc3RMaW5lLm1hdGNoKC9eXHMqKFxkKylbLjrvvJrvvI7jgIFdXHMqKC4qKS8pCiAgICAgIGlmIChudW1iZXJNYXRjaCkgewogICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IG51bWJlck1hdGNoWzJdLnRyaW0oKSAvLyDnp7vpmaTpopjlj7fvvIzlj6rkv53nlZnpopjlubIKICAgICAgICBjdXJyZW50TGluZUluZGV4ID0gMQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWmguaenOesrOS4gOihjOS4jeWMheWQq+mimOWPt++8jOebtOaOpeS9nOS4uumimOW5su+8jOS9huS7jemcgOa4heeQhuWPr+iDveeahOmimOWPtwogICAgICAgIHF1ZXN0aW9uQ29udGVudCA9IHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIoZmlyc3RMaW5lKS50cmltKCkKICAgICAgICBjdXJyZW50TGluZUluZGV4ID0gMQogICAgICB9CgogICAgICAvLyDnu6fnu63or7vlj5bpopjlubLlhoXlrrnvvIjnm7TliLDpgYfliLDpgInpobnvvIkKICAgICAgd2hpbGUgKGN1cnJlbnRMaW5lSW5kZXggPCBsaW5lcy5sZW5ndGgpIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbY3VycmVudExpbmVJbmRleF0KICAgICAgICBpZiAodGhpcy5pc09wdGlvbkxpbmUobGluZSkpIHsKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICAgIHF1ZXN0aW9uQ29udGVudCArPSAnXG4nICsgbGluZQogICAgICAgIGN1cnJlbnRMaW5lSW5kZXgrKwogICAgICB9CgogICAgICBjb25zdCBxdWVzdGlvbiA9IHsKICAgICAgICBxdWVzdGlvblR5cGU6IHF1ZXN0aW9uVHlwZSwKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6IHF1ZXN0aW9uQ29udGVudC50cmltKCksCiAgICAgICAgZGlmZmljdWx0eTogJycsIC8vIOS4jeiuvue9rum7mOiupOWAvAogICAgICAgIGV4cGxhbmF0aW9uOiAnJywKICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICBjb3JyZWN0QW5zd2VyOiAnJwogICAgICB9CgogICAgICAvLyDop6PmnpDpgInpobnvvIjlr7nkuo7pgInmi6npopjvvIkKICAgICAgaWYgKHF1ZXN0aW9uVHlwZSAhPT0gJ2p1ZGdtZW50JykgewogICAgICAgIGNvbnN0IG9wdGlvblJlc3VsdCA9IHRoaXMucGFyc2VPcHRpb25zKGxpbmVzLCBjdXJyZW50TGluZUluZGV4KQogICAgICAgIHF1ZXN0aW9uLm9wdGlvbnMgPSBvcHRpb25SZXN1bHQub3B0aW9ucwogICAgICAgIGN1cnJlbnRMaW5lSW5kZXggPSBvcHRpb25SZXN1bHQubmV4dEluZGV4CiAgICAgIH0KCiAgICAgIC8vIOino+aekOetlOahiOOAgeino+aekOOAgemavuW6pgogICAgICB0aGlzLnBhcnNlUXVlc3Rpb25NZXRhKGxpbmVzLCBjdXJyZW50TGluZUluZGV4LCBxdWVzdGlvbikKCiAgICAgIC8vIOacgOe7iOa4heeQhu+8muehruS/nemimOebruWGheWuueWujOWFqOayoeaciemimOWPtwogICAgICBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCkKCiAgICAgIHJldHVybiBxdWVzdGlvbgogICAgfSwKCiAgICAvLyDliKTmlq3mmK/lkKbkuLrpgInpobnooYwKICAgIGlzT3B0aW9uTGluZShsaW5lKSB7CiAgICAgIHJldHVybiAvXltBLVphLXpdWy4677ya77yOXVxzKi8udGVzdChsaW5lKQogICAgfSwKCiAgICAvLyDop6PmnpDpgInpobkKICAgIHBhcnNlT3B0aW9ucyhsaW5lcywgc3RhcnRJbmRleCkgewogICAgICBjb25zdCBvcHRpb25zID0gW10KICAgICAgbGV0IGN1cnJlbnRJbmRleCA9IHN0YXJ0SW5kZXgKCiAgICAgIHdoaWxlIChjdXJyZW50SW5kZXggPCBsaW5lcy5sZW5ndGgpIHsKICAgICAgICBjb25zdCBsaW5lID0gbGluZXNbY3VycmVudEluZGV4XQogICAgICAgIGNvbnN0IG9wdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXihbQS1aYS16XSlbLjrvvJrvvI5dXHMqKC4qKS8pCgogICAgICAgIGlmICghb3B0aW9uTWF0Y2gpIHsKICAgICAgICAgIGJyZWFrCiAgICAgICAgfQoKICAgICAgICBvcHRpb25zLnB1c2goewogICAgICAgICAgb3B0aW9uS2V5OiBvcHRpb25NYXRjaFsxXS50b1VwcGVyQ2FzZSgpLAogICAgICAgICAgb3B0aW9uQ29udGVudDogb3B0aW9uTWF0Y2hbMl0udHJpbSgpCiAgICAgICAgfSkKCiAgICAgICAgY3VycmVudEluZGV4KysKICAgICAgfQoKICAgICAgcmV0dXJuIHsgb3B0aW9ucywgbmV4dEluZGV4OiBjdXJyZW50SW5kZXggfQogICAgfSwKCiAgICAvLyDop6PmnpDpopjnm67lhYPkv6Hmga/vvIjnrZTmoYjjgIHop6PmnpDjgIHpmr7luqbvvIkKICAgIHBhcnNlUXVlc3Rpb25NZXRhKGxpbmVzLCBzdGFydEluZGV4LCBxdWVzdGlvbikgewogICAgICBmb3IgKGxldCBpID0gc3RhcnRJbmRleDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2ldCgogICAgICAgIC8vIOino+aekOetlOahiAogICAgICAgIGNvbnN0IGFuc3dlck1hdGNoID0gbGluZS5tYXRjaCgvXuetlOahiFvvvJo6XVxzKiguKykvKQogICAgICAgIGlmIChhbnN3ZXJNYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uY29ycmVjdEFuc3dlciA9IHRoaXMucGFyc2VBbnN3ZXIoYW5zd2VyTWF0Y2hbMV0sIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgICAgIGNvbnRpbnVlCiAgICAgICAgfQoKICAgICAgICAvLyDop6PmnpDop6PmnpAKICAgICAgICBjb25zdCBleHBsYW5hdGlvbk1hdGNoID0gbGluZS5tYXRjaCgvXuino+aekFvvvJo6XVxzKiguKykvKQogICAgICAgIGlmIChleHBsYW5hdGlvbk1hdGNoKSB7CiAgICAgICAgICBxdWVzdGlvbi5leHBsYW5hdGlvbiA9IGV4cGxhbmF0aW9uTWF0Y2hbMV0udHJpbSgpCiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KCiAgICAgICAgLy8g6Kej5p6Q6Zq+5bqmIC0g5Y+q5pSv5oyB566A5Y2V44CB5Lit562J44CB5Zuw6Zq+5LiJ5Liq57qn5YirCiAgICAgICAgY29uc3QgZGlmZmljdWx0eU1hdGNoID0gbGluZS5tYXRjaCgvXumavuW6plvvvJo6XVxzKijnroDljZV85Lit562JfOWbsOmavnzkuK0pLykKICAgICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgICBsZXQgZGlmZmljdWx0eSA9IGRpZmZpY3VsdHlNYXRjaFsxXQogICAgICAgICAgLy8g5qCH5YeG5YyW6Zq+5bqm5YC877ya5bCGIuS4rSLnu5/kuIDkuLoi5Lit562JIgogICAgICAgICAgaWYgKGRpZmZpY3VsdHkgPT09ICfkuK0nKSB7CiAgICAgICAgICAgIGRpZmZpY3VsdHkgPSAn5Lit562JJwogICAgICAgICAgfQogICAgICAgICAgLy8g5Y+q5o6l5Y+X5qCH5YeG55qE5LiJ5Liq6Zq+5bqm57qn5YirCiAgICAgICAgICBpZiAoWyfnroDljZUnLCAn5Lit562JJywgJ+WbsOmaviddLmluY2x1ZGVzKGRpZmZpY3VsdHkpKSB7CiAgICAgICAgICAgIHF1ZXN0aW9uLmRpZmZpY3VsdHkgPSBkaWZmaWN1bHR5CiAgICAgICAgICB9CiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5rKh5pyJ5pi+5byP562U5qGI77yM5bCd6K+V5LuO6aKY5bmy5Lit5o+Q5Y+WCiAgICAgIGlmICghcXVlc3Rpb24uY29ycmVjdEFuc3dlcikgewogICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLmV4dHJhY3RBbnN3ZXJGcm9tQ29udGVudChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgfQogICAgfSwKCgoKICAgIC8vIOS7jumimOW5suS4reaPkOWPluetlOahiAogICAgZXh0cmFjdEFuc3dlckZyb21Db250ZW50KGNvbnRlbnQsIHF1ZXN0aW9uVHlwZSkgewogICAgICAvLyDmlK/mjIHnmoTmi6zlj7fnsbvlnosKICAgICAgY29uc3QgYnJhY2tldFBhdHRlcm5zID0gWwogICAgICAgIC/jgJAoW17jgJFdKynjgJEvZywKICAgICAgICAvXFsoW15cXV0rKVxdL2csCiAgICAgICAgL++8iChbXu+8iV0rKe+8iS9nLAogICAgICAgIC9cKChbXildKylcKS9nCiAgICAgIF0KCiAgICAgIGZvciAoY29uc3QgcGF0dGVybiBvZiBicmFja2V0UGF0dGVybnMpIHsKICAgICAgICBjb25zdCBtYXRjaGVzID0gWy4uLmNvbnRlbnQubWF0Y2hBbGwocGF0dGVybildCiAgICAgICAgaWYgKG1hdGNoZXMubGVuZ3RoID4gMCkgewogICAgICAgICAgY29uc3QgYW5zd2VyID0gbWF0Y2hlc1ttYXRjaGVzLmxlbmd0aCAtIDFdWzFdIC8vIOWPluacgOWQjuS4gOS4quWMuemFjQogICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VBbnN3ZXIoYW5zd2VyLCBxdWVzdGlvblR5cGUpCiAgICAgICAgfQogICAgICB9CgogICAgICByZXR1cm4gJycKICAgIH0sCgogICAgLy8g6L2s5o2i6aKY5Z6LCiAgICBjb252ZXJ0UXVlc3Rpb25UeXBlKHR5cGVUZXh0KSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgJ+WNlemAiemimCc6ICdzaW5nbGUnLAogICAgICAgICflpJrpgInpopgnOiAnbXVsdGlwbGUnLAogICAgICAgICfliKTmlq3popgnOiAnanVkZ21lbnQnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZVRleHRdIHx8ICdzaW5nbGUnCiAgICB9LAoKICAgIC8vIOiOt+WPlumimOWei+WQjeensAogICAgZ2V0UXVlc3Rpb25UeXBlTmFtZSh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgJ3NpbmdsZSc6ICfljZXpgInpopgnLAogICAgICAgICdtdWx0aXBsZSc6ICflpJrpgInpopgnLAogICAgICAgICdqdWRnbWVudCc6ICfliKTmlq3popgnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgJ+acquefpScKICAgIH0sCgogICAgLy8g6I635Y+W6aKY5Z6L6aKc6ImyCiAgICBnZXRRdWVzdGlvblR5cGVDb2xvcih0eXBlKSB7CiAgICAgIGNvbnN0IGNvbG9yTWFwID0gewogICAgICAgICdzaW5nbGUnOiAncHJpbWFyeScsCiAgICAgICAgJ211bHRpcGxlJzogJ3N1Y2Nlc3MnLAogICAgICAgICdqdWRnbWVudCc6ICd3YXJuaW5nJwogICAgICB9CiAgICAgIHJldHVybiBjb2xvck1hcFt0eXBlXSB8fCAnaW5mbycKICAgIH0sCgoKCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PSDljp/mnInmlrnms5UgPT09PT09PT09PT09PT09PT09PT0KCiAgICAvLyDojrflj5bmoLzlvI/ljJbnmoTpopjnm67lhoXlrrnvvIjmlK/mjIHlr4zmlofmnKzmoLzlvI/vvIkKICAgIGdldEZvcm1hdHRlZFF1ZXN0aW9uQ29udGVudChxdWVzdGlvbikgewogICAgICBpZiAoIXF1ZXN0aW9uIHx8ICFxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQpIHsKICAgICAgICByZXR1cm4gJycKICAgICAgfQoKICAgICAgbGV0IGNvbnRlbnQgPSBxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQKCiAgICAgIC8vIOWmguaenOaciUhUTUzlhoXlrrnkuJTljIXlkKvlr4zmlofmnKzmoIfnrb7vvIzkvJjlhYjkvb/nlKhIVE1M5YaF5a65CiAgICAgIGlmICh0aGlzLmRvY3VtZW50SHRtbENvbnRlbnQgJiYgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50LmluY2x1ZGVzKCc8JykpIHsKICAgICAgICAvLyDku45IVE1M5YaF5a655Lit5o+Q5Y+W5a+55bqU55qE6aKY55uu5YaF5a65CiAgICAgICAgY29uc3QgaHRtbENvbnRlbnQgPSB0aGlzLmV4dHJhY3RRdWVzdGlvbkZyb21IdG1sKHF1ZXN0aW9uLnF1ZXN0aW9uQ29udGVudCwgdGhpcy5kb2N1bWVudEh0bWxDb250ZW50KQogICAgICAgIGlmIChodG1sQ29udGVudCkgewogICAgICAgICAgY29udGVudCA9IGh0bWxDb250ZW50CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDmuIXnkIbpopjlj7fvvJrnoa7kv53popjnm67lhoXlrrnkuI3ku6XmlbDlrZcr56ym5Y+35byA5aS0CiAgICAgIGNvbnRlbnQgPSB0aGlzLnJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpCgogICAgICByZXR1cm4gdGhpcy5wcm9jZXNzSW1hZ2VQYXRocyhjb250ZW50KQogICAgfSwKCiAgICAvLyDmuIXnkIbpopjnm67lhoXlrrnkuK3nmoTpopjlj7cKICAgIHJlbW92ZVF1ZXN0aW9uTnVtYmVyKGNvbnRlbnQpIHsKICAgICAgaWYgKCFjb250ZW50IHx8IHR5cGVvZiBjb250ZW50ICE9PSAnc3RyaW5nJykgewogICAgICAgIHJldHVybiBjb250ZW50CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhkhUTUzlhoXlrrkKICAgICAgaWYgKGNvbnRlbnQuaW5jbHVkZXMoJzwnKSkgewogICAgICAgIC8vIOWvueS6jkhUTUzlhoXlrrnvvIzpnIDopoHmuIXnkIbmoIfnrb7lhoXnmoTpopjlj7cKICAgICAgICByZXR1cm4gY29udGVudC5yZXBsYWNlKC88cFtePl0qPihccypcZCtbLjrvvJrvvI7jgIFdXHMqKSguKj8pPFwvcD4vZ2ksICc8cD4kMjwvcD4nKQogICAgICAgICAgICAgICAgICAgICAucmVwbGFjZSgvXihccypcZCtbLjrvvJrvvI7jgIFdXHMqKS8sICcnKSAvLyDmuIXnkIblvIDlpLTnmoTpopjlj7cKICAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoLz5ccypcZCtbLjrvvJrvvI7jgIFdXHMqL2csICc+JykgLy8g5riF55CG5qCH562+5ZCO55qE6aKY5Y+3CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5a+55LqO57qv5paH5pys5YaF5a6577yM55u05o6l5riF55CG5byA5aS055qE6aKY5Y+3CiAgICAgICAgcmV0dXJuIGNvbnRlbnQucmVwbGFjZSgvXlxzKlxkK1suOu+8mu+8juOAgV1ccyovLCAnJykudHJpbSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuOSFRNTOWGheWuueS4reaPkOWPluWvueW6lOeahOmimOebruWGheWuuQogICAgZXh0cmFjdFF1ZXN0aW9uRnJvbUh0bWwocGxhaW5Db250ZW50LCBodG1sQ29udGVudCkgewogICAgICBpZiAoIXBsYWluQ29udGVudCB8fCAhaHRtbENvbnRlbnQpIHsKICAgICAgICByZXR1cm4gcGxhaW5Db250ZW50CiAgICAgIH0KCiAgICAgIHRyeSB7CiAgICAgICAgLy8g566A5Y2V55qE5Yy56YWN562W55Wl77ya5p+l5om+5YyF5ZCr6aKY55uu5YaF5a6555qESFRNTOauteiQvQogICAgICAgIGNvbnN0IHBsYWluVGV4dCA9IHBsYWluQ29udGVudC5yZXBsYWNlKC9eXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKCiAgICAgICAgLy8g5ZyoSFRNTOWGheWuueS4reafpeaJvuWMheWQq+i/meS4quaWh+acrOeahOauteiQvQogICAgICAgIGNvbnN0IHBhcmFncmFwaHMgPSBodG1sQ29udGVudC5tYXRjaCgvPHBbXj5dKj4uKj88XC9wPi9naSkgfHwgW10KCiAgICAgICAgZm9yIChjb25zdCBwYXJhZ3JhcGggb2YgcGFyYWdyYXBocykgewogICAgICAgICAgY29uc3QgcGFyYWdyYXBoVGV4dCA9IHBhcmFncmFwaC5yZXBsYWNlKC88W14+XSo+L2csICcnKS50cmltKCkKICAgICAgICAgIC8vIOa4heeQhuauteiQveaWh+acrOS4reeahOmimOWPt+WGjei/m+ihjOWMuemFjQogICAgICAgICAgY29uc3QgY2xlYW5QYXJhZ3JhcGhUZXh0ID0gcGFyYWdyYXBoVGV4dC5yZXBsYWNlKC9eXHMqXGQrWy4677ya77yO44CBXVxzKi8sICcnKS50cmltKCkKICAgICAgICAgIGlmIChjbGVhblBhcmFncmFwaFRleHQuaW5jbHVkZXMocGxhaW5UZXh0LnN1YnN0cmluZygwLCAyMCkpKSB7CiAgICAgICAgICAgIC8vIOaJvuWIsOWMuemFjeeahOauteiQve+8jOi/lOWbnkhUTUzmoLzlvI/vvIjkvYbopoHmuIXnkIbpopjlj7fvvIkKICAgICAgICAgICAgcmV0dXJuIHRoaXMucmVtb3ZlUXVlc3Rpb25OdW1iZXIocGFyYWdyYXBoKQogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5om+5Yiw5Yy56YWN55qE5q616JC977yM6L+U5Zue5Y6f5aeL5YaF5a65CiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aPkOWPlkhUTUzpopjnm67lhoXlrrnlpLHotKU6JywgZXJyb3IpCiAgICAgICAgcmV0dXJuIHBsYWluQ29udGVudAogICAgICB9CiAgICB9LAoKCiAgICAvLyDmkJzntKIKICAgIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQogICAgICB0aGlzLmdldFF1ZXN0aW9uTGlzdCgpCiAgICB9LAogICAgLy8g6YeN572u5pCc57SiCiAgICByZXNldFNlYXJjaCgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVzdGlvblR5cGUgPSBudWxsCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGlmZmljdWx0eSA9IG51bGwKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVzdGlvbkNvbnRlbnQgPSBudWxsCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgICAgdGhpcy5nZXRRdWVzdGlvbkxpc3QoKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAugBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;;AAGA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AACA;;;;AAIA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"importDrawerVisible = true\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n              >\n                导入题目\n              </el-button>\n\n              <el-checkbox v-model=\"importOptions.reverse\">\n                按题目顺序倒序导入\n              </el-checkbox>\n\n              <el-checkbox v-model=\"importOptions.allowDuplicate\">\n                允许题目重复\n              </el-checkbox>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button>\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1.建议使用新版office或WPS软件编辑题目文件，仅支持上传.docx/.xlsx格式的文件<br>\n          2.Word导入支持全部题型，Excel导入不支持完形填空题、组合题<br>\n          3.Word导入支持导入图片/公式，Excel导入暂不支持<br>\n          4.题目数量过多、题目文件过大（如图片较多）等情况建议分批导入<br>\n          5.需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 文档导入对话框 -->\n    <batch-import\n      :visible.sync=\"batchImportVisible\"\n      :bank-id=\"bankId\"\n      :default-mode=\"currentImportMode\"\n      @success=\"handleBatchImportSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport BatchImport from './components/BatchImport'\nimport { listQuestion, delQuestion, getQuestionStatistics } from '@/api/biz/question'\nimport { batchImportQuestions } from '@/api/biz/questionBank'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm,\n    BatchImport\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      // 选择状态\n      selectedQuestions: [],\n      // 选择相关\n      selectedQuestions: [],\n      isAllSelected: false,\n      expandedQuestions: [],\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      batchImportVisible: false,\n      currentImportMode: 'excel', // 当前导入模式\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '', // 存储富文本HTML内容用于预览\n      parsedQuestions: [],\n      parseErrors: [],\n      // 全部展开/收起状态\n      allExpanded: true,\n      // 标志位：是否正在从后端设置内容（避免触发前端重新解析）\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      // 规范对话框标签页\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时初始化编辑器\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数\n    this.debounceParseDocument = this.debounce(this.parseDocument, 1000)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(error => {\n\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(error => {\n\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n    // 批量导入\n    handleBatchImport() {\n      this.batchImportVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要导出的题目')\n        return\n      }\n      this.$message.info(`正在导出 ${this.selectedQuestions.length} 道题目...`)\n      // TODO: 实现导出功能\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里应该调用批量删除API\n        // 暂时使用单个删除的方式\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.allSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      })\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      this.$confirm(`确认删除选中的 ${this.selectedQuestions.length} 道题目吗？`, '批量删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 批量删除API调用\n        const deletePromises = this.selectedQuestions.map(questionId =>\n          delQuestion(questionId)\n        )\n\n        Promise.all(deletePromises).then(() => {\n          this.$message.success(`成功删除 ${this.selectedQuestions.length} 道题目`)\n          this.selectedQuestions = []\n          this.isAllSelected = false\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('批量删除失败')\n        })\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        this.expandedQuestions.splice(index, 1)\n      } else {\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(error => {\n\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 批量导入成功回调\n    handleBatchImportSuccess() {\n      this.batchImportVisible = false\n      this.importDrawerVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      done()\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n    // 下载Excel模板\n    downloadExcelTemplate() {\n      this.download('biz/questionBank/downloadExcelTemplate', {}, `题目导入Excel模板.xlsx`)\n    },\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response, file) {\n\n\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n    // 全部收起\n    collapseAll() {\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', true)\n      })\n    },\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      this.$confirm(`确认导入 ${this.parsedQuestions.length} 道题目吗？`, '确认导入', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n        }\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate\n        }\n\n        const response = await batchImportQuestions(importData)\n\n        if (response.code === 200) {\n          this.$message.success(`成功导入 ${questionsToImport.length} 道题目`)\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n\n\n        this.getQuestionList()\n        this.getStatistics()\n      } catch (error) {\n\n        this.$message.error('导入失败')\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              // 错误处理和事件监听\n              on: {\n                pluginsLoaded: function() {\n\n                },\n                instanceReady: function() {\n\n\n                  const editor = evt.editor\n\n                  // 简单的对话框处理 - 参考您提供的代码风格\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n\n\n                      // 简单检查上传完成并切换标签页\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n\n                              // 切换到图像信息标签页\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n\n                        // 10秒后停止检查\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                },\n\n              }\n            })\n          } catch (error) {\n\n\n            // 尝试简化配置\n            try {\n              this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n                height: 'calc(100vh - 200px)',\n                toolbar: [\n                  ['Bold', 'Italic', 'Underline', 'Strike'],\n                  ['NumberedList', 'BulletedList'],\n                  ['Outdent', 'Indent'],\n                  ['Undo', 'Redo'],\n                  ['Link', 'Unlink'],\n                  ['Image', 'RemoveFormat', 'Maximize']\n                ],\n                removeButtons: '',\n                language: 'zh-cn',\n                removePlugins: 'elementspath',\n                resize_enabled: false,\n                extraPlugins: 'image',\n                allowedContent: true,\n                // 图像上传配置 - 参考您提供的标准配置\n                filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n                image_previewText: ' ',\n                // 设置基础路径，让相对路径能正确解析到后端服务器\n                baseHref: 'http://localhost:8802/',\n                // 隐藏不需要的标签页，只保留上传和图像信息\n                removeDialogTabs: 'image:Link;image:advanced',\n                // 添加实例就绪事件处理\n                on: {\n                  instanceReady: function(evt) {\n\n\n                    const editor = evt.editor\n\n                    // 监听对话框显示事件\n                    editor.on('dialogShow', function(evt) {\n                      const dialog = evt.data\n                      if (dialog.getName() === 'image') {\n\n\n                        // 简单检查上传完成并切换标签页\n                        setTimeout(() => {\n                          const checkInterval = setInterval(() => {\n                            try {\n                              const urlField = dialog.getContentElement('info', 'txtUrl')\n                              if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                                clearInterval(checkInterval)\n\n                                // 切换到图像信息标签页\n                                dialog.selectPage('info')\n                              }\n                            } catch (e) {\n                              // 忽略错误\n                            }\n                          }, 500)\n\n                          // 10秒后停止检查\n                          setTimeout(() => clearInterval(checkInterval), 10000)\n                        }, 1000)\n                      }\n                    })\n\n\n                  }\n                }\n              })\n\n            } catch (fallbackError) {\n\n              this.showFallbackEditor = true\n              return\n            }\n          }\n\n          // 监听内容变化\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            })\n          }\n\n          // 监听按键事件\n          this.richEditor.on('key', () => {\n            setTimeout(() => {\n              const rawContent = this.richEditor.getData()\n              const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n\n              // 保存HTML内容用于预览\n              this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n              // 保存纯文本内容用于解析\n              this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n\n\n            }, 100)\n          })\n\n          // 监听实例准备就绪\n          this.richEditor.on('instanceReady', () => {\n            this.editorInitialized = true\n            // 编辑器初始化完成\n          })\n        })\n\n      } catch (error) {\n\n        // 如果CKEditor初始化失败，回退到普通文本框\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = this.documentContent || ''\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化\n        textarea.addEventListener('input', (e) => {\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value // 纯文本模式下HTML内容与文本内容相同\n\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n    // 去除HTML标签\n    stripHtmlTags(html) {\n      const div = document.createElement('div')\n      div.innerHTML = html\n      return div.textContent || div.innerText || ''\n    },\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        // 如果编辑器还未初始化，保存内容等待初始化完成后设置\n        this.documentContent = content\n        this.documentHtmlContent = content // 同时设置HTML内容\n      }\n    },\n\n\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        // 为每个题目添加collapsed属性\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n\n\n      } catch (error) {\n\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        // 保留图片标签，只移除其他HTML标签\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        // 按行分割内容\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n\n        if (lines.length === 0) {\n\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n        console.error('❌ 文档解析失败:', error)\n      }\n\n      console.log('解析完成，共', questions.length, '道题目，', errors.length, '个错误')\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      return /^[A-Za-z][.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 处理img标签中的相对路径\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n\n          // 如果已经是完整路径，不处理\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          // 如果是相对路径，添加后端服务器地址\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          const result = `<img${before}src=\"${fullSrc}\"${after}>`\n          return result\n        })\n\n        return processedContent\n      } catch (error) {\n        console.error('❌ 处理图片路径时出错:', error)\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        console.error('❌ preserveRichTextFormatting 出错:', error)\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n\n        // 先保存所有图片标签\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        // 移除其他HTML标签，但保留换行\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')  // br标签转换为换行\n          .replace(/<\\/p>/gi, '\\n')       // p结束标签转换为换行\n          .replace(/<p[^>]*>/gi, '\\n')    // p开始标签转换为换行\n          .replace(/<[^>]*>/g, '')        // 移除其他HTML标签\n          .replace(/\\n\\s*\\n/g, '\\n')      // 合并多个换行\n\n        // 恢复图片标签\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        console.error('❌ stripHtmlTagsKeepImages 出错:', error)\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        console.warn('⚠️ 解析选项参数无效')\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n          const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n          if (optionMatch) {\n            const optionKey = optionMatch[1].toUpperCase()\n            const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n            if (optionKey && optionContent) {\n              options.push({\n                optionKey: optionKey,\n                label: optionKey,\n                optionContent: optionContent,\n                content: optionContent\n              })\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            const multipleOptionsMatch = line.match(/([A-Za-z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Za-z][.:：．、]\\s*[^\\s]+)*)/g)\n            if (multipleOptionsMatch) {\n              // 处理同一行多个选项的情况\n              const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n              for (const singleOption of singleOptions) {\n                if (!singleOption) continue\n\n                const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                if (match) {\n                  const optionKey = match[1].toUpperCase()\n                  const optionContent = match[2] ? match[2].trim() : ''\n\n                  if (optionKey && optionContent) {\n                    options.push({\n                      optionKey: optionKey,\n                      label: optionKey,\n                      optionContent: optionContent,\n                      content: optionContent\n                    })\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 解析选项时出错:', error)\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          } else {\n            console.warn('⚠️ 不支持的难度级别:', difficulty, '，已忽略')\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n        console.error('❌ 从题干提取答案时出错:', error)\n      }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n        console.error('❌ 解析答案值时出错:', error)\n        return answerText || ''\n      }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题干（移除题号）\n      const firstLine = lines[0]\n      let questionContent = ''\n      let currentLineIndex = 0\n\n      // 如果第一行包含题号，移除题号部分\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．、]\\s*(.*)/)\n      if (numberMatch) {\n        questionContent = numberMatch[2].trim() // 移除题号，只保留题干\n        currentLineIndex = 1\n      } else {\n        // 如果第一行不包含题号，直接作为题干，但仍需清理可能的题号\n        questionContent = this.removeQuestionNumber(firstLine).trim()\n        currentLineIndex = 1\n      }\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      // 最终清理：确保题目内容完全没有题号\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度 - 只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n\n\n\n    // ==================== 原有方法 ====================\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        // 如果没有找到匹配的段落，返回原始内容\n        return plainContent\n      } catch (error) {\n        console.error('提取HTML题目内容失败:', error)\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n.question-answer {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.question-explanation {\n  margin: 10px 0;\n  font-size: 14px;\n  color: #909399;\n}\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n\n/* 工具栏样式优化 */\n.toolbar {\n  padding: 10px 15px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.toolbar .orange {\n  font-size: 14px;\n  color: #e6a23c;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 8px;\n}\n</style>\n"]}]}