{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\RichTextImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyB1cGxvYWRBbmRQYXJzZSwgc2F2ZUVkaXRlZENvbnRlbnQsIHByZXZpZXdDb250ZW50LCBpbXBvcnRGcm9tRWRpdG9yIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uQmFuaycKaW1wb3J0IEVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvRWRpdG9yJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJSaWNoVGV4dEltcG9ydCIsCiAgY29tcG9uZW50czogewogICAgRWRpdG9yCiAgfSwKICBwcm9wczogewogICAgdmlzaWJsZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIGJhbmtJZDogewogICAgICB0eXBlOiBbU3RyaW5nLCBOdW1iZXJdLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGJhbmtOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ+mimOW6kycKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudFN0ZXA6IDAsCgogICAgICAvLyDmoIfnrb7pobXnm7jlhbMKICAgICAgYWN0aXZlVGFiOiAndXBsb2FkJywKCiAgICAgIC8vIOaWh+S7tuS4iuS8oOebuOWFswogICAgICB1cGxvYWRlZEZpbGU6IG51bGwsCiAgICAgIHVwbG9hZGluZzogZmFsc2UsCiAgICAgIHVwbG9hZFByb2dyZXNzOiAwLAogICAgICAKICAgICAgLy8g57yW6L6R5Zmo55u45YWzCiAgICAgIGVkaXRDb250ZW50OiAnJywKICAgICAgCiAgICAgIC8vIOmihOiniOebuOWFswogICAgICBwcmV2aWV3SHRtbDogJycsCiAgICAgIHByZXZpZXdRdWVzdGlvbkNvdW50OiAwLAogICAgICBwcmV2aWV3TG9hZGluZzogZmFsc2UsCiAgICAgIHBhcnNlRXJyb3JzOiBbXSwKICAgICAgCiAgICAgIC8vIOWvvOWFpeebuOWFswogICAgICBpbXBvcnRpbmc6IGZhbHNlLAogICAgICBpbXBvcnRPcHRpb25zOiB7CiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0sCiAgICAgIGltcG9ydFJlc3VsdDogewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9LAoKICAgICAgLy8g56S65L6L5YaF5a65CiAgICAgIGV4YW1wbGVDb250ZW50OiBgMeOAgVvljZXpgInpophd6K6h566X5py655qEQ1BV5Li76KaB5Yqf6IO95piv5LuA5LmI77yfCkHjgIHlrZjlgqjmlbDmja4KQuOAgeWkhOeQhuaVsOaNruWSjOaOp+WItueoi+W6j+aJp+ihjApD44CB6L6T5YWl5pWw5o2uCkTjgIHovpPlh7rmlbDmja4K562U5qGI77yaQgrop6PmnpDvvJpDUFXvvIjkuK3lpK7lpITnkIblmajvvInmmK/orqHnrpfmnLrnmoTmoLjlv4Ppg6jku7bvvIzkuLvopoHotJ/otKPlpITnkIbmlbDmja7lkozmjqfliLbnqIvluo/nmoTmiafooYzjgIIKCjLjgIFb5aSa6YCJ6aKYXeS7peS4i+WTquS6m+aYr+iuoeeul+acuueahOi+k+WFpeiuvuWkh++8nwpB44CB6ZSu55uYCkLjgIHpvKDmoIcKQ+OAgeaYvuekuuWZqApE44CB5omr5o+P5LuqCkXjgIHmiZPljbDmnLoK562U5qGI77yaQSxCLEQK6Kej5p6Q77ya6ZSu55uY44CB6byg5qCH5ZKM5omr5o+P5Luq6YO95piv6L6T5YWl6K6+5aSH77yM5pi+56S65Zmo5ZKM5omT5Y2w5py65piv6L6T5Ye66K6+5aSH44CCCgoz44CBW+WIpOaWremimF1DUFXnmoTkuLvpopHotorpq5jvvIzorqHnrpfmnLrnmoTmgKfog73lsLHkuIDlrprotorlpb3jgIIK562U5qGI77ya6ZSZ6K+vCuino+aekO+8mkNQVeaAp+iDveS4jeS7heWPluWGs+S6juS4u+mike+8jOi/mOS4juaetuaehOOAgee8k+WtmOOAgeaguOW/g+aVsOetieWkmuS4quWboOe0oOacieWFs+OAggoKNOOAgVvljZXpgInpophd5Lul5LiL5ZOq5Liq5LiN5piv5pON5L2c57O757uf77yfCkHjgIFXaW5kb3dzCkLjgIFMaW51eApD44CBT2ZmaWNlCkTjgIFtYWNPUwrnrZTmoYjvvJpDCuino+aekO+8mk9mZmljZeaYr+WKnuWFrOi9r+S7tuWll+S7tu+8jOS4jeaYr+aTjeS9nOezu+e7n+OAgldpbmRvd3PjgIFMaW51eOWSjG1hY09T6YO95piv5pON5L2c57O757uf44CCCgo144CBW+WIpOaWremimF3orqHnrpfmnLrnl4Xmr5Llj6/ku6XpgJrov4fnvZHnu5zkvKDmkq3jgIIK562U5qGI77ya5q2j56GuCuino+aekO+8muiuoeeul+acuueXheavkuWPr+S7pemAmui/h+WkmuenjemAlOW+hOS8oOaSre+8jOWMheaLrOe9kee7nOOAgeenu+WKqOWtmOWCqOiuvuWkh+OAgeeUteWtkOmCruS7tuetieOAgmAKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB2aXNpYmxlKHZhbCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB2YWwKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMucmVzZXRJbXBvcnQoKQogICAgICB9CiAgICB9LAogICAgZGlhbG9nVmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCB2YWwpCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDoh6rlrprkuYnkuIrkvKDmlrnms5UKICAgIGN1c3RvbVVwbG9hZChvcHRpb24pIHsKICAgICAgY29uc3QgZmlsZSA9IG9wdGlvbi5maWxlCiAgICAgIHRoaXMudXBsb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMAoKICAgICAgdXBsb2FkQW5kUGFyc2UoZmlsZSwgdGhpcy5iYW5rSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB0aGlzLnVwbG9hZGVkRmlsZSA9IHsKICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lLAogICAgICAgICAgICByZXNwb25zZTogcmVzcG9uc2UKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuZWRpdENvbnRlbnQgPSByZXNwb25zZS5kYXRhLmVkaXRhYmxlQ29udGVudCB8fCAnJwogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+aho+S4iuS8oOaIkOWKnycpCiAgICAgICAgICBvcHRpb24ub25TdWNjZXNzKHJlc3BvbnNlLCBmaWxlKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5paH5qGj5LiK5Lyg5aSx6LSlJykKICAgICAgICAgIG9wdGlvbi5vbkVycm9yKG5ldyBFcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S4iuS8oOWksei0pScpLCBmaWxlKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UKICAgICAgICBjb25zb2xlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKUnLCBlcnJvcikKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKUnKQogICAgICAgIG9wdGlvbi5vbkVycm9yKGVycm9yLCBmaWxlKQogICAgICB9KQogICAgfSwKCiAgICAvLyDmlofku7bkuIrkvKDliY3pqozor4EKICAgIGJlZm9yZUZpbGVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBpc0RvY3ggPSBmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aCgnLmRvY3gnKQogICAgICBjb25zdCBpc0x0MTBNID0gZmlsZS5zaXplIC8gMTAyNCAvIDEwMjQgPCAxMAoKICAgICAgaWYgKCFpc0RvY3gpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKAuZG9jeOagvOW8j+eahFdvcmTmlofmoaMhJykKICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgfQogICAgICBpZiAoIWlzTHQxME0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cxME1CIScpCiAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgIH0KCiAgICAgIHRoaXMudXBsb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLnVwbG9hZFByb2dyZXNzID0gMAogICAgICByZXR1cm4gdHJ1ZQogICAgfSwKCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/vvIjnlLFjdXN0b21VcGxvYWTosIPnlKjvvIkKICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlKSB7CiAgICAgIC8vIOi/meS4quaWueazleeOsOWcqOeUsWN1c3RvbVVwbG9hZOWkhOeQhu+8jOS/neeVmeS7pemYsumcgOimgQogICAgfSwKCiAgICAvLyDmlofku7bkuIrkvKDlpLHotKXvvIjnlLFjdXN0b21VcGxvYWTosIPnlKjvvIkKICAgIGhhbmRsZUZpbGVFcnJvcihlcnIsIGZpbGUpIHsKICAgICAgLy8g6L+Z5Liq5pa55rOV546w5Zyo55SxY3VzdG9tVXBsb2Fk5aSE55CG77yM5L+d55WZ5Lul6Ziy6ZyA6KaBCiAgICB9LAoKICAgIC8vIOe8lui+keWZqOWGheWuueWPmOWMlgogICAgb25Db250ZW50Q2hhbmdlKGRhdGEpIHsKICAgICAgLy8gZGF0YeWMheWQqyB7IGh0bWwsIHRleHQsIHF1aWxsIH0KICAgICAgaWYgKGRhdGEgJiYgZGF0YS5odG1sKSB7CiAgICAgICAgdGhpcy5lZGl0Q29udGVudCA9IGRhdGEuaHRtbAogICAgICB9CiAgICAgIC8vIOmYsuaKluWkhOeQhu+8jOmBv+WFjemikee5geivt+axggogICAgICBjbGVhclRpbWVvdXQodGhpcy5wcmV2aWV3VGltZXIpCiAgICAgIHRoaXMucHJldmlld1RpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5hdXRvUHJldmlldygpCiAgICAgIH0sIDEwMDApCiAgICB9LAoKICAgIC8vIOiHquWKqOmihOiniAogICAgYXV0b1ByZXZpZXcoKSB7CiAgICAgIGlmICghdGhpcy5lZGl0Q29udGVudCB8fCB0aGlzLmVkaXRDb250ZW50LnRyaW0oKSA9PT0gJycpIHsKICAgICAgICB0aGlzLnByZXZpZXdIdG1sID0gJycKICAgICAgICB0aGlzLnByZXZpZXdRdWVzdGlvbkNvdW50ID0gMAogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgfSwKCiAgICAvLyDkv53lrZjlhoXlrrkKICAgIHNhdmVDb250ZW50KCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGJhbmtJZDogdGhpcy5iYW5rSWQsCiAgICAgICAgY29udGVudDogdGhpcy5lZGl0Q29udGVudAogICAgICB9CiAgICAgIHNhdmVFZGl0ZWRDb250ZW50KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5YaF5a655L+d5a2Y5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S/neWtmOWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y5YaF5a655aSx6LSlJywgZXJyb3IpCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+d5a2Y5aSx6LSlJykKICAgICAgfSkKICAgIH0sCgogICAgLy8g6aKE6KeI5YaF5a65CiAgICBwcmV2aWV3Q29udGVudCgpIHsKICAgICAgaWYgKCF0aGlzLmVkaXRDb250ZW50IHx8IHRoaXMuZWRpdENvbnRlbnQudHJpbSgpID09PSAnJykgewogICAgICAgIHJldHVybgogICAgICB9CgogICAgICB0aGlzLnByZXZpZXdMb2FkaW5nID0gdHJ1ZQogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIGNvbnRlbnQ6IHRoaXMuZWRpdENvbnRlbnQgIC8vIOeOsOWcqOebtOaOpeS8oOmAkuaWh+acrOWGheWuue+8jOS4jemcgOimgUhUTUzovazmjaIKICAgICAgfQogICAgICAKICAgICAgcHJldmlld0NvbnRlbnQoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wcmV2aWV3TG9hZGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5wcmV2aWV3SHRtbCA9IHJlc3BvbnNlLmRhdGEucHJldmlld0h0bWwgfHwgJycKICAgICAgICAgIHRoaXMucHJldmlld1F1ZXN0aW9uQ291bnQgPSByZXNwb25zZS5kYXRhLnF1ZXN0aW9uQ291bnQgfHwgMAogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfpooTop4jlpLHotKUnKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gewogICAgICAgIHRoaXMucHJldmlld0xvYWRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mihOiniOWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+mihOiniOWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOehruiupOWvvOWFpQogICAgY29uZmlybUltcG9ydCgpIHsKICAgICAgdGhpcy5pbXBvcnRpbmcgPSB0cnVlCiAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICBjb250ZW50OiB0aGlzLmVkaXRDb250ZW50LAogICAgICAgIGFsbG93RHVwbGljYXRlOiB0aGlzLmltcG9ydE9wdGlvbnMuYWxsb3dEdXBsaWNhdGUKICAgICAgfQogICAgICAKICAgICAgaW1wb3J0RnJvbUVkaXRvcihkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmltcG9ydGluZyA9IGZhbHNlCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSByZXNwb25zZS5kYXRhCiAgICAgICAgICB0aGlzLm5leHRTdGVwKCkKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6aKY55uu5a+85YWl5oiQ5YqfJykKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeWksei0pScpCiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWFpeWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pScpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOS4i+S4gOatpQogICAgbmV4dFN0ZXAoKSB7CiAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwIDwgMykgewogICAgICAgIHRoaXMuY3VycmVudFN0ZXArKwogICAgICAgIGlmICh0aGlzLmN1cnJlbnRTdGVwID09PSAyKSB7CiAgICAgICAgICAvLyDov5vlhaXpooTop4jnoa7orqTmraXpqqTml7bvvIzoh6rliqjpooTop4gKICAgICAgICAgIHRoaXMucHJldmlld0NvbnRlbnQoKQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDkuIrkuIDmraUKICAgIHByZXZTdGVwKCkgewogICAgICBpZiAodGhpcy5jdXJyZW50U3RlcCA+IDApIHsKICAgICAgICB0aGlzLmN1cnJlbnRTdGVwLS0KICAgICAgfQogICAgfSwKCiAgICAvLyDlrozmiJDlr7zlhaUKICAgIGhhbmRsZUNvbXBsZXRlKCkgewogICAgICB0aGlzLiRlbWl0KCdzdWNjZXNzJykKICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpCiAgICB9LAoKICAgIC8vIOmHjee9ruWvvOWFpQogICAgcmVzZXRJbXBvcnQoKSB7CiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwCiAgICAgIHRoaXMuYWN0aXZlVGFiID0gJ3VwbG9hZCcKICAgICAgdGhpcy51cGxvYWRlZEZpbGUgPSBudWxsCiAgICAgIHRoaXMudXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy51cGxvYWRQcm9ncmVzcyA9IDAKICAgICAgdGhpcy5lZGl0Q29udGVudCA9ICcnCiAgICAgIHRoaXMucHJldmlld0h0bWwgPSAnJwogICAgICB0aGlzLnByZXZpZXdRdWVzdGlvbkNvdW50ID0gMAogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICB0aGlzLmltcG9ydE9wdGlvbnMgPSB7CiAgICAgICAgYWxsb3dEdXBsaWNhdGU6IGZhbHNlCiAgICAgIH0KICAgICAgdGhpcy5pbXBvcnRSZXN1bHQgPSB7CiAgICAgICAgc3VjY2Vzc0NvdW50OiAwLAogICAgICAgIGZhaWxDb3VudDogMCwKICAgICAgICBlcnJvcnM6IFtdCiAgICAgIH0KICAgIH0sCgogICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCgogICAgLy8g5qCH562+6aG15YiH5o2iCiAgICBoYW5kbGVUYWJDbGljayh0YWIpIHsKICAgICAgdGhpcy5hY3RpdmVUYWIgPSB0YWIubmFtZQogICAgfSwKCiAgICAvLyDlpI3liLbnpLrkvovliLDnvJbovpHlmagKICAgIGNvcHlFeGFtcGxlVG9FZGl0b3IoKSB7CiAgICAgIHRoaXMuZWRpdENvbnRlbnQgPSB0aGlzLmV4YW1wbGVDb250ZW50CiAgICAgIHRoaXMudXBsb2FkZWRGaWxlID0gewogICAgICAgIG5hbWU6ICfnpLrkvovlhoXlrrkuZG9jeCcsCiAgICAgICAgcmVzcG9uc2U6IHsgY29kZTogMjAwIH0KICAgICAgfQogICAgICB0aGlzLnBhcnNlRXJyb3JzID0gW10KICAgICAgdGhpcy5hY3RpdmVUYWIgPSAndXBsb2FkJwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ekuuS+i+WGheWuueW3suWkjeWItuWIsOe8lui+keWZqO+8jOWPr+S7peebtOaOpei/m+WFpeS4i+S4gOatpeS9k+mqjOWKn+iDvScpCgogICAgICAvLyDoh6rliqjpooTop4jnpLrkvovlhoXlrrkKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5hdXRvUHJldmlldygpCiAgICAgIH0sIDUwMCkKICAgIH0sCgogICAgLy8g5Yqg6L295ryU56S65YaF5a65CiAgICBsb2FkRGVtb0NvbnRlbnQoKSB7CiAgICAgIHRoaXMuY29weUV4YW1wbGVUb0VkaXRvcigpCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["RichTextImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkRA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "RichTextImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"题库文档导入\"\n    :visible.sync=\"dialogVisible\"\n    width=\"95%\"\n    :before-close=\"handleClose\"\n    append-to-body\n    class=\"rich-text-import-dialog\"\n  >\n    <div class=\"import-container\">\n      <!-- 步骤指示器 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"上传文档\"></el-step>\n        <el-step title=\"编辑内容\"></el-step>\n        <el-step title=\"预览确认\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 文档上传 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <div class=\"upload-options\">\n            <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\n              <el-tab-pane label=\"上传文档\" name=\"upload\">\n                <div class=\"upload-content\">\n                  <h3>上传Word文档</h3>\n                  <p>请上传包含题目的Word文档(.docx格式)，系统将自动解析文档内容</p>\n\n                  <div class=\"demo-section\">\n                    <el-button size=\"small\" type=\"text\" @click=\"loadDemoContent\">\n                      <i class=\"el-icon-magic-stick\"></i> 加载演示内容\n                    </el-button>\n                    <span class=\"demo-tip\">或者点击加载演示内容快速体验功能</span>\n                  </div>\n\n                  <el-upload\n                    ref=\"fileUpload\"\n                    :http-request=\"customUpload\"\n                    :on-success=\"handleFileSuccess\"\n                    :on-error=\"handleFileError\"\n                    :before-upload=\"beforeFileUpload\"\n                    :show-file-list=\"false\"\n                    accept=\".docx\"\n                    drag\n                  >\n                    <div class=\"upload-area\">\n                      <i class=\"el-icon-upload\"></i>\n                      <div class=\"upload-text\">\n                        <p>将Word文档拖到此处，或<em>点击上传</em></p>\n                        <p class=\"upload-tip\">支持 .docx 格式文件，文件大小不超过10MB</p>\n                      </div>\n                    </div>\n                  </el-upload>\n\n                  <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n                    <el-alert\n                      :title=\"`已上传文件：${uploadedFile.name}`\"\n                      type=\"success\"\n                      :closable=\"false\"\n                      show-icon\n                    />\n                  </div>\n\n                  <div v-if=\"uploading\" class=\"uploading-status\">\n                    <el-progress :percentage=\"50\" :show-text=\"false\"></el-progress>\n                    <p>正在上传并解析文档...</p>\n                  </div>\n                </div>\n              </el-tab-pane>\n\n              <el-tab-pane label=\"输入规范与范例\" name=\"rules\">\n                <div class=\"rules-content\">\n                  <h3>题目输入格式规范</h3>\n\n                  <div class=\"format-rules\">\n                    <div class=\"rule-section\">\n                      <h4>基本格式要求</h4>\n                      <ul>\n                        <li><strong>题目编号：</strong>使用\"数字、\"格式，如：1、2、3、</li>\n                        <li><strong>题型标识：</strong>[单选题]、[多选题]、[判断题]</li>\n                        <li><strong>选项格式：</strong>A、B、C、D、（大写字母+顿号）</li>\n                        <li><strong>答案格式：</strong>答案：A 或 答案：A,B,C</li>\n                        <li><strong>解析格式：</strong>解析：解析内容（可选）</li>\n                      </ul>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>标准范例</h4>\n                      <div class=\"example-container\">\n                        <div class=\"example-header\">\n                          <span>完整题目示例</span>\n                          <el-button size=\"mini\" type=\"primary\" @click=\"copyExampleToEditor\">\n                            <i class=\"el-icon-copy-document\"></i> 复制到编辑器\n                          </el-button>\n                        </div>\n                        <pre class=\"example-content\">{{ exampleContent }}</pre>\n                      </div>\n                    </div>\n\n                    <div class=\"rule-section\">\n                      <h4>注意事项</h4>\n                      <el-alert\n                        title=\"格式要求\"\n                        type=\"warning\"\n                        :closable=\"false\"\n                        show-icon\n                      >\n                        <ul>\n                          <li>严格按照格式要求编写，格式错误会导致解析失败</li>\n                          <li>题目编号必须连续，不能跳号</li>\n                          <li>选项字母必须大写，使用中文顿号</li>\n                          <li>多选题答案用逗号分隔，如：A,B,C</li>\n                          <li>判断题答案只能是\"正确\"或\"错误\"</li>\n                        </ul>\n                      </el-alert>\n                    </div>\n                  </div>\n                </div>\n              </el-tab-pane>\n            </el-tabs>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"handleClose\">取消</el-button>\n          <el-button type=\"primary\" :disabled=\"!uploadedFile\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 富文本编辑 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"editor-container\">\n          <div class=\"editor-header\">\n            <h3>编辑文档内容</h3>\n            <div class=\"editor-actions\">\n              <el-button size=\"small\" @click=\"saveContent\">保存内容</el-button>\n              <el-button size=\"small\" type=\"primary\" @click=\"previewContent\">实时预览</el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-preview-layout\">\n            <!-- 左侧富文本编辑器 -->\n            <div class=\"editor-panel\">\n              <div class=\"panel-header\">\n                <h4>编辑区域</h4>\n                <span class=\"panel-tip\">您可以在此编辑题目内容</span>\n              </div>\n              <div class=\"editor-wrapper\">\n                <editor\n                  v-model=\"editContent\"\n                  :min-height=\"350\"\n                  @on-change=\"onContentChange\"\n                  class=\"content-editor\"\n                />\n              </div>\n            </div>\n\n            <!-- 右侧预览区域 -->\n            <div class=\"preview-panel\">\n              <div class=\"panel-header\">\n                <h4>实时预览</h4>\n                <span class=\"panel-tip\">题目解析结果：{{ previewQuestionCount }}道题目</span>\n              </div>\n              <div class=\"preview-wrapper\">\n                <div v-if=\"previewLoading\" class=\"preview-loading\">\n                  <el-loading text=\"正在解析预览...\"></el-loading>\n                </div>\n                <div v-else-if=\"previewHtml\" class=\"preview-content\" v-html=\"previewHtml\"></div>\n                <div v-else class=\"preview-empty\">\n                  <el-empty description=\"暂无预览内容，请编辑左侧内容\"></el-empty>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :disabled=\"!editContent\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 预览确认 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"confirm-section\">\n          <h3>确认导入</h3>\n          <div class=\"import-summary\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析题目数量\" :value=\"previewQuestionCount\" suffix=\"道\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"解析错误\" :value=\"parseErrors.length\" suffix=\"个\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"目标题库\" :value=\"bankName\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <h4>解析错误信息</h4>\n            <el-alert\n              v-for=\"(error, index) in parseErrors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"warning\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n\n          <div class=\"import-options\">\n            <h4>导入选项</h4>\n            <el-checkbox v-model=\"importOptions.allowDuplicate\">允许导入重复题目</el-checkbox>\n          </div>\n\n          <div class=\"final-preview\">\n            <h4>最终预览</h4>\n            <div class=\"preview-content\" v-html=\"previewHtml\"></div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" :loading=\"importing\" @click=\"confirmImport\">确认导入</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 64px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          \n          <div class=\"import-result\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"成功导入\" :value=\"importResult.successCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"导入失败\" :value=\"importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"总计处理\" :value=\"importResult.successCount + importResult.failCount\" suffix=\"道题目\"></el-statistic>\n              </el-col>\n            </el-row>\n          </div>\n\n          <div v-if=\"importResult.errors && importResult.errors.length > 0\" class=\"import-errors\">\n            <h4>导入错误详情</h4>\n            <el-alert\n              v-for=\"(error, index) in importResult.errors\"\n              :key=\"index\"\n              :title=\"error\"\n              type=\"error\"\n              :closable=\"false\"\n              style=\"margin-bottom: 10px;\"\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { uploadAndParse, saveEditedContent, previewContent, importFromEditor } from '@/api/biz/questionBank'\nimport Editor from '@/components/Editor'\n\nexport default {\n  name: \"RichTextImport\",\n  components: {\n    Editor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    bankName: {\n      type: String,\n      default: '题库'\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n\n      // 标签页相关\n      activeTab: 'upload',\n\n      // 文件上传相关\n      uploadedFile: null,\n      uploading: false,\n      uploadProgress: 0,\n      \n      // 编辑器相关\n      editContent: '',\n      \n      // 预览相关\n      previewHtml: '',\n      previewQuestionCount: 0,\n      previewLoading: false,\n      parseErrors: [],\n      \n      // 导入相关\n      importing: false,\n      importOptions: {\n        allowDuplicate: false\n      },\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      },\n\n      // 示例内容\n      exampleContent: `1、[单选题]计算机的CPU主要功能是什么？\nA、存储数据\nB、处理数据和控制程序执行\nC、输入数据\nD、输出数据\n答案：B\n解析：CPU（中央处理器）是计算机的核心部件，主要负责处理数据和控制程序的执行。\n\n2、[多选题]以下哪些是计算机的输入设备？\nA、键盘\nB、鼠标\nC、显示器\nD、扫描仪\nE、打印机\n答案：A,B,D\n解析：键盘、鼠标和扫描仪都是输入设备，显示器和打印机是输出设备。\n\n3、[判断题]CPU的主频越高，计算机的性能就一定越好。\n答案：错误\n解析：CPU性能不仅取决于主频，还与架构、缓存、核心数等多个因素有关。\n\n4、[单选题]以下哪个不是操作系统？\nA、Windows\nB、Linux\nC、Office\nD、macOS\n答案：C\n解析：Office是办公软件套件，不是操作系统。Windows、Linux和macOS都是操作系统。\n\n5、[判断题]计算机病毒可以通过网络传播。\n答案：正确\n解析：计算机病毒可以通过多种途径传播，包括网络、移动存储设备、电子邮件等。`\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  methods: {\n    // 自定义上传方法\n    customUpload(option) {\n      const file = option.file\n      this.uploading = true\n      this.uploadProgress = 0\n\n      uploadAndParse(file, this.bankId).then(response => {\n        this.uploading = false\n        if (response.code === 200) {\n          this.uploadedFile = {\n            name: file.name,\n            response: response\n          }\n          this.editContent = response.data.editableContent || ''\n          this.parseErrors = response.data.errors || []\n          this.$message.success('文档上传成功')\n          option.onSuccess(response, file)\n        } else {\n          this.$message.error(response.msg || '文档上传失败')\n          option.onError(new Error(response.msg || '上传失败'), file)\n        }\n      }).catch(error => {\n        this.uploading = false\n        console.error('文件上传失败', error)\n        this.$message.error('文件上传失败')\n        option.onError(error, file)\n      })\n    },\n\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isDocx = file.name.toLowerCase().endsWith('.docx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isDocx) {\n        this.$message.error('只能上传.docx格式的Word文档!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过10MB!')\n        return false\n      }\n\n      this.uploading = true\n      this.uploadProgress = 0\n      return true\n    },\n\n    // 文件上传成功（由customUpload调用）\n    handleFileSuccess(response, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 文件上传失败（由customUpload调用）\n    handleFileError(err, file) {\n      // 这个方法现在由customUpload处理，保留以防需要\n    },\n\n    // 编辑器内容变化\n    onContentChange(data) {\n      // data包含 { html, text, quill }\n      if (data && data.html) {\n        this.editContent = data.html\n      }\n      // 防抖处理，避免频繁请求\n      clearTimeout(this.previewTimer)\n      this.previewTimer = setTimeout(() => {\n        this.autoPreview()\n      }, 1000)\n    },\n\n    // 自动预览\n    autoPreview() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        this.previewHtml = ''\n        this.previewQuestionCount = 0\n        return\n      }\n      this.previewContent()\n    },\n\n    // 保存内容\n    saveContent() {\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent\n      }\n      saveEditedContent(data).then(response => {\n        if (response.code === 200) {\n          this.$message.success('内容保存成功')\n        } else {\n          this.$message.error(response.msg || '保存失败')\n        }\n      }).catch(error => {\n        console.error('保存内容失败', error)\n        this.$message.error('保存失败')\n      })\n    },\n\n    // 预览内容\n    previewContent() {\n      if (!this.editContent || this.editContent.trim() === '') {\n        return\n      }\n\n      this.previewLoading = true\n      const data = {\n        content: this.editContent  // 现在直接传递文本内容，不需要HTML转换\n      }\n      \n      previewContent(data).then(response => {\n        this.previewLoading = false\n        if (response.code === 200) {\n          this.previewHtml = response.data.previewHtml || ''\n          this.previewQuestionCount = response.data.questionCount || 0\n          this.parseErrors = response.data.errors || []\n        } else {\n          this.$message.error(response.msg || '预览失败')\n        }\n      }).catch(error => {\n        this.previewLoading = false\n        console.error('预览失败', error)\n        this.$message.error('预览失败')\n      })\n    },\n\n    // 确认导入\n    confirmImport() {\n      this.importing = true\n      const data = {\n        bankId: this.bankId,\n        content: this.editContent,\n        allowDuplicate: this.importOptions.allowDuplicate\n      }\n      \n      importFromEditor(data).then(response => {\n        this.importing = false\n        if (response.code === 200) {\n          this.importResult = response.data\n          this.nextStep()\n          this.$message.success('题目导入成功')\n        } else {\n          this.$message.error(response.msg || '导入失败')\n        }\n      }).catch(error => {\n        this.importing = false\n        console.error('导入失败', error)\n        this.$message.error('导入失败')\n      })\n    },\n\n    // 下一步\n    nextStep() {\n      if (this.currentStep < 3) {\n        this.currentStep++\n        if (this.currentStep === 2) {\n          // 进入预览确认步骤时，自动预览\n          this.previewContent()\n        }\n      }\n    },\n\n    // 上一步\n    prevStep() {\n      if (this.currentStep > 0) {\n        this.currentStep--\n      }\n    },\n\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.activeTab = 'upload'\n      this.uploadedFile = null\n      this.uploading = false\n      this.uploadProgress = 0\n      this.editContent = ''\n      this.previewHtml = ''\n      this.previewQuestionCount = 0\n      this.parseErrors = []\n      this.importing = false\n      this.importOptions = {\n        allowDuplicate: false\n      }\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    },\n\n    // 标签页切换\n    handleTabClick(tab) {\n      this.activeTab = tab.name\n    },\n\n    // 复制示例到编辑器\n    copyExampleToEditor() {\n      this.editContent = this.exampleContent\n      this.uploadedFile = {\n        name: '示例内容.docx',\n        response: { code: 200 }\n      }\n      this.parseErrors = []\n      this.activeTab = 'upload'\n      this.$message.success('示例内容已复制到编辑器，可以直接进入下一步体验功能')\n\n      // 自动预览示例内容\n      setTimeout(() => {\n        this.autoPreview()\n      }, 500)\n    },\n\n    // 加载演示内容\n    loadDemoContent() {\n      this.copyExampleToEditor()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.rich-text-import-dialog {\n  .el-dialog {\n    margin-top: 5vh !important;\n  }\n\n  .el-dialog__body {\n    padding: 20px;\n  }\n}\n\n.import-container {\n  min-height: 600px;\n}\n\n.step-content {\n  min-height: 500px;\n  display: flex;\n  flex-direction: column;\n}\n\n.step-actions {\n  margin-top: auto;\n  padding-top: 20px;\n  border-top: 1px solid #e9ecef;\n  text-align: right;\n}\n\n/* 文件上传样式 */\n.upload-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.upload-section p {\n  margin-bottom: 30px;\n  color: #666;\n  text-align: center;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 10px 0;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n}\n\n.uploaded-file {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.uploading-status {\n  margin-top: 20px;\n  width: 100%;\n  max-width: 400px;\n  text-align: center;\n}\n\n.demo-section {\n  margin: 20px 0;\n  text-align: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px dashed #d9ecff;\n}\n\n.demo-tip {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #666;\n}\n\n/* 标签页样式 */\n.upload-options {\n  width: 100%;\n}\n\n.upload-content,\n.rules-content {\n  padding: 20px 0;\n}\n\n.rules-content h3 {\n  margin-bottom: 20px;\n  color: #333;\n  text-align: center;\n}\n\n.format-rules {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.rule-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.rule-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.rule-section ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin-bottom: 8px;\n  line-height: 1.6;\n  color: #555;\n}\n\n.rule-section li strong {\n  color: #333;\n}\n\n.example-container {\n  background: #fff;\n  border: 1px solid #d9ecff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.example-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #ecf5ff;\n  border-bottom: 1px solid #d9ecff;\n}\n\n.example-header span {\n  font-weight: 500;\n  color: #409eff;\n}\n\n.example-content {\n  margin: 0;\n  padding: 16px;\n  background: #fafafa;\n  font-family: 'Courier New', Consolas, monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n/* 规则提示样式 */\n.rule-section .el-alert {\n  margin-top: 15px;\n}\n\n.rule-section .el-alert ul {\n  margin: 10px 0 0 0;\n  padding-left: 20px;\n}\n\n.rule-section .el-alert li {\n  margin-bottom: 5px;\n  font-size: 13px;\n}\n\n/* 编辑器样式 */\n.editor-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.editor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.editor-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.editor-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.editor-preview-layout {\n  flex: 1;\n  display: flex;\n  gap: 20px;\n  min-height: 400px;\n}\n\n.editor-panel,\n.preview-panel {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.panel-header {\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.panel-header h4 {\n  margin: 0;\n  font-size: 14px;\n  color: #333;\n}\n\n.panel-tip {\n  font-size: 12px;\n  color: #666;\n}\n\n.editor-wrapper,\n.preview-wrapper {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.content-editor {\n  flex: 1;\n  min-height: 350px;\n}\n\n.preview-wrapper {\n  position: relative;\n}\n\n.preview-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.8);\n}\n\n.preview-content {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n  max-height: 350px;\n}\n\n.preview-empty {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 确认导入样式 */\n.confirm-section {\n  flex: 1;\n}\n\n.confirm-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.import-summary {\n  margin-bottom: 30px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.error-section {\n  margin-bottom: 30px;\n}\n\n.error-section h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n.import-options {\n  margin-bottom: 30px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n}\n\n.import-options h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.final-preview {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.final-preview h4 {\n  margin: 0;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n  color: #333;\n}\n\n.final-preview .preview-content {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n/* 完成页面样式 */\n.result-section {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-section h3 {\n  margin-bottom: 30px;\n  color: #333;\n}\n\n.import-result {\n  margin-bottom: 30px;\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors {\n  width: 100%;\n  max-width: 600px;\n}\n\n.import-errors h4 {\n  margin-bottom: 15px;\n  color: #f56c6c;\n}\n\n/* 预览内容样式 */\n.preview-content .questions-preview {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n}\n\n.preview-content .question-item {\n  margin-bottom: 20px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.preview-content .question-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.preview-content .question-number {\n  color: #409eff;\n  font-weight: bold;\n}\n\n.preview-content .question-type {\n  color: #409eff;\n  font-weight: 500;\n}\n\n.preview-content .question-difficulty {\n  color: #666;\n  font-size: 12px;\n}\n\n.preview-content .question-content {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #333;\n}\n\n.preview-content .question-options {\n  margin-bottom: 15px;\n}\n\n.preview-content .option-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  margin-bottom: 8px;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n}\n\n.preview-content .option-key {\n  font-weight: 500;\n  color: #409eff;\n  min-width: 20px;\n}\n\n.preview-content .option-content {\n  flex: 1;\n  line-height: 1.5;\n}\n\n.preview-content .question-answer {\n  margin-bottom: 10px;\n  padding: 8px 12px;\n  background: #e8f5e8;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.preview-content .question-analysis {\n  padding: 8px 12px;\n  background: #f0f9ff;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .editor-preview-layout {\n    flex-direction: column;\n  }\n\n  .editor-panel,\n  .preview-panel {\n    min-height: 300px;\n  }\n}\n</style>\n"]}]}