{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAnQC91dGlscy9hdXRoJwppbXBvcnQgeyBkb3dubG9hZFRlbXBsYXRlLCBwYXJzZUltcG9ydEZpbGUsIGJhdGNoSW1wb3J0UXVlc3Rpb25zIH0gZnJvbSAnQC9hcGkvYml6L3F1ZXN0aW9uJwppbXBvcnQgeyBkb3dubG9hZCB9IGZyb20gJ0AvdXRpbHMvcmVxdWVzdCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQmF0Y2hJbXBvcnQiLAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgYmFua0lkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9LAogICAgZGVmYXVsdE1vZGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnZXhjZWwnIC8vIGV4Y2VsIOaIliBkb2N1bWVudAogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBjdXJyZW50U3RlcDogMCwKICAgICAgaW1wb3J0TW9kZTogJ2V4Y2VsJywgLy8g5a+85YWl5qih5byP77yaZXhjZWwg5oiWIGRvY3VtZW50CiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvY29tbW9uL3VwbG9hZCcsCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyBnZXRUb2tlbigpCiAgICAgIH0sCiAgICAgIHVwbG9hZGVkRmlsZTogbnVsbCwKICAgICAgcGFyc2luZzogZmFsc2UsCiAgICAgIGltcG9ydGluZzogZmFsc2UsCiAgICAgIHBhcnNlZERhdGE6IFtdLAogICAgICBwYXJzZUVycm9yczogW10sCiAgICAgIGltcG9ydFJlc3VsdDogewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgdmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdmFsCiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLnJlc2V0SW1wb3J0KCkKICAgICAgfQogICAgfSwKICAgIGRpYWxvZ1Zpc2libGUodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZTp2aXNpYmxlJywgdmFsKQogICAgfSwKICAgIGRlZmF1bHRNb2RlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5pbXBvcnRNb2RlID0gdmFsCiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOS4i+i9veaooeadvwogICAgZG93bmxvYWRUZW1wbGF0ZSh0eXBlKSB7CiAgICAgIGNvbnN0IGZpbGVOYW1lID0gdHlwZSA9PT0gJ2FsbCcgPyAn6aKY55uu5a+85YWl5qih5p2/Lnhsc3gnIDogYCR7dGhpcy5nZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpfeWvvOWFpeaooeadvy54bHN4YAogICAgICBkb3dubG9hZCgnL2Jpei9xdWVzdGlvbi9kb3dubG9hZFRlbXBsYXRlJywgeyBxdWVzdGlvblR5cGU6IHR5cGUgfSwgZmlsZU5hbWUpCiAgICB9LAogICAgLy8g6I635Y+W6aKY5Z6L5ZCN56ewCiAgICBnZXRRdWVzdGlvblR5cGVOYW1lKHR5cGUpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlXSB8fCAn5pyq55+l6aKY5Z6LJwogICAgfSwKICAgIC8vIOiOt+WPlumimOWei+minOiJsgogICAgZ2V0UXVlc3Rpb25UeXBlQ29sb3IodHlwZSkgewogICAgICBjb25zdCBjb2xvck1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ3ByaW1hcnknLAogICAgICAgICdtdWx0aXBsZSc6ICdzdWNjZXNzJywKICAgICAgICAnanVkZ21lbnQnOiAnd2FybmluZycKICAgICAgfQogICAgICByZXR1cm4gY29sb3JNYXBbdHlwZV0gfHwgJ2luZm8nCiAgICB9LAogICAgLy8g5LiL5LiA5q2lCiAgICBuZXh0U3RlcCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcCsrCiAgICB9LAogICAgLy8g5LiK5LiA5q2lCiAgICBwcmV2U3RlcCgpIHsKICAgICAgdGhpcy5jdXJyZW50U3RlcC0tCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5YmN6aqM6K+BCiAgICBiZWZvcmVGaWxlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgaXNMdDEwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgMTAKCiAgICAgIGlmICh0aGlzLmltcG9ydE1vZGUgPT09ICdleGNlbCcpIHsKICAgICAgICBjb25zdCBpc0V4Y2VsID0gZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnIHx8CiAgICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJwogICAgICAgIGlmICghaXNFeGNlbCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygRXhjZWzmlofku7YhJykKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSBlbHNlIGlmICh0aGlzLmltcG9ydE1vZGUgPT09ICdkb2N1bWVudCcpIHsKICAgICAgICBjb25zdCBpc0RvY3VtZW50ID0gZmlsZS50eXBlID09PSAndGV4dC9wbGFpbicgfHwKICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlLnR5cGUgPT09ICdhcHBsaWNhdGlvbi9tc3dvcmQnIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnIHx8CiAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy50eHQnKSB8fAogICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGUubmFtZS50b0xvd2VyQ2FzZSgpLmVuZHNXaXRoKCcuZG9jJykgfHwKICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aCgnLmRvY3gnKQogICAgICAgIGlmICghaXNEb2N1bWVudCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5Lyg5paH5qGj5paH5Lu2KC50eHQsIC5kb2MsIC5kb2N4KSEnKQogICAgICAgICAgcmV0dXJuIGZhbHNlCiAgICAgICAgfQogICAgICB9CgogICAgICBpZiAoIWlzTHQxME0pIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQiEnKQogICAgICAgIHJldHVybiBmYWxzZQogICAgICB9CiAgICAgIHJldHVybiB0cnVlCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5YqfCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkgewogICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgdGhpcy51cGxvYWRlZEZpbGUgPSB7CiAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUsCiAgICAgICAgICB1cmw6IHJlc3BvbnNlLnVybCwKICAgICAgICAgIGZpbGVOYW1lOiByZXNwb25zZS5maWxlTmFtZQogICAgICAgIH0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuS4iuS8oOaIkOWKnycpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpCiAgICAgIH0KICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDlpLHotKUKICAgIGhhbmRsZUZpbGVFcnJvcigpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlJykKICAgIH0sCiAgICAvLyDop6PmnpDmlofku7YKICAgIHBhcnNlRmlsZSgpIHsKICAgICAgdGhpcy5wYXJzaW5nID0gdHJ1ZQoKICAgICAgaWYgKHRoaXMuaW1wb3J0TW9kZSA9PT0gJ2V4Y2VsJykgewogICAgICAgIC8vIEV4Y2Vs5paH5Lu26Kej5p6QCiAgICAgICAgY29uc3QgcGFyc2VEYXRhID0gewogICAgICAgICAgZmlsZU5hbWU6IHRoaXMudXBsb2FkZWRGaWxlLmZpbGVOYW1lLAogICAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZAogICAgICAgIH0KICAgICAgICBwYXJzZUltcG9ydEZpbGUocGFyc2VEYXRhKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMucGFyc2luZyA9IGZhbHNlCiAgICAgICAgICB0aGlzLnBhcnNlZERhdGEgPSByZXNwb25zZS5kYXRhLnF1ZXN0aW9ucyB8fCBbXQogICAgICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IHJlc3BvbnNlLmRhdGEuZXJyb3JzIHx8IFtdCiAgICAgICAgICB0aGlzLm5leHRTdGVwKCkKICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICB0aGlzLnBhcnNpbmcgPSBmYWxzZQogICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q5paH5Lu25aSx6LSlJywgZXJyb3IpCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfop6PmnpDmlofku7blpLHotKUnKQogICAgICAgIH0pCiAgICAgIH0gZWxzZSBpZiAodGhpcy5pbXBvcnRNb2RlID09PSAnZG9jdW1lbnQnKSB7CiAgICAgICAgLy8g5paH5qGj5YaF5a656Kej5p6QCiAgICAgICAgdGhpcy5wYXJzZURvY3VtZW50Q29udGVudCgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g6Kej5p6Q5paH5qGj5YaF5a65CiAgICBhc3luYyBwYXJzZURvY3VtZW50Q29udGVudCgpIHsKICAgICAgdHJ5IHsKICAgICAgICAvLyDor7vlj5bmlofku7blhoXlrrkKICAgICAgICBjb25zdCBmaWxlQ29udGVudCA9IGF3YWl0IHRoaXMucmVhZEZpbGVDb250ZW50KHRoaXMudXBsb2FkZWRGaWxlLnVybCkKCiAgICAgICAgLy8g6Kej5p6Q6aKY55uuCiAgICAgICAgY29uc3QgcGFyc2VSZXN1bHQgPSB0aGlzLnBhcnNlUXVlc3Rpb25Db250ZW50KGZpbGVDb250ZW50KQoKICAgICAgICB0aGlzLnBhcnNpbmcgPSBmYWxzZQogICAgICAgIHRoaXMucGFyc2VkRGF0YSA9IHBhcnNlUmVzdWx0LnF1ZXN0aW9ucwogICAgICAgIHRoaXMucGFyc2VFcnJvcnMgPSBwYXJzZVJlc3VsdC5lcnJvcnMKICAgICAgICB0aGlzLm5leHRTdGVwKCkKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICB0aGlzLnBhcnNpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOaWh+aho+WGheWuueWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ino+aekOaWh+aho+WGheWuueWksei0pScpCiAgICAgIH0KICAgIH0sCgogICAgLy8g6K+75Y+W5paH5Lu25YaF5a65CiAgICByZWFkRmlsZUNvbnRlbnQoZmlsZVVybCkgewogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gewogICAgICAgIGNvbnN0IHhociA9IG5ldyBYTUxIdHRwUmVxdWVzdCgpCiAgICAgICAgeGhyLm9wZW4oJ0dFVCcsIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyBmaWxlVXJsLCB0cnVlKQogICAgICAgIHhoci5zZXRSZXF1ZXN0SGVhZGVyKCdBdXRob3JpemF0aW9uJywgJ0JlYXJlciAnICsgZ2V0VG9rZW4oKSkKICAgICAgICB4aHIucmVzcG9uc2VUeXBlID0gJ3RleHQnCgogICAgICAgIHhoci5vbmxvYWQgPSBmdW5jdGlvbigpIHsKICAgICAgICAgIGlmICh4aHIuc3RhdHVzID09PSAyMDApIHsKICAgICAgICAgICAgcmVzb2x2ZSh4aHIucmVzcG9uc2VUZXh0KQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcign6K+75Y+W5paH5Lu25aSx6LSlJykpCiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICB4aHIub25lcnJvciA9IGZ1bmN0aW9uKCkgewogICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcign6K+75Y+W5paH5Lu25aSx6LSlJykpCiAgICAgICAgfQoKICAgICAgICB4aHIuc2VuZCgpCiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOino+aekOmimOebruWGheWuuQogICAgcGFyc2VRdWVzdGlvbkNvbnRlbnQoY29udGVudCkgewogICAgICBjb25zdCBxdWVzdGlvbnMgPSBbXQogICAgICBjb25zdCBlcnJvcnMgPSBbXQoKICAgICAgdHJ5IHsKICAgICAgICAvLyDmjInpopjlnovliIblibLlhoXlrrkKICAgICAgICBjb25zdCBzZWN0aW9ucyA9IHRoaXMuc3BsaXRCeVF1ZXN0aW9uVHlwZShjb250ZW50KQoKICAgICAgICBzZWN0aW9ucy5mb3JFYWNoKChzZWN0aW9uLCBzZWN0aW9uSW5kZXgpID0+IHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGNvbnN0IHBhcnNlZFF1ZXN0aW9ucyA9IHRoaXMucGFyc2VTZWN0aW9uUXVlc3Rpb25zKHNlY3Rpb24pCiAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKC4uLnBhcnNlZFF1ZXN0aW9ucykKICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIGVycm9ycy5wdXNoKGDnrKwke3NlY3Rpb25JbmRleCArIDF95Liq6aKY5Z6L5Yy65Z+f6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgICAgIH0KICAgICAgICB9KQoKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBlcnJvcnMucHVzaChg5paH5qGj6Kej5p6Q5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2V9YCkKICAgICAgfQoKICAgICAgcmV0dXJuIHsgcXVlc3Rpb25zLCBlcnJvcnMgfQogICAgfSwKCiAgICAvLyDmjInpopjlnovliIblibLlhoXlrrkKICAgIHNwbGl0QnlRdWVzdGlvblR5cGUoY29udGVudCkgewogICAgICBjb25zdCBzZWN0aW9ucyA9IFtdCiAgICAgIGNvbnN0IHR5cGVSZWdleCA9IC9cWyjljZXpgInpoph85aSa6YCJ6aKYfOWIpOaWremimClcXS9nCgogICAgICBsZXQgbGFzdEluZGV4ID0gMAogICAgICBsZXQgbWF0Y2gKICAgICAgbGV0IGN1cnJlbnRUeXBlID0gbnVsbAoKICAgICAgd2hpbGUgKChtYXRjaCA9IHR5cGVSZWdleC5leGVjKGNvbnRlbnQpKSAhPT0gbnVsbCkgewogICAgICAgIGlmIChjdXJyZW50VHlwZSkgewogICAgICAgICAgLy8g5L+d5a2Y5LiK5LiA5Liq5Yy65Z+fCiAgICAgICAgICBzZWN0aW9ucy5wdXNoKHsKICAgICAgICAgICAgdHlwZTogY3VycmVudFR5cGUsCiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQuc3Vic3RyaW5nKGxhc3RJbmRleCwgbWF0Y2guaW5kZXgpLnRyaW0oKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgICAgY3VycmVudFR5cGUgPSBtYXRjaFsxXQogICAgICAgIGxhc3RJbmRleCA9IG1hdGNoLmluZGV4ICsgbWF0Y2hbMF0ubGVuZ3RoCiAgICAgIH0KCiAgICAgIC8vIOS/neWtmOacgOWQjuS4gOS4quWMuuWfnwogICAgICBpZiAoY3VycmVudFR5cGUpIHsKICAgICAgICBzZWN0aW9ucy5wdXNoKHsKICAgICAgICAgIHR5cGU6IGN1cnJlbnRUeXBlLAogICAgICAgICAgY29udGVudDogY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4KS50cmltKCkKICAgICAgICB9KQogICAgICB9CgogICAgICByZXR1cm4gc2VjdGlvbnMKICAgIH0sCgogICAgLy8g6Kej5p6Q5Yy65Z+f5YaF55qE6aKY55uuCiAgICBwYXJzZVNlY3Rpb25RdWVzdGlvbnMoc2VjdGlvbikgewogICAgICBjb25zdCBxdWVzdGlvbnMgPSBbXQogICAgICBjb25zdCBxdWVzdGlvblR5cGUgPSB0aGlzLmNvbnZlcnRRdWVzdGlvblR5cGUoc2VjdGlvbi50eXBlKQoKICAgICAgLy8g5oyJ6aKY5Y+35YiG5Ymy6aKY55uuCiAgICAgIGNvbnN0IHF1ZXN0aW9uQmxvY2tzID0gdGhpcy5zcGxpdEJ5UXVlc3Rpb25OdW1iZXIoc2VjdGlvbi5jb250ZW50KQoKICAgICAgcXVlc3Rpb25CbG9ja3MuZm9yRWFjaCgoYmxvY2ssIGluZGV4KSA9PiB7CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGNvbnN0IHF1ZXN0aW9uID0gdGhpcy5wYXJzZVF1ZXN0aW9uQmxvY2soYmxvY2ssIHF1ZXN0aW9uVHlwZSwgaW5kZXggKyAxKQogICAgICAgICAgaWYgKHF1ZXN0aW9uKSB7CiAgICAgICAgICAgIHF1ZXN0aW9ucy5wdXNoKHF1ZXN0aW9uKQogICAgICAgICAgfQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYOesrCR7aW5kZXggKyAxfemimOino+aekOWksei0pTogJHtlcnJvci5tZXNzYWdlfWApCiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIHF1ZXN0aW9ucwogICAgfSwKCiAgICAvLyDmjInpopjlj7fliIblibLpopjnm64KICAgIHNwbGl0QnlRdWVzdGlvbk51bWJlcihjb250ZW50KSB7CiAgICAgIGNvbnN0IGJsb2NrcyA9IFtdCiAgICAgIGNvbnN0IG51bWJlclJlZ2V4ID0gL15ccyooXGQrKVsuOu+8mu+8jl1ccyovZ20KCiAgICAgIGxldCBsYXN0SW5kZXggPSAwCiAgICAgIGxldCBtYXRjaAoKICAgICAgd2hpbGUgKChtYXRjaCA9IG51bWJlclJlZ2V4LmV4ZWMoY29udGVudCkpICE9PSBudWxsKSB7CiAgICAgICAgaWYgKGxhc3RJbmRleCA+IDApIHsKICAgICAgICAgIC8vIOS/neWtmOS4iuS4gOmimAogICAgICAgICAgYmxvY2tzLnB1c2goY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4LCBtYXRjaC5pbmRleCkudHJpbSgpKQogICAgICAgIH0KICAgICAgICBsYXN0SW5kZXggPSBtYXRjaC5pbmRleAogICAgICB9CgogICAgICAvLyDkv53lrZjmnIDlkI7kuIDpopgKICAgICAgaWYgKGxhc3RJbmRleCA8IGNvbnRlbnQubGVuZ3RoKSB7CiAgICAgICAgYmxvY2tzLnB1c2goY29udGVudC5zdWJzdHJpbmcobGFzdEluZGV4KS50cmltKCkpCiAgICAgIH0KCiAgICAgIHJldHVybiBibG9ja3MuZmlsdGVyKGJsb2NrID0+IGJsb2NrLmxlbmd0aCA+IDApCiAgICB9LAoKICAgIC8vIOino+aekOWNleS4qumimOebruWdlwogICAgcGFyc2VRdWVzdGlvbkJsb2NrKGJsb2NrLCBxdWVzdGlvblR5cGUsIHF1ZXN0aW9uSW5kZXgpIHsKICAgICAgY29uc3QgbGluZXMgPSBibG9jay5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKGxpbmUgPT4gbGluZS5sZW5ndGggPiAwKQoKICAgICAgaWYgKGxpbmVzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRocm93IG5ldyBFcnJvcign6aKY55uu5YaF5a655Li656m6JykKICAgICAgfQoKICAgICAgLy8g5o+Q5Y+W6aKY5Y+35ZKM6aKY5bmyCiAgICAgIGNvbnN0IGZpcnN0TGluZSA9IGxpbmVzWzBdCiAgICAgIGNvbnN0IG51bWJlck1hdGNoID0gZmlyc3RMaW5lLm1hdGNoKC9eXHMqKFxkKylbLjrvvJrvvI5dXHMqKC4qKS8pCiAgICAgIGlmICghbnVtYmVyTWF0Y2gpIHsKICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+mimOWPt+agvOW8j+S4jeato+ehricpCiAgICAgIH0KCiAgICAgIGxldCBxdWVzdGlvbkNvbnRlbnQgPSBudW1iZXJNYXRjaFsyXQogICAgICBsZXQgY3VycmVudExpbmVJbmRleCA9IDEKCiAgICAgIC8vIOe7p+e7reivu+WPlumimOW5suWGheWuue+8iOebtOWIsOmBh+WIsOmAiemhue+8iQogICAgICB3aGlsZSAoY3VycmVudExpbmVJbmRleCA8IGxpbmVzLmxlbmd0aCkgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tjdXJyZW50TGluZUluZGV4XQogICAgICAgIGlmICh0aGlzLmlzT3B0aW9uTGluZShsaW5lKSkgewogICAgICAgICAgYnJlYWsKICAgICAgICB9CiAgICAgICAgcXVlc3Rpb25Db250ZW50ICs9ICdcbicgKyBsaW5lCiAgICAgICAgY3VycmVudExpbmVJbmRleCsrCiAgICAgIH0KCiAgICAgIGNvbnN0IHF1ZXN0aW9uID0gewogICAgICAgIHF1ZXN0aW9uVHlwZTogcXVlc3Rpb25UeXBlLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogcXVlc3Rpb25Db250ZW50LnRyaW0oKSwKICAgICAgICBkaWZmaWN1bHR5OiAn5Lit562JJywKICAgICAgICBleHBsYW5hdGlvbjogJycsCiAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgY29ycmVjdEFuc3dlcjogJycKICAgICAgfQoKICAgICAgLy8g6Kej5p6Q6YCJ6aG577yI5a+55LqO6YCJ5oup6aKY77yJCiAgICAgIGlmIChxdWVzdGlvblR5cGUgIT09ICdqdWRnbWVudCcpIHsKICAgICAgICBjb25zdCBvcHRpb25SZXN1bHQgPSB0aGlzLnBhcnNlT3B0aW9ucyhsaW5lcywgY3VycmVudExpbmVJbmRleCkKICAgICAgICBxdWVzdGlvbi5vcHRpb25zID0gb3B0aW9uUmVzdWx0Lm9wdGlvbnMKICAgICAgICBjdXJyZW50TGluZUluZGV4ID0gb3B0aW9uUmVzdWx0Lm5leHRJbmRleAogICAgICB9CgogICAgICAvLyDop6PmnpDnrZTmoYjjgIHop6PmnpDjgIHpmr7luqYKICAgICAgdGhpcy5wYXJzZVF1ZXN0aW9uTWV0YShsaW5lcywgY3VycmVudExpbmVJbmRleCwgcXVlc3Rpb24pCgogICAgICByZXR1cm4gcXVlc3Rpb24KICAgIH0sCgogICAgLy8g5Yik5pat5piv5ZCm5Li66YCJ6aG56KGMCiAgICBpc09wdGlvbkxpbmUobGluZSkgewogICAgICByZXR1cm4gL15bQS1aYS16XVsuOu+8mu+8jl1ccyovLnRlc3QobGluZSkKICAgIH0sCgogICAgLy8g6Kej5p6Q6YCJ6aG5CiAgICBwYXJzZU9wdGlvbnMobGluZXMsIHN0YXJ0SW5kZXgpIHsKICAgICAgY29uc3Qgb3B0aW9ucyA9IFtdCiAgICAgIGxldCBjdXJyZW50SW5kZXggPSBzdGFydEluZGV4CgogICAgICB3aGlsZSAoY3VycmVudEluZGV4IDwgbGluZXMubGVuZ3RoKSB7CiAgICAgICAgY29uc3QgbGluZSA9IGxpbmVzW2N1cnJlbnRJbmRleF0KICAgICAgICBjb25zdCBvcHRpb25NYXRjaCA9IGxpbmUubWF0Y2goL14oW0EtWmEtel0pWy4677ya77yOXVxzKiguKikvKQoKICAgICAgICBpZiAoIW9wdGlvbk1hdGNoKSB7CiAgICAgICAgICBicmVhawogICAgICAgIH0KCiAgICAgICAgb3B0aW9ucy5wdXNoKHsKICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uTWF0Y2hbMV0udG9VcHBlckNhc2UoKSwKICAgICAgICAgIG9wdGlvbkNvbnRlbnQ6IG9wdGlvbk1hdGNoWzJdLnRyaW0oKQogICAgICAgIH0pCgogICAgICAgIGN1cnJlbnRJbmRleCsrCiAgICAgIH0KCiAgICAgIHJldHVybiB7IG9wdGlvbnMsIG5leHRJbmRleDogY3VycmVudEluZGV4IH0KICAgIH0sCgogICAgLy8g6Kej5p6Q6aKY55uu5YWD5L+h5oGv77yI562U5qGI44CB6Kej5p6Q44CB6Zq+5bqm77yJCiAgICBwYXJzZVF1ZXN0aW9uTWV0YShsaW5lcywgc3RhcnRJbmRleCwgcXVlc3Rpb24pIHsKICAgICAgZm9yIChsZXQgaSA9IHN0YXJ0SW5kZXg7IGkgPCBsaW5lcy5sZW5ndGg7IGkrKykgewogICAgICAgIGNvbnN0IGxpbmUgPSBsaW5lc1tpXQoKICAgICAgICAvLyDop6PmnpDnrZTmoYgKICAgICAgICBjb25zdCBhbnN3ZXJNYXRjaCA9IGxpbmUubWF0Y2goL17nrZTmoYhb77yaOl1ccyooLispLykKICAgICAgICBpZiAoYW5zd2VyTWF0Y2gpIHsKICAgICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLnBhcnNlQW5zd2VyKGFuc3dlck1hdGNoWzFdLCBxdWVzdGlvbi5xdWVzdGlvblR5cGUpCiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KCiAgICAgICAgLy8g6Kej5p6Q6Kej5p6QCiAgICAgICAgY29uc3QgZXhwbGFuYXRpb25NYXRjaCA9IGxpbmUubWF0Y2goL17op6PmnpBb77yaOl1ccyooLispLykKICAgICAgICBpZiAoZXhwbGFuYXRpb25NYXRjaCkgewogICAgICAgICAgcXVlc3Rpb24uZXhwbGFuYXRpb24gPSBleHBsYW5hdGlvbk1hdGNoWzFdLnRyaW0oKQogICAgICAgICAgY29udGludWUKICAgICAgICB9CgogICAgICAgIC8vIOino+aekOmavuW6pgogICAgICAgIGNvbnN0IGRpZmZpY3VsdHlNYXRjaCA9IGxpbmUubWF0Y2goL17pmr7luqZb77yaOl1ccyoo566A5Y2VfOS4reetiXzlm7Dpmr4pLykKICAgICAgICBpZiAoZGlmZmljdWx0eU1hdGNoKSB7CiAgICAgICAgICBxdWVzdGlvbi5kaWZmaWN1bHR5ID0gZGlmZmljdWx0eU1hdGNoWzFdCiAgICAgICAgICBjb250aW51ZQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aaC5p6c5rKh5pyJ5pi+5byP562U5qGI77yM5bCd6K+V5LuO6aKY5bmy5Lit5o+Q5Y+WCiAgICAgIGlmICghcXVlc3Rpb24uY29ycmVjdEFuc3dlcikgewogICAgICAgIHF1ZXN0aW9uLmNvcnJlY3RBbnN3ZXIgPSB0aGlzLmV4dHJhY3RBbnN3ZXJGcm9tQ29udGVudChxdWVzdGlvbi5xdWVzdGlvbkNvbnRlbnQsIHF1ZXN0aW9uLnF1ZXN0aW9uVHlwZSkKICAgICAgfQogICAgfSwKCiAgICAvLyDop6PmnpDnrZTmoYgKICAgIHBhcnNlQW5zd2VyKGFuc3dlclRleHQsIHF1ZXN0aW9uVHlwZSkgewogICAgICBpZiAocXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnKSB7CiAgICAgICAgLy8g5Yik5pat6aKY562U5qGI5aSE55CGCiAgICAgICAgaWYgKGFuc3dlclRleHQuaW5jbHVkZXMoJ+ato+ehricpIHx8IGFuc3dlclRleHQuaW5jbHVkZXMoJ+WvuScpIHx8IGFuc3dlclRleHQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygndHJ1ZScpKSB7CiAgICAgICAgICByZXR1cm4gJ3RydWUnCiAgICAgICAgfSBlbHNlIGlmIChhbnN3ZXJUZXh0LmluY2x1ZGVzKCfplJnor68nKSB8fCBhbnN3ZXJUZXh0LmluY2x1ZGVzKCfplJknKSB8fCBhbnN3ZXJUZXh0LmluY2x1ZGVzKCflgYcnKSB8fCBhbnN3ZXJUZXh0LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2ZhbHNlJykpIHsKICAgICAgICAgIHJldHVybiAnZmFsc2UnCiAgICAgICAgfQogICAgICAgIHJldHVybiBhbnN3ZXJUZXh0LnRyaW0oKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOmAieaLqemimOetlOahiOWkhOeQhgogICAgICAgIHJldHVybiBhbnN3ZXJUZXh0LnJlcGxhY2UoL1ss77yMXHNdL2csICcnKS50b1VwcGVyQ2FzZSgpCiAgICAgIH0KICAgIH0sCgogICAgLy8g5LuO6aKY5bmy5Lit5o+Q5Y+W562U5qGICiAgICBleHRyYWN0QW5zd2VyRnJvbUNvbnRlbnQoY29udGVudCwgcXVlc3Rpb25UeXBlKSB7CiAgICAgIC8vIOaUr+aMgeeahOaLrOWPt+exu+WeiwogICAgICBjb25zdCBicmFja2V0UGF0dGVybnMgPSBbCiAgICAgICAgL+OAkChbXuOAkV0rKeOAkS9nLAogICAgICAgIC9cWyhbXlxdXSspXF0vZywKICAgICAgICAv77yIKFte77yJXSsp77yJL2csCiAgICAgICAgL1woKFteKV0rKVwpL2cKICAgICAgXQoKICAgICAgZm9yIChjb25zdCBwYXR0ZXJuIG9mIGJyYWNrZXRQYXR0ZXJucykgewogICAgICAgIGNvbnN0IG1hdGNoZXMgPSBbLi4uY29udGVudC5tYXRjaEFsbChwYXR0ZXJuKV0KICAgICAgICBpZiAobWF0Y2hlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICBjb25zdCBhbnN3ZXIgPSBtYXRjaGVzW21hdGNoZXMubGVuZ3RoIC0gMV1bMV0gLy8g5Y+W5pyA5ZCO5LiA5Liq5Yy56YWNCiAgICAgICAgICByZXR1cm4gdGhpcy5wYXJzZUFuc3dlcihhbnN3ZXIsIHF1ZXN0aW9uVHlwZSkKICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiAnJwogICAgfSwKCiAgICAvLyDovazmjaLpopjlnosKICAgIGNvbnZlcnRRdWVzdGlvblR5cGUodHlwZVRleHQpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAn5Y2V6YCJ6aKYJzogJ3NpbmdsZScsCiAgICAgICAgJ+WkmumAiemimCc6ICdtdWx0aXBsZScsCiAgICAgICAgJ+WIpOaWremimCc6ICdqdWRnbWVudCcKICAgICAgfQogICAgICByZXR1cm4gdHlwZU1hcFt0eXBlVGV4dF0gfHwgJ3NpbmdsZScKICAgIH0sCgogICAgLy8g5a+85YWl5pWw5o2uCiAgICBpbXBvcnREYXRhKCkgewogICAgICB0aGlzLmltcG9ydGluZyA9IHRydWUKICAgICAgY29uc3QgaW1wb3J0RGF0YSA9IHsKICAgICAgICBiYW5rSWQ6IHRoaXMuYmFua0lkLAogICAgICAgIHF1ZXN0aW9uczogdGhpcy5wYXJzZWREYXRhCiAgICAgIH0KICAgICAgYmF0Y2hJbXBvcnRRdWVzdGlvbnMoaW1wb3J0RGF0YSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICAgIHRoaXMuaW1wb3J0UmVzdWx0ID0gcmVzcG9uc2UuZGF0YQogICAgICAgIHRoaXMubmV4dFN0ZXAoKQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy5pbXBvcnRpbmcgPSBmYWxzZQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WvvOWFpeaVsOaNruWksei0pScsIGVycm9yKQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeaVsOaNruWksei0pScpCiAgICAgIH0pCiAgICB9LAogICAgLy8g5a6M5oiQ5a+85YWlCiAgICBoYW5kbGVDb21wbGV0ZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnc3VjY2VzcycpCiAgICAgIHRoaXMuaGFuZGxlQ2xvc2UoKQogICAgfSwKICAgIC8vIOmHjee9ruWvvOWFpQogICAgcmVzZXRJbXBvcnQoKSB7CiAgICAgIHRoaXMuY3VycmVudFN0ZXAgPSAwCiAgICAgIHRoaXMuaW1wb3J0TW9kZSA9IHRoaXMuZGVmYXVsdE1vZGUgfHwgJ2V4Y2VsJwogICAgICB0aGlzLnVwbG9hZGVkRmlsZSA9IG51bGwKICAgICAgdGhpcy5wYXJzZWREYXRhID0gW10KICAgICAgdGhpcy5wYXJzZUVycm9ycyA9IFtdCiAgICAgIHRoaXMuaW1wb3J0UmVzdWx0ID0gewogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwKICAgICAgICBmYWlsQ291bnQ6IDAsCiAgICAgICAgZXJyb3JzOiBbXQogICAgICB9CiAgICB9LAogICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["BatchImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "BatchImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量导入题目\"\n    :visible.sync=\"dialogVisible\"\n    width=\"70%\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"import-container\">\n      <!-- 导入步骤 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"下载模板\"></el-step>\n        <el-step title=\"上传文件\"></el-step>\n        <el-step title=\"数据预览\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 选择导入方式 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"import-mode-section\">\n          <h3>第一步：选择导入方式</h3>\n\n          <!-- 导入方式选择 -->\n          <el-radio-group v-model=\"importMode\" style=\"margin-bottom: 30px;\">\n            <el-radio label=\"excel\">Excel模板导入</el-radio>\n            <el-radio label=\"document\">文档内容导入</el-radio>\n          </el-radio-group>\n\n          <!-- Excel模板导入 -->\n          <div v-if=\"importMode === 'excel'\" class=\"template-section\">\n            <h4>Excel模板导入</h4>\n            <p>请先下载题目导入模板，按照模板格式填写题目数据</p>\n\n            <div class=\"template-buttons\">\n              <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate('all')\">\n                下载题目导入模板\n              </el-button>\n            </div>\n\n            <div class=\"template-tips\">\n              <h4>填写说明：</h4>\n              <ul>\n                <li>模板支持单选题、多选题、判断题混合导入</li>\n                <li>题目内容：必填，支持HTML格式</li>\n                <li>选项内容：选择题必填，选项A-H，每个选项单独一列</li>\n                <li>正确答案：必填，单选题填写A/B/C等，多选题用逗号分隔如A,C，判断题填写true/false</li>\n                <li>难度系数：选填，可填写\"简单\"、\"中等\"、\"困难\"</li>\n                <li>题目解析：选填，支持HTML格式</li>\n                <li>请确保Excel文件编码为UTF-8，避免中文乱码</li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- 文档内容导入 -->\n          <div v-if=\"importMode === 'document'\" class=\"document-section\">\n            <h4>文档内容导入</h4>\n            <p>支持上传包含题目内容的文档文件，系统将自动解析题目信息</p>\n\n            <div class=\"document-format-tips\">\n              <h4>格式要求：</h4>\n              <div class=\"format-rules\">\n                <div class=\"rule-item\">\n                  <h5>题型标注（必填）：</h5>\n                  <p><code>[单选题]</code> <code>[多选题]</code> <code>[判断题]</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>题号规则（必填）：</h5>\n                  <p>题目前必须有题号，如：<code>1.</code> <code>2：</code> <code>3．</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>选项格式（必填）：</h5>\n                  <p><code>A.选项内容</code> <code>B：选项内容</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>答案标注（必填）：</h5>\n                  <p><code>答案：A</code> 或题干内 <code>【A】</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>解析和难度（可选）：</h5>\n                  <p><code>解析：解析内容</code> <code>难度：中等</code></p>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"document-example\">\n              <h4>示例格式：</h4>\n              <pre class=\"example-text\">\n[单选题]\n1.（ ）是我国最早的诗歌总集。\nA.《左传》\nB.《离骚》\nC.《坛经》\nD.《诗经》\n答案：D\n解析：诗经是我国最早的诗歌总集。\n难度：中等\n\n[判断题]\n2.元杂剧的四大悲剧包括郑光祖的《赵氏孤儿》。\n答案：错误\n解析：《赵氏孤儿》实为纪君祥所作。\n              </pre>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 上传文件 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>第二步：上传题目文件</h3>\n          <p v-if=\"importMode === 'excel'\">请选择填写好的Excel文件进行上传</p>\n          <p v-if=\"importMode === 'document'\">请选择包含题目内容的文档文件进行上传</p>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\"importMode === 'excel' ? '.xlsx,.xls' : '.txt,.doc,.docx'\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将文件拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'excel'\">\n                  支持 .xlsx、.xls 格式文件，文件大小不超过10MB\n                </p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'document'\">\n                  支持 .txt、.doc、.docx 格式文件，文件大小不超过10MB\n                </p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" @click=\"parseFile\" :disabled=\"!uploadedFile\" :loading=\"parsing\">\n            解析文件\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 数据预览 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"preview-section\">\n          <h3>第三步：数据预览与确认</h3>\n          <p>共解析到 {{ parsedData.length }} 道题目，请确认数据无误后点击导入</p>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <el-alert\n              title=\"数据解析错误\"\n              type=\"error\"\n              :closable=\"false\"\n              show-icon\n              style=\"margin-bottom: 15px;\"\n            />\n            <div class=\"error-list\">\n              <div v-for=\"(error, index) in parseErrors\" :key=\"index\" class=\"error-item\">\n                第{{ error.row }}行：{{ error.message }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-table\">\n            <el-table :data=\"parsedData.slice(0, 10)\" border style=\"width: 100%\">\n              <el-table-column prop=\"questionType\" label=\"题型\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getQuestionTypeColor(scope.row.questionType)\" size=\"mini\">\n                    {{ getQuestionTypeName(scope.row.questionType) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题目内容\" min-width=\"200\" show-overflow-tooltip />\n              <el-table-column prop=\"correctAnswer\" label=\"正确答案\" width=\"100\" />\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"80\" />\n            </el-table>\n            <div v-if=\"parsedData.length > 10\" class=\"table-tip\">\n              仅显示前10条数据，共{{ parsedData.length }}条\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"importData\" \n            :disabled=\"parseErrors.length > 0 || parsedData.length === 0\"\n            :loading=\"importing\"\n          >\n            确认导入\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 60px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          <div class=\"result-stats\">\n            <p>成功导入 <span class=\"success-count\">{{ importResult.successCount }}</span> 道题目</p>\n            <p v-if=\"importResult.failCount > 0\">\n              失败 <span class=\"fail-count\">{{ importResult.failCount }}</span> 道题目\n            </p>\n          </div>\n          \n          <div v-if=\"importResult.errors.length > 0\" class=\"import-errors\">\n            <el-collapse>\n              <el-collapse-item title=\"查看失败详情\" name=\"errors\">\n                <div v-for=\"(error, index) in importResult.errors\" :key=\"index\" class=\"error-detail\">\n                  第{{ error.row }}行：{{ error.message }}\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { downloadTemplate, parseImportFile, batchImportQuestions } from '@/api/biz/question'\nimport { download } from '@/utils/request'\n\nexport default {\n  name: \"BatchImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    defaultMode: {\n      type: String,\n      default: 'excel' // excel 或 document\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      importMode: 'excel', // 导入模式：excel 或 document\n      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      },\n      uploadedFile: null,\n      parsing: false,\n      importing: false,\n      parsedData: [],\n      parseErrors: [],\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    defaultMode(val) {\n      if (val) {\n        this.importMode = val\n      }\n    }\n  },\n  methods: {\n    // 下载模板\n    downloadTemplate(type) {\n      const fileName = type === 'all' ? '题目导入模板.xlsx' : `${this.getQuestionTypeName(type)}导入模板.xlsx`\n      download('/biz/question/downloadTemplate', { questionType: type }, fileName)\n    },\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 下一步\n    nextStep() {\n      this.currentStep++\n    },\n    // 上一步\n    prevStep() {\n      this.currentStep--\n    },\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (this.importMode === 'excel') {\n        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                       file.type === 'application/vnd.ms-excel'\n        if (!isExcel) {\n          this.$message.error('只能上传Excel文件!')\n          return false\n        }\n      } else if (this.importMode === 'document') {\n        const isDocument = file.type === 'text/plain' ||\n                          file.type === 'application/msword' ||\n                          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                          file.name.toLowerCase().endsWith('.txt') ||\n                          file.name.toLowerCase().endsWith('.doc') ||\n                          file.name.toLowerCase().endsWith('.docx')\n        if (!isDocument) {\n          this.$message.error('只能上传文档文件(.txt, .doc, .docx)!')\n          return false\n        }\n      }\n\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n      return true\n    },\n    // 文件上传成功\n    handleFileSuccess(response, file) {\n      if (response.code === 200) {\n        this.uploadedFile = {\n          name: file.name,\n          url: response.url,\n          fileName: response.fileName\n        }\n        this.$message.success('文件上传成功')\n      } else {\n        this.$message.error(response.msg || '文件上传失败')\n      }\n    },\n    // 文件上传失败\n    handleFileError() {\n      this.$message.error('文件上传失败')\n    },\n    // 解析文件\n    parseFile() {\n      this.parsing = true\n\n      if (this.importMode === 'excel') {\n        // Excel文件解析\n        const parseData = {\n          fileName: this.uploadedFile.fileName,\n          bankId: this.bankId\n        }\n        parseImportFile(parseData).then(response => {\n          this.parsing = false\n          this.parsedData = response.data.questions || []\n          this.parseErrors = response.data.errors || []\n          this.nextStep()\n        }).catch(error => {\n          this.parsing = false\n          console.error('解析文件失败', error)\n          this.$message.error('解析文件失败')\n        })\n      } else if (this.importMode === 'document') {\n        // 文档内容解析\n        this.parseDocumentContent()\n      }\n    },\n\n    // 解析文档内容\n    async parseDocumentContent() {\n      try {\n        // 读取文件内容\n        const fileContent = await this.readFileContent(this.uploadedFile.url)\n\n        // 解析题目\n        const parseResult = this.parseQuestionContent(fileContent)\n\n        this.parsing = false\n        this.parsedData = parseResult.questions\n        this.parseErrors = parseResult.errors\n        this.nextStep()\n      } catch (error) {\n        this.parsing = false\n        console.error('解析文档内容失败', error)\n        this.$message.error('解析文档内容失败')\n      }\n    },\n\n    // 读取文件内容\n    readFileContent(fileUrl) {\n      return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest()\n        xhr.open('GET', process.env.VUE_APP_BASE_API + fileUrl, true)\n        xhr.setRequestHeader('Authorization', 'Bearer ' + getToken())\n        xhr.responseType = 'text'\n\n        xhr.onload = function() {\n          if (xhr.status === 200) {\n            resolve(xhr.responseText)\n          } else {\n            reject(new Error('读取文件失败'))\n          }\n        }\n\n        xhr.onerror = function() {\n          reject(new Error('读取文件失败'))\n        }\n\n        xhr.send()\n      })\n    },\n\n    // 解析题目内容\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      try {\n        // 按题型分割内容\n        const sections = this.splitByQuestionType(content)\n\n        sections.forEach((section, sectionIndex) => {\n          try {\n            const parsedQuestions = this.parseSectionQuestions(section)\n            questions.push(...parsedQuestions)\n          } catch (error) {\n            errors.push(`第${sectionIndex + 1}个题型区域解析失败: ${error.message}`)\n          }\n        })\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType, questionIndex) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题号和题干\n      const firstLine = lines[0]\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．]\\s*(.*)/)\n      if (!numberMatch) {\n        throw new Error('题号格式不正确')\n      }\n\n      let questionContent = numberMatch[2]\n      let currentLineIndex = 1\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '中等',\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难)/)\n        if (difficultyMatch) {\n          question.difficulty = difficultyMatch[1]\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 解析答案\n    parseAnswer(answerText, questionType) {\n      if (questionType === 'judgment') {\n        // 判断题答案处理\n        if (answerText.includes('正确') || answerText.includes('对') || answerText.toLowerCase().includes('true')) {\n          return 'true'\n        } else if (answerText.includes('错误') || answerText.includes('错') || answerText.includes('假') || answerText.toLowerCase().includes('false')) {\n          return 'false'\n        }\n        return answerText.trim()\n      } else {\n        // 选择题答案处理\n        return answerText.replace(/[,，\\s]/g, '').toUpperCase()\n      }\n    },\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 导入数据\n    importData() {\n      this.importing = true\n      const importData = {\n        bankId: this.bankId,\n        questions: this.parsedData\n      }\n      batchImportQuestions(importData).then(response => {\n        this.importing = false\n        this.importResult = response.data\n        this.nextStep()\n      }).catch(error => {\n        this.importing = false\n        console.error('导入数据失败', error)\n        this.$message.error('导入数据失败')\n      })\n    },\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.importMode = this.defaultMode || 'excel'\n      this.uploadedFile = null\n      this.parsedData = []\n      this.parseErrors = []\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.import-container {\n  padding: 20px 0;\n}\n\n.step-content {\n  min-height: 400px;\n}\n\n.template-section h3,\n.upload-section h3,\n.preview-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-buttons {\n  margin: 20px 0;\n  display: flex;\n  gap: 15px;\n}\n\n.template-tips {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.template-tips h4 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.template-tips li {\n  margin-bottom: 5px;\n  color: #666;\n}\n\n/* 文档导入样式 */\n.import-mode-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.document-section {\n  margin-top: 20px;\n}\n\n.document-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.document-format-tips {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n}\n\n.document-format-tips h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.format-rules {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.rule-item {\n  background: #fff;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 3px solid #409eff;\n}\n\n.rule-item h5 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.rule-item p {\n  margin: 0;\n  color: #666;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.rule-item code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  color: #e74c3c;\n}\n\n.document-example {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.document-example h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.example-text {\n  background: #fff;\n  padding: 15px;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  background: #fafafa;\n  transition: border-color 0.3s;\n}\n\n.upload-area:hover {\n  border-color: #409EFF;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 0;\n  color: #666;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.uploaded-file {\n  margin-top: 15px;\n}\n\n.error-section {\n  margin-bottom: 20px;\n}\n\n.error-list {\n  max-height: 150px;\n  overflow-y: auto;\n  background: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.error-item {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.table-tip {\n  text-align: center;\n  color: #999;\n  font-size: 12px;\n  margin-top: 10px;\n}\n\n.result-section {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-stats {\n  margin: 20px 0;\n}\n\n.success-count {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.fail-count {\n  color: #f56c6c;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.import-errors {\n  margin-top: 20px;\n  text-align: left;\n}\n\n.error-detail {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.step-actions {\n  text-align: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style>\n"]}]}