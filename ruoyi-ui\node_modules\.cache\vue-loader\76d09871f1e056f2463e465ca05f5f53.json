{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue?vue&type=style&index=0&id=358c5cf8&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OptionEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "OptionEditor.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <div class=\"option-editor\">\n    <!-- 隐藏的上传组件 -->\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n    >\n    </el-upload>\n    <div\n      class=\"editor-container\"\n      :class=\"{ 'focused': isFocused, 'show-toolbar': showToolbar }\"\n      @click=\"handleEditorClick\"\n    >\n      <div ref=\"editor\" class=\"editor\" :style=\"styles\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport Quill from \"quill\"\nimport \"quill/dist/quill.core.css\"\nimport \"quill/dist/quill.snow.css\"\nimport { getToken } from \"@/utils/auth\"\n\n\n// 全局状态管理当前激活的编辑器\nlet currentActiveEditor = null\n\nexport default {\n  name: \"OptionEditor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: 80,\n    },\n    /* 占位符 */\n    placeholder: {\n      type: String,\n      default: \"请输入内容\",\n    },\n    /* 编辑器唯一标识 */\n    editorId: {\n      type: String,\n      default: () => 'editor_' + Math.random().toString(36).substr(2, 9)\n    }\n  },\n  data() {\n    return {\n      Quill: null,\n      currentValue: \"\",\n      isFocused: false,\n      showToolbar: false,\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置（移除视频功能）\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ align: [] }],                                 // 对齐方式\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],  // 字体大小\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [\"link\", \"image\"],                               // 链接、图片\n            [\"clean\"]                                        // 清除文本格式\n          ],\n        },\n        placeholder: this.placeholder,\n        readOnly: false,\n      },\n    }\n  },\n  computed: {\n    styles() {\n      let style = {}\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`\n      }\n      return style\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init()\n    // 添加全局点击事件监听\n    document.addEventListener('click', this.handleGlobalClick)\n  },\n  beforeDestroy() {\n    // 如果当前编辑器是激活状态，清除全局状态\n    if (currentActiveEditor === this) {\n      currentActiveEditor = null\n    }\n    this.Quill = null\n    // 移除全局点击事件监听\n    document.removeEventListener('click', this.handleGlobalClick)\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor\n      this.Quill = new Quill(editor, this.options)\n\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n\n      // 初始化时隐藏工具栏\n      this.hideToolbar()\n\n      // 图片上传处理\n      const toolbar = this.Quill.getModule(\"toolbar\")\n      toolbar.addHandler(\"image\", this.imgHandler)\n\n      // 监听内容变化\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML\n        const text = this.Quill.getText()\n        const quill = this.Quill\n        this.currentValue = html\n        this.$emit(\"input\", html)\n        this.$emit(\"on-change\", { html, text, quill })\n      })\n\n      // 监听焦点事件\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        if (range) {\n          this.isFocused = true\n        } else {\n          this.isFocused = false\n        }\n        this.$emit(\"on-selection-change\", range, oldRange, source)\n      })\n    },\n    handleEditorClick() {\n      this.setActiveEditor()\n      if (this.Quill) {\n        this.Quill.focus()\n      }\n    },\n    // 设置当前编辑器为激活状态\n    setActiveEditor() {\n      // 隐藏其他编辑器的工具栏\n      if (currentActiveEditor && currentActiveEditor !== this) {\n        currentActiveEditor.showToolbar = false\n        currentActiveEditor.hideToolbar()\n      }\n      // 设置当前编辑器为激活状态\n      currentActiveEditor = this\n      this.showToolbar = true\n      this.showToolbarElement()\n    },\n    // 处理全局点击事件\n    handleGlobalClick(event) {\n      // 检查点击是否在当前编辑器内\n      if (!this.$el.contains(event.target)) {\n        // 如果当前编辑器显示工具栏，则隐藏\n        if (this.showToolbar) {\n          this.showToolbar = false\n          this.hideToolbar()\n          if (currentActiveEditor === this) {\n            currentActiveEditor = null\n          }\n        }\n      }\n    },\n    // 显示工具栏\n    showToolbarElement() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'block'\n        }\n      })\n    },\n    // 隐藏工具栏\n    hideToolbar() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'none'\n        }\n      })\n    },\n    // 图片上传处理\n    imgHandler() {\n      this.$refs.upload.$children[0].$refs.input.click()\n    },\n\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件大小\n      if (file.size / 1024 / 1024 > 5) {\n        this.$message.error(\"上传文件大小不能超过 5MB!\")\n        return false\n      }\n      return true\n    },\n\n    // 上传成功处理\n    handleUploadSuccess(res, file) {\n      if (res.code == 200) {\n        // 获取光标所在位置\n        let length = this.Quill.getSelection().index\n        // 插入图片\n        this.Quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName)\n        // 调整光标到最后\n        this.Quill.setSelection(length + 1)\n      } else {\n        this.$message.error(\"图片插入失败\")\n      }\n    },\n\n    // 上传失败处理\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\")\n    }\n  }\n}\n</script>\n\n<style>\n.option-editor {\n  position: relative;\n}\n\n.editor-container {\n  position: relative;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.editor-container:hover {\n  border-color: #c0c4cc;\n}\n\n.editor-container.focused {\n  border-color: #409EFF;\n}\n\n.editor-container.show-toolbar {\n  border-color: #409EFF;\n}\n\n.editor {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n\n/* 工具栏样式 */\n.option-editor .ql-toolbar {\n  border: 1px solid #e4e7ed;\n  border-top: none;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.option-editor .ql-container {\n  border: none;\n  font-size: 14px;\n}\n\n.option-editor .ql-editor {\n  padding: 12px;\n  min-height: inherit;\n  line-height: 1.5;\n}\n\n.option-editor .ql-editor.ql-blank::before {\n  font-style: normal;\n  color: #c0c4cc;\n  left: 12px;\n}\n\n/* 当显示工具栏时，调整编辑器容器的圆角 */\n.editor-container.show-toolbar .ql-container {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n/* 工具栏按钮样式优化 */\n.option-editor .ql-toolbar .ql-formats {\n  margin-right: 15px;\n}\n\n.option-editor .ql-toolbar .ql-formats:last-child {\n  margin-right: 0;\n}\n\n/* 工具栏过渡动画 */\n.option-editor .ql-toolbar {\n  transition: all 0.3s ease;\n}\n</style>\n"]}]}