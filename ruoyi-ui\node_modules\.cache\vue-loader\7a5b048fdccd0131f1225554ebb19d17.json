{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\OptionEditor.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["OptionEditor.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "OptionEditor.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <div class=\"option-editor\">\n    <!-- 隐藏的上传组件 -->\n    <el-upload\n      :action=\"uploadUrl\"\n      :before-upload=\"handleBeforeUpload\"\n      :on-success=\"handleUploadSuccess\"\n      :on-error=\"handleUploadError\"\n      name=\"file\"\n      :show-file-list=\"false\"\n      :headers=\"headers\"\n      style=\"display: none\"\n      ref=\"upload\"\n    >\n    </el-upload>\n    <div\n      class=\"editor-container\"\n      :class=\"{ 'focused': isFocused, 'show-toolbar': showToolbar }\"\n      @click=\"handleEditorClick\"\n    >\n      <div ref=\"editor\" class=\"editor\" :style=\"styles\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport Quill from \"quill\"\nimport \"quill/dist/quill.core.css\"\nimport \"quill/dist/quill.snow.css\"\nimport { getToken } from \"@/utils/auth\"\n\n\n// 全局状态管理当前激活的编辑器\nlet currentActiveEditor = null\n\nexport default {\n  name: \"OptionEditor\",\n  props: {\n    /* 编辑器的内容 */\n    value: {\n      type: String,\n      default: \"\",\n    },\n    /* 最小高度 */\n    minHeight: {\n      type: Number,\n      default: 80,\n    },\n    /* 占位符 */\n    placeholder: {\n      type: String,\n      default: \"请输入内容\",\n    },\n    /* 编辑器唯一标识 */\n    editorId: {\n      type: String,\n      default: () => 'editor_' + Math.random().toString(36).substr(2, 9)\n    }\n  },\n  data() {\n    return {\n      Quill: null,\n      currentValue: \"\",\n      isFocused: false,\n      showToolbar: false,\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/common/upload\",\n      headers: {\n        Authorization: \"Bearer \" + getToken()\n      },\n      options: {\n        theme: \"snow\",\n        bounds: document.body,\n        debug: \"warn\",\n        modules: {\n          // 工具栏配置（移除视频功能）\n          toolbar: [\n            [\"bold\", \"italic\", \"underline\", \"strike\"],       // 加粗 斜体 下划线 删除线\n            [\"blockquote\", \"code-block\"],                    // 引用  代码块\n            [{ list: \"ordered\" }, { list: \"bullet\" }],       // 有序、无序列表\n            [{ indent: \"-1\" }, { indent: \"+1\" }],            // 缩进\n            [{ align: [] }],                                 // 对齐方式\n            [{ size: [\"small\", false, \"large\", \"huge\"] }],  // 字体大小\n            [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色\n            [\"link\", \"image\"],                               // 链接、图片\n            [\"clean\"]                                        // 清除文本格式\n          ],\n        },\n        placeholder: this.placeholder,\n        readOnly: false,\n      },\n    }\n  },\n  computed: {\n    styles() {\n      let style = {}\n      if (this.minHeight) {\n        style.minHeight = `${this.minHeight}px`\n      }\n      return style\n    }\n  },\n  watch: {\n    value: {\n      handler(val) {\n        if (val !== this.currentValue) {\n          this.currentValue = val === null ? \"\" : val\n          if (this.Quill) {\n            this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n          }\n        }\n      },\n      immediate: true,\n    },\n  },\n  mounted() {\n    this.init()\n    // 添加全局点击事件监听\n    document.addEventListener('click', this.handleGlobalClick)\n  },\n  beforeDestroy() {\n    // 如果当前编辑器是激活状态，清除全局状态\n    if (currentActiveEditor === this) {\n      currentActiveEditor = null\n    }\n    this.Quill = null\n    // 移除全局点击事件监听\n    document.removeEventListener('click', this.handleGlobalClick)\n  },\n  methods: {\n    init() {\n      const editor = this.$refs.editor\n      this.Quill = new Quill(editor, this.options)\n\n      this.Quill.clipboard.dangerouslyPasteHTML(this.currentValue)\n\n      // 初始化时隐藏工具栏\n      this.hideToolbar()\n\n      // 图片上传处理\n      const toolbar = this.Quill.getModule(\"toolbar\")\n      toolbar.addHandler(\"image\", this.imgHandler)\n\n      // 监听内容变化\n      this.Quill.on(\"text-change\", (delta, oldDelta, source) => {\n        const html = this.$refs.editor.children[0].innerHTML\n        const text = this.Quill.getText()\n        const quill = this.Quill\n        this.currentValue = html\n        this.$emit(\"input\", html)\n        this.$emit(\"on-change\", { html, text, quill })\n      })\n\n      // 监听焦点事件\n      this.Quill.on(\"selection-change\", (range, oldRange, source) => {\n        if (range) {\n          this.isFocused = true\n        } else {\n          this.isFocused = false\n        }\n        this.$emit(\"on-selection-change\", range, oldRange, source)\n      })\n    },\n    handleEditorClick() {\n      this.setActiveEditor()\n      if (this.Quill) {\n        this.Quill.focus()\n      }\n    },\n    // 设置当前编辑器为激活状态\n    setActiveEditor() {\n      // 隐藏其他编辑器的工具栏\n      if (currentActiveEditor && currentActiveEditor !== this) {\n        currentActiveEditor.showToolbar = false\n        currentActiveEditor.hideToolbar()\n      }\n      // 设置当前编辑器为激活状态\n      currentActiveEditor = this\n      this.showToolbar = true\n      this.showToolbarElement()\n    },\n    // 处理全局点击事件\n    handleGlobalClick(event) {\n      // 检查点击是否在当前编辑器内\n      if (!this.$el.contains(event.target)) {\n        // 如果当前编辑器显示工具栏，则隐藏\n        if (this.showToolbar) {\n          this.showToolbar = false\n          this.hideToolbar()\n          if (currentActiveEditor === this) {\n            currentActiveEditor = null\n          }\n        }\n      }\n    },\n    // 显示工具栏\n    showToolbarElement() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'block'\n        }\n      })\n    },\n    // 隐藏工具栏\n    hideToolbar() {\n      this.$nextTick(() => {\n        const toolbar = this.$el.querySelector('.ql-toolbar')\n        if (toolbar) {\n          toolbar.style.display = 'none'\n        }\n      })\n    },\n    // 图片上传处理\n    imgHandler() {\n      this.$refs.upload.$children[0].$refs.input.click()\n    },\n\n    // 上传前校检格式和大小\n    handleBeforeUpload(file) {\n      // 校检文件大小\n      if (file.size / 1024 / 1024 > 5) {\n        this.$message.error(\"上传文件大小不能超过 5MB!\")\n        return false\n      }\n      return true\n    },\n\n    // 上传成功处理\n    handleUploadSuccess(res, file) {\n      if (res.code == 200) {\n        // 获取光标所在位置\n        let length = this.Quill.getSelection().index\n        // 插入图片\n        this.Quill.insertEmbed(length, \"image\", process.env.VUE_APP_BASE_API + res.fileName)\n        // 调整光标到最后\n        this.Quill.setSelection(length + 1)\n      } else {\n        this.$message.error(\"图片插入失败\")\n      }\n    },\n\n    // 上传失败处理\n    handleUploadError() {\n      this.$message.error(\"图片插入失败\")\n    }\n  }\n}\n</script>\n\n<style>\n.option-editor {\n  position: relative;\n}\n\n.editor-container {\n  position: relative;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.editor-container:hover {\n  border-color: #c0c4cc;\n}\n\n.editor-container.focused {\n  border-color: #409EFF;\n}\n\n.editor-container.show-toolbar {\n  border-color: #409EFF;\n}\n\n.editor {\n  white-space: pre-wrap !important;\n  line-height: normal !important;\n}\n\n/* 工具栏样式 */\n.option-editor .ql-toolbar {\n  border: 1px solid #e4e7ed;\n  border-top: none;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.option-editor .ql-container {\n  border: none;\n  font-size: 14px;\n}\n\n.option-editor .ql-editor {\n  padding: 12px;\n  min-height: inherit;\n  line-height: 1.5;\n}\n\n.option-editor .ql-editor.ql-blank::before {\n  font-style: normal;\n  color: #c0c4cc;\n  left: 12px;\n}\n\n/* 当显示工具栏时，调整编辑器容器的圆角 */\n.editor-container.show-toolbar .ql-container {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n/* 工具栏按钮样式优化 */\n.option-editor .ql-toolbar .ql-formats {\n  margin-right: 15px;\n}\n\n.option-editor .ql-toolbar .ql-formats:last-child {\n  margin-right: 0;\n}\n\n/* 工具栏过渡动画 */\n.option-editor .ql-toolbar {\n  transition: all 0.3s ease;\n}\n</style>\n"]}]}