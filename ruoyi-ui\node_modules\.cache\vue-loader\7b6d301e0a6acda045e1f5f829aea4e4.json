{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue?vue&type=template&id=4755e558&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}