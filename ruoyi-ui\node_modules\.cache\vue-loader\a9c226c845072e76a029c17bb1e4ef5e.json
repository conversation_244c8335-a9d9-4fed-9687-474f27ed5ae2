{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionForm.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGFkZFF1ZXN0aW9uLCB1cGRhdGVRdWVzdGlvbiB9IGZyb20gJ0AvYXBpL2Jpei9xdWVzdGlvbicKaW1wb3J0IE9wdGlvbkVkaXRvciBmcm9tICcuL09wdGlvbkVkaXRvcicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUXVlc3Rpb25Gb3JtIiwKICBjb21wb25lbnRzOiB7CiAgICBPcHRpb25FZGl0b3IKICB9LAogIHByb3BzOiB7CiAgICB2aXNpYmxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgcXVlc3Rpb25UeXBlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ3NpbmdsZScKICAgIH0sCiAgICBxdWVzdGlvbkRhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBudWxsCiAgICB9LAogICAgYmFua0lkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsIE51bWJlcl0sCiAgICAgIHJlcXVpcmVkOiB0cnVlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIHN1Ym1pdHRpbmc6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgcXVlc3Rpb25JZDogbnVsbCwKICAgICAgICBiYW5rSWQ6IG51bGwsCiAgICAgICAgcXVlc3Rpb25UeXBlOiAnc2luZ2xlJywKICAgICAgICBxdWVzdGlvbkNvbnRlbnQ6ICcnLAogICAgICAgIGRpZmZpY3VsdHk6ICfkuK3nrYknLAogICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgIGNvcnJlY3RBbnN3ZXI6ICcnLAogICAgICAgIGV4cGxhbmF0aW9uOiAnJwogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIHF1ZXN0aW9uVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqemimOWeiycsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemimOebruWGheWuuScsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBkaWZmaWN1bHR5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6Zq+5bqm57O75pWwJywgdHJpZ2dlcjogJ2NoYW5nZScgfQogICAgICAgIF0sCiAgICAgICAgY29ycmVjdEFuc3dlcjogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+iuvue9ruato+ehruetlOahiCcsIHRyaWdnZXI6ICdjaGFuZ2UnIH0KICAgICAgICBdCiAgICAgIH0KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBkaWFsb2dUaXRsZSgpIHsKICAgICAgY29uc3QgdHlwZU1hcCA9IHsKICAgICAgICAnc2luZ2xlJzogJ+WNlemAiemimCcsCiAgICAgICAgJ211bHRpcGxlJzogJ+WkmumAiemimCcsCiAgICAgICAgJ2p1ZGdtZW50JzogJ+WIpOaWremimCcKICAgICAgfQogICAgICBjb25zdCB0eXBlTmFtZSA9IHR5cGVNYXBbdGhpcy5mb3JtLnF1ZXN0aW9uVHlwZV0gfHwgJ+mimOebricKICAgICAgcmV0dXJuIHRoaXMuaXNFZGl0ID8gYOe8lui+kSR7dHlwZU5hbWV9YCA6IGDliJvlu7oke3R5cGVOYW1lfWAKICAgIH0sCiAgICBpc0VkaXQoKSB7CiAgICAgIHJldHVybiB0aGlzLnF1ZXN0aW9uRGF0YSAmJiB0aGlzLnF1ZXN0aW9uRGF0YS5xdWVzdGlvbklkCiAgICB9LAogICAgaXNDaG9pY2VRdWVzdGlvbigpIHsKICAgICAgcmV0dXJuIFsnc2luZ2xlJywgJ211bHRpcGxlJ10uaW5jbHVkZXModGhpcy5mb3JtLnF1ZXN0aW9uVHlwZSkKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICB2aXNpYmxlKHZhbCkgewogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB2YWwKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMuaW5pdEZvcm0oKQogICAgICB9CiAgICB9LAogICAgZGlhbG9nVmlzaWJsZSh2YWwpIHsKICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCB2YWwpCiAgICB9LAogICAgJ2Zvcm0ucXVlc3Rpb25UeXBlJyhuZXdUeXBlKSB7CiAgICAgIHRoaXMuaGFuZGxlUXVlc3Rpb25UeXBlQ2hhbmdlKG5ld1R5cGUpCiAgICB9LAogICAgcXVlc3Rpb25EYXRhOiB7CiAgICAgIGhhbmRsZXIobmV3RGF0YSkgewogICAgICAgIGlmIChuZXdEYXRhICYmIHRoaXMuZGlhbG9nVmlzaWJsZSkgewogICAgICAgICAgdGhpcy5pbml0Rm9ybSgpCiAgICAgICAgfQogICAgICB9LAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIneWni+WMluihqOWNlQogICAgaW5pdEZvcm0oKSB7CiAgICAgIGlmICh0aGlzLnF1ZXN0aW9uRGF0YSkgewogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuY29udmVydFF1ZXN0aW9uRGF0YUZvckZvcm0odGhpcy5xdWVzdGlvbkRhdGEpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5yZXNldEZvcm0oKQogICAgICAgIHRoaXMuZm9ybS5xdWVzdGlvblR5cGUgPSB0aGlzLnF1ZXN0aW9uVHlwZQogICAgICAgIHRoaXMuZm9ybS5iYW5rSWQgPSB0aGlzLmJhbmtJZAogICAgICB9CiAgICAgIHRoaXMuaGFuZGxlUXVlc3Rpb25UeXBlQ2hhbmdlKHRoaXMuZm9ybS5xdWVzdGlvblR5cGUpCiAgICB9LAoKICAgIC8vIOi9rOaNouWQjuerr+aVsOaNruS4uuihqOWNleagvOW8jwogICAgY29udmVydFF1ZXN0aW9uRGF0YUZvckZvcm0ocXVlc3Rpb25EYXRhKSB7CiAgICAgIGNvbnN0IGZvcm1EYXRhID0gewogICAgICAgIHF1ZXN0aW9uSWQ6IHF1ZXN0aW9uRGF0YS5xdWVzdGlvbklkLAogICAgICAgIGJhbmtJZDogcXVlc3Rpb25EYXRhLmJhbmtJZCwKICAgICAgICBxdWVzdGlvblR5cGU6IHRoaXMuY29udmVydFF1ZXN0aW9uVHlwZVRvU3RyaW5nKHF1ZXN0aW9uRGF0YS5xdWVzdGlvblR5cGUpLAogICAgICAgIGRpZmZpY3VsdHk6IHRoaXMuY29udmVydERpZmZpY3VsdHlUb1N0cmluZyhxdWVzdGlvbkRhdGEuZGlmZmljdWx0eSksCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiBxdWVzdGlvbkRhdGEucXVlc3Rpb25Db250ZW50LAogICAgICAgIGV4cGxhbmF0aW9uOiBxdWVzdGlvbkRhdGEuZXhwbGFuYXRpb24gfHwgcXVlc3Rpb25EYXRhLmFuYWx5c2lzIHx8ICcnLAogICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgIGNvcnJlY3RBbnN3ZXI6ICcnCiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhumAieaLqemimOmAiemhuQogICAgICBpZiAocXVlc3Rpb25EYXRhLm9wdGlvbnMpIHsKICAgICAgICBsZXQgb3B0aW9ucyA9IHF1ZXN0aW9uRGF0YS5vcHRpb25zCgogICAgICAgIC8vIOWmguaenG9wdGlvbnPmmK/lrZfnrKbkuLLvvIzpnIDopoHop6PmnpDkuLpKU09OCiAgICAgICAgaWYgKHR5cGVvZiBvcHRpb25zID09PSAnc3RyaW5nJykgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgb3B0aW9ucyA9IEpTT04ucGFyc2Uob3B0aW9ucykKICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcign6Kej5p6Q6YCJ6aG5SlNPTuWksei0pTonLCBlKQogICAgICAgICAgICBvcHRpb25zID0gW10KICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIGlmIChBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7CiAgICAgICAgICBmb3JtRGF0YS5vcHRpb25zID0gb3B0aW9ucy5tYXAob3B0aW9uID0+ICh7CiAgICAgICAgICAgIG9wdGlvbktleTogb3B0aW9uLmtleSB8fCBvcHRpb24ub3B0aW9uS2V5LAogICAgICAgICAgICBvcHRpb25Db250ZW50OiBvcHRpb24uY29udGVudCB8fCBvcHRpb24ub3B0aW9uQ29udGVudAogICAgICAgICAgfSkpCgogICAgICAgICAgLy8g6K6+572u5q2j56Gu562U5qGICiAgICAgICAgICBjb25zdCBjb3JyZWN0T3B0aW9ucyA9IG9wdGlvbnMKICAgICAgICAgICAgLmZpbHRlcihvcHRpb24gPT4gb3B0aW9uLmlzQ29ycmVjdCkKICAgICAgICAgICAgLm1hcChvcHRpb24gPT4gb3B0aW9uLmtleSB8fCBvcHRpb24ub3B0aW9uS2V5KQoKICAgICAgICAgIGlmIChxdWVzdGlvbkRhdGEucXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJyB8fCBxdWVzdGlvbkRhdGEucXVlc3Rpb25UeXBlID09PSAxKSB7CiAgICAgICAgICAgIGZvcm1EYXRhLmNvcnJlY3RBbnN3ZXIgPSBjb3JyZWN0T3B0aW9uc1swXSB8fCAnJwogICAgICAgICAgfSBlbHNlIGlmIChxdWVzdGlvbkRhdGEucXVlc3Rpb25UeXBlID09PSAnbXVsdGlwbGUnIHx8IHF1ZXN0aW9uRGF0YS5xdWVzdGlvblR5cGUgPT09IDIpIHsKICAgICAgICAgICAgZm9ybURhdGEuY29ycmVjdEFuc3dlciA9IGNvcnJlY3RPcHRpb25zCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpITnkIbliKTmlq3popjnrZTmoYgKICAgICAgaWYgKChxdWVzdGlvbkRhdGEucXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnIHx8IHF1ZXN0aW9uRGF0YS5xdWVzdGlvblR5cGUgPT09IDMpICYmIHF1ZXN0aW9uRGF0YS5jb3JyZWN0QW5zd2VyKSB7CiAgICAgICAgZm9ybURhdGEuY29ycmVjdEFuc3dlciA9IHF1ZXN0aW9uRGF0YS5jb3JyZWN0QW5zd2VyCiAgICAgIH0KCiAgICAgIHJldHVybiBmb3JtRGF0YQogICAgfSwKCiAgICAvLyDpopjlnovmlbDlrZfovazlrZfnrKbkuLIKICAgIGNvbnZlcnRRdWVzdGlvblR5cGVUb1N0cmluZyh0eXBlKSB7CiAgICAgIGNvbnN0IHR5cGVNYXAgPSB7CiAgICAgICAgMTogJ3NpbmdsZScsCiAgICAgICAgMjogJ211bHRpcGxlJywKICAgICAgICAzOiAnanVkZ21lbnQnCiAgICAgIH0KICAgICAgcmV0dXJuIHR5cGVNYXBbdHlwZV0gfHwgdHlwZQogICAgfSwKCiAgICAvLyDpmr7luqbmlbDlrZfovazlrZfnrKbkuLIKICAgIGNvbnZlcnREaWZmaWN1bHR5VG9TdHJpbmcoZGlmZmljdWx0eSkgewogICAgICBjb25zdCBkaWZmaWN1bHR5TWFwID0gewogICAgICAgIDE6ICfnroDljZUnLAogICAgICAgIDI6ICfkuK3nrYknLAogICAgICAgIDM6ICflm7Dpmr4nCiAgICAgIH0KICAgICAgcmV0dXJuIGRpZmZpY3VsdHlNYXBbZGlmZmljdWx0eV0gfHwgZGlmZmljdWx0eQogICAgfSwKCiAgICAvLyDph43nva7ooajljZUKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHF1ZXN0aW9uSWQ6IG51bGwsCiAgICAgICAgYmFua0lkOiB0aGlzLmJhbmtJZCwKICAgICAgICBxdWVzdGlvblR5cGU6ICdzaW5nbGUnLAogICAgICAgIHF1ZXN0aW9uQ29udGVudDogJycsCiAgICAgICAgZGlmZmljdWx0eTogJ+S4reetiScsCiAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgY29ycmVjdEFuc3dlcjogJycsCiAgICAgICAgZXhwbGFuYXRpb246ICcnCiAgICAgIH0KICAgIH0sCiAgICAvLyDpopjlnovlj5jljJblpITnkIYKICAgIGhhbmRsZVF1ZXN0aW9uVHlwZUNoYW5nZSh0eXBlKSB7CiAgICAgIGlmICh0eXBlID09PSAnc2luZ2xlJyB8fCB0eXBlID09PSAnbXVsdGlwbGUnKSB7CiAgICAgICAgaWYgKHRoaXMuZm9ybS5vcHRpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgICAgdGhpcy5mb3JtLm9wdGlvbnMgPSBbCiAgICAgICAgICAgIHsgb3B0aW9uS2V5OiAnQScsIG9wdGlvbkNvbnRlbnQ6ICcnIH0sCiAgICAgICAgICAgIHsgb3B0aW9uS2V5OiAnQicsIG9wdGlvbkNvbnRlbnQ6ICcnIH0sCiAgICAgICAgICAgIHsgb3B0aW9uS2V5OiAnQycsIG9wdGlvbkNvbnRlbnQ6ICcnIH0sCiAgICAgICAgICAgIHsgb3B0aW9uS2V5OiAnRCcsIG9wdGlvbkNvbnRlbnQ6ICcnIH0KICAgICAgICAgIF0KICAgICAgICB9CiAgICAgICAgLy8g6K6+572u6buY6K6k562U5qGICiAgICAgICAgaWYgKHR5cGUgPT09ICdzaW5nbGUnKSB7CiAgICAgICAgICB0aGlzLmZvcm0uY29ycmVjdEFuc3dlciA9ICdBJwogICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ211bHRpcGxlJykgewogICAgICAgICAgdGhpcy5mb3JtLmNvcnJlY3RBbnN3ZXIgPSAnQSxCJwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAnanVkZ21lbnQnKSB7CiAgICAgICAgdGhpcy5mb3JtLm9wdGlvbnMgPSBbXQogICAgICAgIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyID0gJ3RydWUnICAvLyDliKTmlq3popjpu5jorqTpgInmi6ki5q2j56GuIgogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5vcHRpb25zID0gW10KICAgICAgICB0aGlzLmZvcm0uY29ycmVjdEFuc3dlciA9ICcnCiAgICAgIH0KICAgIH0sCiAgICAvLyDmt7vliqDpgInpobkKICAgIGFkZE9wdGlvbigpIHsKICAgICAgY29uc3QgbmV4dEtleSA9IFN0cmluZy5mcm9tQ2hhckNvZGUoNjUgKyB0aGlzLmZvcm0ub3B0aW9ucy5sZW5ndGgpCiAgICAgIHRoaXMuZm9ybS5vcHRpb25zLnB1c2goewogICAgICAgIG9wdGlvbktleTogbmV4dEtleSwKICAgICAgICBvcHRpb25Db250ZW50OiAnJwogICAgICB9KQogICAgfSwKICAgIC8vIOWIoOmZpOmAiemhuQogICAgcmVtb3ZlT3B0aW9uKGluZGV4KSB7CiAgICAgIHRoaXMuZm9ybS5vcHRpb25zLnNwbGljZShpbmRleCwgMSkKICAgICAgLy8g6YeN5paw5YiG6YWN6YCJ6aG56ZSuCiAgICAgIHRoaXMuZm9ybS5vcHRpb25zLmZvckVhY2goKG9wdGlvbiwgaWR4KSA9PiB7CiAgICAgICAgb3B0aW9uLm9wdGlvbktleSA9IFN0cmluZy5mcm9tQ2hhckNvZGUoNjUgKyBpZHgpCiAgICAgIH0pCiAgICAgIC8vIOabtOaWsOato+ehruetlOahiAogICAgICB0aGlzLnVwZGF0ZUNvcnJlY3RBbnN3ZXJBZnRlclJlbW92ZSgpCiAgICB9LAogICAgLy8g5Yig6Zmk6YCJ6aG55ZCO5pu05paw5q2j56Gu562U5qGICiAgICB1cGRhdGVDb3JyZWN0QW5zd2VyQWZ0ZXJSZW1vdmUoKSB7CiAgICAgIGlmICh0aGlzLmZvcm0ucXVlc3Rpb25UeXBlID09PSAnc2luZ2xlJykgewogICAgICAgIGNvbnN0IHZhbGlkS2V5cyA9IHRoaXMuZm9ybS5vcHRpb25zLm1hcChvcHQgPT4gb3B0Lm9wdGlvbktleSkKICAgICAgICBpZiAoIXZhbGlkS2V5cy5pbmNsdWRlcyh0aGlzLmZvcm0uY29ycmVjdEFuc3dlcikpIHsKICAgICAgICAgIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyID0gJycKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLnF1ZXN0aW9uVHlwZSA9PT0gJ211bHRpcGxlJykgewogICAgICAgIGNvbnN0IHZhbGlkS2V5cyA9IHRoaXMuZm9ybS5vcHRpb25zLm1hcChvcHQgPT4gb3B0Lm9wdGlvbktleSkKICAgICAgICBjb25zdCBjdXJyZW50QW5zd2VycyA9IHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyLnNwbGl0KCcsJykuZmlsdGVyKEJvb2xlYW4pCiAgICAgICAgY29uc3QgdmFsaWRBbnN3ZXJzID0gY3VycmVudEFuc3dlcnMuZmlsdGVyKGFucyA9PiB2YWxpZEtleXMuaW5jbHVkZXMoYW5zKSkKICAgICAgICB0aGlzLmZvcm0uY29ycmVjdEFuc3dlciA9IHZhbGlkQW5zd2Vycy5qb2luKCcsJykKICAgICAgfQogICAgfSwKICAgIC8vIOWNlemAiemimOato+ehruetlOahiOWPmOWMlgogICAgaGFuZGxlU2luZ2xlQ29ycmVjdENoYW5nZShvcHRpb25LZXksIGNoZWNrZWQpIHsKICAgICAgdGhpcy5mb3JtLmNvcnJlY3RBbnN3ZXIgPSBjaGVja2VkID8gb3B0aW9uS2V5IDogJycKICAgIH0sCiAgICAvLyDlpJrpgInpopjmraPnoa7nrZTmoYjlj5jljJYKICAgIGhhbmRsZU11bHRpcGxlQ29ycmVjdENoYW5nZShvcHRpb25LZXksIGNoZWNrZWQpIHsKICAgICAgbGV0IGFuc3dlcnMgPSB0aGlzLmZvcm0uY29ycmVjdEFuc3dlciA/IHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyLnNwbGl0KCcsJykgOiBbXQogICAgICBpZiAoY2hlY2tlZCkgewogICAgICAgIGlmICghYW5zd2Vycy5pbmNsdWRlcyhvcHRpb25LZXkpKSB7CiAgICAgICAgICBhbnN3ZXJzLnB1c2gob3B0aW9uS2V5KQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBhbnN3ZXJzID0gYW5zd2Vycy5maWx0ZXIoYW5zID0+IGFucyAhPT0gb3B0aW9uS2V5KQogICAgICB9CiAgICAgIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyID0gYW5zd2Vycy5qb2luKCcsJykKICAgIH0sCiAgICAvLyDliKTmlq3lpJrpgInpopjmmK/lkKbkuLrmraPnoa7nrZTmoYgKICAgIGlzTXVsdGlwbGVDb3JyZWN0KG9wdGlvbktleSkgewogICAgICBpZiAoIXRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyKSByZXR1cm4gZmFsc2UKICAgICAgcmV0dXJuIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyLnNwbGl0KCcsJykuaW5jbHVkZXMob3B0aW9uS2V5KQogICAgfSwKCiAgICAvLyDliKTmlq3pgInpobnmmK/lkKbkuLrmraPnoa7nrZTmoYgKICAgIGlzQ29ycmVjdE9wdGlvbihvcHRpb25LZXkpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5xdWVzdGlvblR5cGUgPT09ICdzaW5nbGUnKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyID09PSBvcHRpb25LZXkKICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0ucXVlc3Rpb25UeXBlID09PSAnbXVsdGlwbGUnKSB7CiAgICAgICAgaWYgKCF0aGlzLmZvcm0uY29ycmVjdEFuc3dlcikgcmV0dXJuIGZhbHNlCiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybS5jb3JyZWN0QW5zd2VyLnNwbGl0KCcsJykuaW5jbHVkZXMob3B0aW9uS2V5KQogICAgICB9CiAgICAgIHJldHVybiBmYWxzZQogICAgfSwKCiAgICAvLyDmj5DkuqTooajljZUKICAgIGhhbmRsZVN1Ym1pdCgpIHsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuc3VibWl0dGluZyA9IHRydWUKCiAgICAgICAgICAvLyDovazmjaLooajljZXmlbDmja7kuLrlkI7nq6/pnIDopoHnmoTmoLzlvI8KICAgICAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB0aGlzLmNvbnZlcnRGb3JtRGF0YUZvclN1Ym1pdCgpCgogICAgICAgICAgY29uc3QgYXBpQ2FsbCA9IHRoaXMuaXNFZGl0ID8gdXBkYXRlUXVlc3Rpb24oc3VibWl0RGF0YSkgOiBhZGRRdWVzdGlvbihzdWJtaXREYXRhKQogICAgICAgICAgYXBpQ2FsbC50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy5zdWJtaXR0aW5nID0gZmFsc2UKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHRoaXMuaXNFZGl0ID8gJ+abtOaWsOaIkOWKnycgOiAn5L+d5a2Y5oiQ5YqfJykKICAgICAgICAgICAgdGhpcy4kZW1pdCgnc3VjY2VzcycpCiAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOmimOebruWksei0pScsIGVycm9yKQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkv53lrZjpopjnm67lpLHotKUnKQogICAgICAgICAgfSkKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOi9rOaNouihqOWNleaVsOaNruS4uuaPkOS6pOagvOW8jwogICAgY29udmVydEZvcm1EYXRhRm9yU3VibWl0KCkgewogICAgICBjb25zdCBkYXRhID0gewogICAgICAgIHF1ZXN0aW9uSWQ6IHRoaXMuZm9ybS5xdWVzdGlvbklkLAogICAgICAgIGJhbmtJZDogdGhpcy5mb3JtLmJhbmtJZCwKICAgICAgICBxdWVzdGlvblR5cGU6IHRoaXMuZm9ybS5xdWVzdGlvblR5cGUsCiAgICAgICAgZGlmZmljdWx0eTogdGhpcy5mb3JtLmRpZmZpY3VsdHksCiAgICAgICAgcXVlc3Rpb25Db250ZW50OiB0aGlzLmZvcm0ucXVlc3Rpb25Db250ZW50LAogICAgICAgIGV4cGxhbmF0aW9uOiB0aGlzLmZvcm0uZXhwbGFuYXRpb24KICAgICAgfQoKICAgICAgLy8g5aSE55CG6YCJ5oup6aKY6YCJ6aG5CiAgICAgIGlmICh0aGlzLmlzQ2hvaWNlUXVlc3Rpb24pIHsKICAgICAgICBkYXRhLm9wdGlvbnMgPSB0aGlzLmZvcm0ub3B0aW9ucy5tYXAob3B0aW9uID0+ICh7CiAgICAgICAgICBrZXk6IG9wdGlvbi5vcHRpb25LZXksCiAgICAgICAgICBjb250ZW50OiBvcHRpb24ub3B0aW9uQ29udGVudCwKICAgICAgICAgIGlzQ29ycmVjdDogdGhpcy5pc0NvcnJlY3RPcHRpb24ob3B0aW9uLm9wdGlvbktleSkKICAgICAgICB9KSkKICAgICAgfQoKICAgICAgLy8g5aSE55CG5Yik5pat6aKY562U5qGICiAgICAgIGlmICh0aGlzLmZvcm0ucXVlc3Rpb25UeXBlID09PSAnanVkZ21lbnQnKSB7CiAgICAgICAgZGF0YS5jb3JyZWN0QW5zd2VyID0gdGhpcy5mb3JtLmNvcnJlY3RBbnN3ZXIKICAgICAgICAvLyDliKTmlq3popjkuZ/pnIDopoHorr7nva5vcHRpb25z5qC85byP77yM5Lul5L6/5ZCO56uv57uf5LiA5aSE55CGCiAgICAgICAgZGF0YS5vcHRpb25zID0gWwogICAgICAgICAgewogICAgICAgICAgICBrZXk6ICd0cnVlJywKICAgICAgICAgICAgY29udGVudDogJ+ato+ehricsCiAgICAgICAgICAgIGlzQ29ycmVjdDogdGhpcy5mb3JtLmNvcnJlY3RBbnN3ZXIgPT09ICd0cnVlJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAga2V5OiAnZmFsc2UnLAogICAgICAgICAgICBjb250ZW50OiAn6ZSZ6K+vJywKICAgICAgICAgICAgaXNDb3JyZWN0OiB0aGlzLmZvcm0uY29ycmVjdEFuc3dlciA9PT0gJ2ZhbHNlJwogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfQoKICAgICAgcmV0dXJuIGRhdGEKICAgIH0sCiAgICAvLyDlhbPpl63liY3noa7orqQKICAgIGhhbmRsZUJlZm9yZUNsb3NlKGRvbmUpIHsKICAgICAgLy8g5aaC5p6c5q2j5Zyo5o+Q5Lqk77yM5LiN5YWB6K645YWz6ZetCiAgICAgIGlmICh0aGlzLnN1Ym1pdHRpbmcpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ato+WcqOS/neWtmOS4re+8jOivt+eojeWAmS4uLicpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOajgOafpeihqOWNleaYr+WQpuacieWGheWuuQogICAgICBjb25zdCBoYXNDb250ZW50ID0gdGhpcy5mb3JtLnF1ZXN0aW9uQ29udGVudCB8fAogICAgICAgICAgICAgICAgICAgICAgICAodGhpcy5mb3JtLm9wdGlvbnMgJiYgdGhpcy5mb3JtLm9wdGlvbnMubGVuZ3RoID4gMCkgfHwKICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLmV4cGxhbmF0aW9uCgogICAgICBpZiAoaGFzQ29udGVudCkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOWFs+mXre+8n+acquS/neWtmOeahOWGheWuueWwhuS8muS4ouWksScsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgdGhpcy5oYW5kbGVDbG9zZSgpCiAgICAgICAgICBkb25lKCkKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAvLyDnlKjmiLflj5bmtojlhbPpl60KICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaGFuZGxlQ2xvc2UoKQogICAgICAgIGRvbmUoKQogICAgICB9CiAgICB9LAoKICAgIC8vIOWPlua2iOaMiemSrueCueWHuwogICAgaGFuZGxlQ2FuY2VsKCkgewogICAgICB0aGlzLmhhbmRsZUNsb3NlKCkKICAgIH0sCgogICAgLy8g5YWz6Zet5a+56K+d5qGGCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgICAgdGhpcy4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCkKICAgICAgdGhpcy5yZXNldEZvcm0oKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["QuestionForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "QuestionForm.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    :title=\"dialogTitle\"\n    :visible.sync=\"dialogVisible\"\n    width=\"55%\"\n    :before-close=\"handleBeforeClose\"\n    :close-on-click-modal=\"false\"\n    :close-on-press-escape=\"false\"\n    append-to-body\n  >\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n      <!-- 基本信息 -->\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-form-item label=\"题型\" prop=\"questionType\">\n            <el-select v-model=\"form.questionType\" placeholder=\"请选择题型\" style=\"width: 100%;\" :disabled=\"isEdit\">\n              <el-option label=\"单选题\" value=\"single\"></el-option>\n              <el-option label=\"多选题\" value=\"multiple\"></el-option>\n              <el-option label=\"判断题\" value=\"judgment\"></el-option>\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"难度\" prop=\"difficulty\">\n            <el-radio-group v-model=\"form.difficulty\">\n              <el-radio-button label=\"简单\">简单</el-radio-button>\n              <el-radio-button label=\"中等\">中等</el-radio-button>\n              <el-radio-button label=\"困难\">困难</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 题干 -->\n      <el-form-item label=\"题干\" prop=\"questionContent\">\n        <option-editor\n          v-model=\"form.questionContent\"\n          :min-height=\"100\"\n          placeholder=\"请输入题干内容\"\n          editor-id=\"question-content\"\n        />\n      </el-form-item>\n\n\n\n      <!-- 选择题选项 -->\n      <div v-if=\"isChoiceQuestion\">\n        <el-form-item label=\"选项设置\" required>\n          <div class=\"options-container\">\n            <div\n              v-for=\"(option, index) in form.options\"\n              :key=\"index\"\n              class=\"option-item\"\n            >\n              <div class=\"option-row\">\n                <div class=\"option-answer\">\n                  <el-checkbox\n                    v-if=\"form.questionType === 'single'\"\n                    :value=\"form.correctAnswer === option.optionKey\"\n                    @change=\"handleSingleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                  <el-checkbox\n                    v-else-if=\"form.questionType === 'multiple'\"\n                    :value=\"isMultipleCorrect(option.optionKey)\"\n                    @change=\"handleMultipleCorrectChange(option.optionKey, $event)\"\n                  >\n                    {{ option.optionKey }}\n                  </el-checkbox>\n                </div>\n                <div class=\"option-content\">\n                  <option-editor\n                    :key=\"`option-${option.optionKey}-${form.questionType}`\"\n                    v-model=\"option.optionContent\"\n                    :min-height=\"50\"\n                    :placeholder=\"`请输入选项${option.optionKey}内容`\"\n                    :editor-id=\"`option-${option.optionKey}-${form.questionType}`\"\n                  />\n                </div>\n                <div class=\"option-actions\">\n                  <el-button\n                    v-if=\"form.options.length > 2\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    @click=\"removeOption(index)\"\n                    style=\"color: #F56C6C;\"\n                    size=\"mini\"\n                  >\n                    删除\n                  </el-button>\n                </div>\n              </div>\n            </div>\n            <el-button\n              v-if=\"form.options.length < 8\"\n              type=\"dashed\"\n              icon=\"el-icon-plus\"\n              @click=\"addOption\"\n              style=\"width: 100%; margin-top: 10px;\"\n            >\n              添加选项\n            </el-button>\n          </div>\n        </el-form-item>\n      </div>\n\n      <!-- 判断题答案 -->\n      <div v-if=\"form.questionType === 'judgment'\">\n        <el-form-item label=\"正确答案\" prop=\"correctAnswer\">\n          <el-radio-group v-model=\"form.correctAnswer\">\n            <el-radio label=\"true\">正确</el-radio>\n            <el-radio label=\"false\">错误</el-radio>\n          </el-radio-group>\n        </el-form-item>\n      </div>\n\n      <!-- 题目解析 -->\n      <el-form-item label=\"解析\">\n        <option-editor\n          v-model=\"form.explanation\"\n          :min-height=\"100\"\n          placeholder=\"请输入解析内容\"\n          editor-id=\"question-explanation\"\n        />\n      </el-form-item>\n\n\n    </el-form>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleCancel\" :disabled=\"submitting\">取消</el-button>\n      <el-button type=\"primary\" @click=\"handleSubmit\" :loading=\"submitting\">\n        {{ isEdit ? '更新' : '保存' }}\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { addQuestion, updateQuestion } from '@/api/biz/question'\nimport OptionEditor from './OptionEditor'\n\nexport default {\n  name: \"QuestionForm\",\n  components: {\n    OptionEditor\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    questionType: {\n      type: String,\n      default: 'single'\n    },\n    questionData: {\n      type: Object,\n      default: null\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      form: {\n        questionId: null,\n        bankId: null,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      },\n      rules: {\n        questionType: [\n          { required: true, message: '请选择题型', trigger: 'change' }\n        ],\n        questionContent: [\n          { required: true, message: '请输入题目内容', trigger: 'blur' }\n        ],\n        difficulty: [\n          { required: true, message: '请选择难度系数', trigger: 'change' }\n        ],\n        correctAnswer: [\n          { required: true, message: '请设置正确答案', trigger: 'change' }\n        ]\n      }\n    }\n  },\n  computed: {\n    dialogTitle() {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      const typeName = typeMap[this.form.questionType] || '题目'\n      return this.isEdit ? `编辑${typeName}` : `创建${typeName}`\n    },\n    isEdit() {\n      return this.questionData && this.questionData.questionId\n    },\n    isChoiceQuestion() {\n      return ['single', 'multiple'].includes(this.form.questionType)\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    'form.questionType'(newType) {\n      this.handleQuestionTypeChange(newType)\n    },\n    questionData: {\n      handler(newData) {\n        if (newData && this.dialogVisible) {\n          this.initForm()\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    // 初始化表单\n    initForm() {\n      if (this.questionData) {\n        this.form = this.convertQuestionDataForForm(this.questionData)\n      } else {\n        this.resetForm()\n        this.form.questionType = this.questionType\n        this.form.bankId = this.bankId\n      }\n      this.handleQuestionTypeChange(this.form.questionType)\n    },\n\n    // 转换后端数据为表单格式\n    convertQuestionDataForForm(questionData) {\n      const formData = {\n        questionId: questionData.questionId,\n        bankId: questionData.bankId,\n        questionType: this.convertQuestionTypeToString(questionData.questionType),\n        difficulty: this.convertDifficultyToString(questionData.difficulty),\n        questionContent: questionData.questionContent,\n        explanation: questionData.explanation || questionData.analysis || '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 处理选择题选项\n      if (questionData.options) {\n        let options = questionData.options\n\n        // 如果options是字符串，需要解析为JSON\n        if (typeof options === 'string') {\n          try {\n            options = JSON.parse(options)\n          } catch (e) {\n            console.error('解析选项JSON失败:', e)\n            options = []\n          }\n        }\n\n        if (Array.isArray(options)) {\n          formData.options = options.map(option => ({\n            optionKey: option.key || option.optionKey,\n            optionContent: option.content || option.optionContent\n          }))\n\n          // 设置正确答案\n          const correctOptions = options\n            .filter(option => option.isCorrect)\n            .map(option => option.key || option.optionKey)\n\n          if (questionData.questionType === 'single' || questionData.questionType === 1) {\n            formData.correctAnswer = correctOptions[0] || ''\n          } else if (questionData.questionType === 'multiple' || questionData.questionType === 2) {\n            formData.correctAnswer = correctOptions\n          }\n        }\n      }\n\n      // 处理判断题答案\n      if ((questionData.questionType === 'judgment' || questionData.questionType === 3) && questionData.correctAnswer) {\n        formData.correctAnswer = questionData.correctAnswer\n      }\n\n      return formData\n    },\n\n    // 题型数字转字符串\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n\n    // 难度数字转字符串\n    convertDifficultyToString(difficulty) {\n      const difficultyMap = {\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || difficulty\n    },\n\n    // 重置表单\n    resetForm() {\n      this.form = {\n        questionId: null,\n        bankId: this.bankId,\n        questionType: 'single',\n        questionContent: '',\n        difficulty: '中等',\n        options: [],\n        correctAnswer: '',\n        explanation: ''\n      }\n    },\n    // 题型变化处理\n    handleQuestionTypeChange(type) {\n      if (type === 'single' || type === 'multiple') {\n        if (this.form.options.length === 0) {\n          this.form.options = [\n            { optionKey: 'A', optionContent: '' },\n            { optionKey: 'B', optionContent: '' },\n            { optionKey: 'C', optionContent: '' },\n            { optionKey: 'D', optionContent: '' }\n          ]\n        }\n        // 设置默认答案\n        if (type === 'single') {\n          this.form.correctAnswer = 'A'\n        } else if (type === 'multiple') {\n          this.form.correctAnswer = 'A,B'\n        }\n      } else if (type === 'judgment') {\n        this.form.options = []\n        this.form.correctAnswer = 'true'  // 判断题默认选择\"正确\"\n      } else {\n        this.form.options = []\n        this.form.correctAnswer = ''\n      }\n    },\n    // 添加选项\n    addOption() {\n      const nextKey = String.fromCharCode(65 + this.form.options.length)\n      this.form.options.push({\n        optionKey: nextKey,\n        optionContent: ''\n      })\n    },\n    // 删除选项\n    removeOption(index) {\n      this.form.options.splice(index, 1)\n      // 重新分配选项键\n      this.form.options.forEach((option, idx) => {\n        option.optionKey = String.fromCharCode(65 + idx)\n      })\n      // 更新正确答案\n      this.updateCorrectAnswerAfterRemove()\n    },\n    // 删除选项后更新正确答案\n    updateCorrectAnswerAfterRemove() {\n      if (this.form.questionType === 'single') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        if (!validKeys.includes(this.form.correctAnswer)) {\n          this.form.correctAnswer = ''\n        }\n      } else if (this.form.questionType === 'multiple') {\n        const validKeys = this.form.options.map(opt => opt.optionKey)\n        const currentAnswers = this.form.correctAnswer.split(',').filter(Boolean)\n        const validAnswers = currentAnswers.filter(ans => validKeys.includes(ans))\n        this.form.correctAnswer = validAnswers.join(',')\n      }\n    },\n    // 单选题正确答案变化\n    handleSingleCorrectChange(optionKey, checked) {\n      this.form.correctAnswer = checked ? optionKey : ''\n    },\n    // 多选题正确答案变化\n    handleMultipleCorrectChange(optionKey, checked) {\n      let answers = this.form.correctAnswer ? this.form.correctAnswer.split(',') : []\n      if (checked) {\n        if (!answers.includes(optionKey)) {\n          answers.push(optionKey)\n        }\n      } else {\n        answers = answers.filter(ans => ans !== optionKey)\n      }\n      this.form.correctAnswer = answers.join(',')\n    },\n    // 判断多选题是否为正确答案\n    isMultipleCorrect(optionKey) {\n      if (!this.form.correctAnswer) return false\n      return this.form.correctAnswer.split(',').includes(optionKey)\n    },\n\n    // 判断选项是否为正确答案\n    isCorrectOption(optionKey) {\n      if (this.form.questionType === 'single') {\n        return this.form.correctAnswer === optionKey\n      } else if (this.form.questionType === 'multiple') {\n        if (!this.form.correctAnswer) return false\n        return this.form.correctAnswer.split(',').includes(optionKey)\n      }\n      return false\n    },\n\n    // 提交表单\n    handleSubmit() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          this.submitting = true\n\n          // 转换表单数据为后端需要的格式\n          const submitData = this.convertFormDataForSubmit()\n\n          const apiCall = this.isEdit ? updateQuestion(submitData) : addQuestion(submitData)\n          apiCall.then(response => {\n            this.submitting = false\n            this.$message.success(this.isEdit ? '更新成功' : '保存成功')\n            this.$emit('success')\n          }).catch(error => {\n            this.submitting = false\n            console.error('保存题目失败', error)\n            this.$message.error('保存题目失败')\n          })\n        }\n      })\n    },\n\n    // 转换表单数据为提交格式\n    convertFormDataForSubmit() {\n      const data = {\n        questionId: this.form.questionId,\n        bankId: this.form.bankId,\n        questionType: this.form.questionType,\n        difficulty: this.form.difficulty,\n        questionContent: this.form.questionContent,\n        explanation: this.form.explanation\n      }\n\n      // 处理选择题选项\n      if (this.isChoiceQuestion) {\n        data.options = this.form.options.map(option => ({\n          key: option.optionKey,\n          content: option.optionContent,\n          isCorrect: this.isCorrectOption(option.optionKey)\n        }))\n      }\n\n      // 处理判断题答案\n      if (this.form.questionType === 'judgment') {\n        data.correctAnswer = this.form.correctAnswer\n        // 判断题也需要设置options格式，以便后端统一处理\n        data.options = [\n          {\n            key: 'true',\n            content: '正确',\n            isCorrect: this.form.correctAnswer === 'true'\n          },\n          {\n            key: 'false',\n            content: '错误',\n            isCorrect: this.form.correctAnswer === 'false'\n          }\n        ]\n      }\n\n      return data\n    },\n    // 关闭前确认\n    handleBeforeClose(done) {\n      // 如果正在提交，不允许关闭\n      if (this.submitting) {\n        this.$message.warning('正在保存中，请稍候...')\n        return\n      }\n\n      // 检查表单是否有内容\n      const hasContent = this.form.questionContent ||\n                        (this.form.options && this.form.options.length > 0) ||\n                        this.form.explanation\n\n      if (hasContent) {\n        this.$confirm('确认关闭？未保存的内容将会丢失', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.handleClose()\n          done()\n        }).catch(() => {\n          // 用户取消关闭\n        })\n      } else {\n        this.handleClose()\n        done()\n      }\n    },\n\n    // 取消按钮点击\n    handleCancel() {\n      this.handleClose()\n    },\n\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.form.resetFields()\n      this.resetForm()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.editor-container {\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n}\n\n.editor {\n  min-height: 200px;\n}\n\n.options-container {\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  padding: 15px;\n}\n\n.option-item {\n  margin-bottom: 20px;\n}\n\n.option-item:last-child {\n  margin-bottom: 0;\n}\n\n.option-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 15px;\n}\n\n.option-answer {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n}\n\n.option-content {\n  flex: 1;\n}\n\n.option-actions {\n  flex-shrink: 0;\n  width: 60px;\n  padding-top: 8px;\n  text-align: center;\n}\n\n\n</style>\n"]}]}