{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue?vue&type=style&index=0&id=7c007bad&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\BatchImport.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaW1wb3J0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweCAwOwp9Cgouc3RlcC1jb250ZW50IHsKICBtaW4taGVpZ2h0OiA0MDBweDsKfQoKLnRlbXBsYXRlLXNlY3Rpb24gaDMsCi51cGxvYWQtc2VjdGlvbiBoMywKLnByZXZpZXctc2VjdGlvbiBoMyB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBjb2xvcjogIzMzMzsKfQoKLnRlbXBsYXRlLWJ1dHRvbnMgewogIG1hcmdpbjogMjBweCAwOwogIGRpc3BsYXk6IGZsZXg7CiAgZ2FwOiAxNXB4Owp9CgoudGVtcGxhdGUtdGlwcyB7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBwYWRkaW5nOiAxNXB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBtYXJnaW4tdG9wOiAyMHB4Owp9CgoudGVtcGxhdGUtdGlwcyBoNCB7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBjb2xvcjogIzMzMzsKfQoKLnRlbXBsYXRlLXRpcHMgdWwgewogIG1hcmdpbjogMDsKICBwYWRkaW5nLWxlZnQ6IDIwcHg7Cn0KCi50ZW1wbGF0ZS10aXBzIGxpIHsKICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgY29sb3I6ICM2NjY7Cn0KCi8qIOaWh+aho+WvvOWFpeagt+W8jyAqLwouaW1wb3J0LW1vZGUtc2VjdGlvbiBoMyB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBjb2xvcjogIzMzMzsKfQoKLmRvY3VtZW50LXNlY3Rpb24gewogIG1hcmdpbi10b3A6IDIwcHg7Cn0KCi5kb2N1bWVudC1zZWN0aW9uIGg0IHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIGNvbG9yOiAjMzMzOwp9CgouZG9jdW1lbnQtZm9ybWF0LXRpcHMgewogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgcGFkZGluZzogMjBweDsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmRvY3VtZW50LWZvcm1hdC10aXBzIGg0IHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIGNvbG9yOiAjMzMzOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLmZvcm1hdC1ydWxlcyB7CiAgZGlzcGxheTogZ3JpZDsKICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7CiAgZ2FwOiAxNXB4Owp9CgoucnVsZS1pdGVtIHsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIHBhZGRpbmc6IDEycHg7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzQwOWVmZjsKfQoKLnJ1bGUtaXRlbSBoNSB7CiAgbWFyZ2luOiAwIDAgOHB4IDA7CiAgY29sb3I6ICMzMzM7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiA2MDA7Cn0KCi5ydWxlLWl0ZW0gcCB7CiAgbWFyZ2luOiAwOwogIGNvbG9yOiAjNjY2OwogIGZvbnQtc2l6ZTogMTNweDsKICBsaW5lLWhlaWdodDogMS40Owp9CgoucnVsZS1pdGVtIGNvZGUgewogIGJhY2tncm91bmQ6ICNmMWYyZjM7CiAgcGFkZGluZzogMnB4IDZweDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICNlNzRjM2M7Cn0KCi5kb2N1bWVudC1leGFtcGxlIHsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIHBhZGRpbmc6IDIwcHg7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlOWVjZWY7Cn0KCi5kb2N1bWVudC1leGFtcGxlIGg0IHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIGNvbG9yOiAjMzMzOwogIGZvbnQtc2l6ZTogMTZweDsKfQoKLmV4YW1wbGUtdGV4dCB7CiAgYmFja2dyb3VuZDogI2ZmZjsKICBwYWRkaW5nOiAxNXB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOwogIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7CiAgZm9udC1zaXplOiAxM3B4OwogIGxpbmUtaGVpZ2h0OiAxLjY7CiAgY29sb3I6ICMzMzM7CiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOwogIG92ZXJmbG93LXg6IGF1dG87Cn0KCi51cGxvYWQtYXJlYSB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIHBhZGRpbmc6IDQwcHggMDsKICBib3JkZXI6IDJweCBkYXNoZWQgI2Q5ZDlkOTsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYmFja2dyb3VuZDogI2ZhZmFmYTsKICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgMC4zczsKfQoKLnVwbG9hZC1hcmVhOmhvdmVyIHsKICBib3JkZXItY29sb3I6ICM0MDlFRkY7Cn0KCi51cGxvYWQtYXJlYSBpIHsKICBmb250LXNpemU6IDQ4cHg7CiAgY29sb3I6ICNjMGM0Y2M7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnVwbG9hZC10ZXh0IHAgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzY2NjsKfQoKLnVwbG9hZC10aXAgewogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzk5OTsKICBtYXJnaW4tdG9wOiA1cHg7Cn0KCi51cGxvYWRlZC1maWxlIHsKICBtYXJnaW4tdG9wOiAxNXB4Owp9CgouZXJyb3Itc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmVycm9yLWxpc3QgewogIG1heC1oZWlnaHQ6IDE1MHB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgYmFja2dyb3VuZDogI2ZlZjBmMDsKICBib3JkZXI6IDFweCBzb2xpZCAjZmJjNGM0OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiAxMHB4Owp9CgouZXJyb3ItaXRlbSB7CiAgY29sb3I6ICNmNTZjNmM7CiAgZm9udC1zaXplOiAxNHB4OwogIG1hcmdpbi1ib3R0b206IDVweDsKfQoKLnRhYmxlLXRpcCB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGNvbG9yOiAjOTk5OwogIGZvbnQtc2l6ZTogMTJweDsKICBtYXJnaW4tdG9wOiAxMHB4Owp9CgoucmVzdWx0LXNlY3Rpb24gewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA0MHB4IDA7Cn0KCi5yZXN1bHQtaWNvbiB7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLnJlc3VsdC1zdGF0cyB7CiAgbWFyZ2luOiAyMHB4IDA7Cn0KCi5zdWNjZXNzLWNvdW50IHsKICBjb2xvcjogIzY3YzIzYTsKICBmb250LXdlaWdodDogYm9sZDsKICBmb250LXNpemU6IDE4cHg7Cn0KCi5mYWlsLWNvdW50IHsKICBjb2xvcjogI2Y1NmM2YzsKICBmb250LXdlaWdodDogYm9sZDsKICBmb250LXNpemU6IDE4cHg7Cn0KCi5pbXBvcnQtZXJyb3JzIHsKICBtYXJnaW4tdG9wOiAyMHB4OwogIHRleHQtYWxpZ246IGxlZnQ7Cn0KCi5lcnJvci1kZXRhaWwgewogIGNvbG9yOiAjZjU2YzZjOwogIGZvbnQtc2l6ZTogMTRweDsKICBtYXJnaW4tYm90dG9tOiA1cHg7Cn0KCi5zdGVwLWFjdGlvbnMgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBtYXJnaW4tdG9wOiAzMHB4OwogIHBhZGRpbmctdG9wOiAyMHB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTRlN2VkOwp9Cg=="}, {"version": 3, "sources": ["BatchImport.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyvBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BatchImport.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"批量导入题目\"\n    :visible.sync=\"dialogVisible\"\n    width=\"70%\"\n    :before-close=\"handleClose\"\n    append-to-body\n  >\n    <div class=\"import-container\">\n      <!-- 导入步骤 -->\n      <el-steps :active=\"currentStep\" finish-status=\"success\" style=\"margin-bottom: 30px;\">\n        <el-step title=\"下载模板\"></el-step>\n        <el-step title=\"上传文件\"></el-step>\n        <el-step title=\"数据预览\"></el-step>\n        <el-step title=\"导入完成\"></el-step>\n      </el-steps>\n\n      <!-- 步骤1: 选择导入方式 -->\n      <div v-if=\"currentStep === 0\" class=\"step-content\">\n        <div class=\"import-mode-section\">\n          <h3>第一步：选择导入方式</h3>\n\n          <!-- 导入方式选择 -->\n          <el-radio-group v-model=\"importMode\" style=\"margin-bottom: 30px;\">\n            <el-radio label=\"excel\">Excel模板导入</el-radio>\n            <el-radio label=\"document\">文档内容导入</el-radio>\n          </el-radio-group>\n\n          <!-- Excel模板导入 -->\n          <div v-if=\"importMode === 'excel'\" class=\"template-section\">\n            <h4>Excel模板导入</h4>\n            <p>请先下载题目导入模板，按照模板格式填写题目数据</p>\n\n            <div class=\"template-buttons\">\n              <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"downloadTemplate('all')\">\n                下载题目导入模板\n              </el-button>\n            </div>\n\n            <div class=\"template-tips\">\n              <h4>填写说明：</h4>\n              <ul>\n                <li>模板支持单选题、多选题、判断题混合导入</li>\n                <li>题目内容：必填，支持HTML格式</li>\n                <li>选项内容：选择题必填，选项A-H，每个选项单独一列</li>\n                <li>正确答案：必填，单选题填写A/B/C等，多选题用逗号分隔如A,C，判断题填写true/false</li>\n                <li>难度系数：选填，可填写\"简单\"、\"中等\"、\"困难\"</li>\n                <li>题目解析：选填，支持HTML格式</li>\n                <li>请确保Excel文件编码为UTF-8，避免中文乱码</li>\n              </ul>\n            </div>\n          </div>\n\n          <!-- 文档内容导入 -->\n          <div v-if=\"importMode === 'document'\" class=\"document-section\">\n            <h4>文档内容导入</h4>\n            <p>支持上传包含题目内容的文档文件，系统将自动解析题目信息</p>\n\n            <div class=\"document-format-tips\">\n              <h4>格式要求：</h4>\n              <div class=\"format-rules\">\n                <div class=\"rule-item\">\n                  <h5>题型标注（必填）：</h5>\n                  <p><code>[单选题]</code> <code>[多选题]</code> <code>[判断题]</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>题号规则（必填）：</h5>\n                  <p>题目前必须有题号，如：<code>1.</code> <code>2：</code> <code>3．</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>选项格式（必填）：</h5>\n                  <p><code>A.选项内容</code> <code>B：选项内容</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>答案标注（必填）：</h5>\n                  <p><code>答案：A</code> 或题干内 <code>【A】</code></p>\n                </div>\n                <div class=\"rule-item\">\n                  <h5>解析和难度（可选）：</h5>\n                  <p><code>解析：解析内容</code> <code>难度：中等</code></p>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"document-example\">\n              <h4>示例格式：</h4>\n              <pre class=\"example-text\">\n[单选题]\n1.（ ）是我国最早的诗歌总集。\nA.《左传》\nB.《离骚》\nC.《坛经》\nD.《诗经》\n答案：D\n解析：诗经是我国最早的诗歌总集。\n难度：中等\n\n[判断题]\n2.元杂剧的四大悲剧包括郑光祖的《赵氏孤儿》。\n答案：错误\n解析：《赵氏孤儿》实为纪君祥所作。\n              </pre>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"nextStep\">下一步</el-button>\n        </div>\n      </div>\n\n      <!-- 步骤2: 上传文件 -->\n      <div v-if=\"currentStep === 1\" class=\"step-content\">\n        <div class=\"upload-section\">\n          <h3>第二步：上传题目文件</h3>\n          <p v-if=\"importMode === 'excel'\">请选择填写好的Excel文件进行上传</p>\n          <p v-if=\"importMode === 'document'\">请选择包含题目内容的文档文件进行上传</p>\n\n          <el-upload\n            ref=\"fileUpload\"\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :on-success=\"handleFileSuccess\"\n            :on-error=\"handleFileError\"\n            :before-upload=\"beforeFileUpload\"\n            :show-file-list=\"false\"\n            :accept=\"importMode === 'excel' ? '.xlsx,.xls' : '.txt,.doc,.docx'\"\n            drag\n          >\n            <div class=\"upload-area\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"upload-text\">\n                <p>将文件拖到此处，或<em>点击上传</em></p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'excel'\">\n                  支持 .xlsx、.xls 格式文件，文件大小不超过10MB\n                </p>\n                <p class=\"upload-tip\" v-if=\"importMode === 'document'\">\n                  支持 .txt、.doc、.docx 格式文件，文件大小不超过10MB\n                </p>\n              </div>\n            </div>\n          </el-upload>\n\n          <div v-if=\"uploadedFile\" class=\"uploaded-file\">\n            <el-alert\n              :title=\"`已上传文件：${uploadedFile.name}`\"\n              type=\"success\"\n              :closable=\"false\"\n              show-icon\n            />\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button type=\"primary\" @click=\"parseFile\" :disabled=\"!uploadedFile\" :loading=\"parsing\">\n            解析文件\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤3: 数据预览 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"preview-section\">\n          <h3>第三步：数据预览与确认</h3>\n          <p>共解析到 {{ parsedData.length }} 道题目，请确认数据无误后点击导入</p>\n\n          <div v-if=\"parseErrors.length > 0\" class=\"error-section\">\n            <el-alert\n              title=\"数据解析错误\"\n              type=\"error\"\n              :closable=\"false\"\n              show-icon\n              style=\"margin-bottom: 15px;\"\n            />\n            <div class=\"error-list\">\n              <div v-for=\"(error, index) in parseErrors\" :key=\"index\" class=\"error-item\">\n                第{{ error.row }}行：{{ error.message }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-table\">\n            <el-table :data=\"parsedData.slice(0, 10)\" border style=\"width: 100%\">\n              <el-table-column prop=\"questionType\" label=\"题型\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-tag :type=\"getQuestionTypeColor(scope.row.questionType)\" size=\"mini\">\n                    {{ getQuestionTypeName(scope.row.questionType) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column prop=\"questionContent\" label=\"题目内容\" min-width=\"200\" show-overflow-tooltip />\n              <el-table-column prop=\"correctAnswer\" label=\"正确答案\" width=\"100\" />\n              <el-table-column prop=\"difficulty\" label=\"难度\" width=\"80\" />\n            </el-table>\n            <div v-if=\"parsedData.length > 10\" class=\"table-tip\">\n              仅显示前10条数据，共{{ parsedData.length }}条\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button @click=\"prevStep\">上一步</el-button>\n          <el-button \n            type=\"primary\" \n            @click=\"importData\" \n            :disabled=\"parseErrors.length > 0 || parsedData.length === 0\"\n            :loading=\"importing\"\n          >\n            确认导入\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 步骤4: 导入完成 -->\n      <div v-if=\"currentStep === 3\" class=\"step-content\">\n        <div class=\"result-section\">\n          <div class=\"result-icon\">\n            <i class=\"el-icon-success\" style=\"font-size: 60px; color: #67c23a;\"></i>\n          </div>\n          <h3>导入完成</h3>\n          <div class=\"result-stats\">\n            <p>成功导入 <span class=\"success-count\">{{ importResult.successCount }}</span> 道题目</p>\n            <p v-if=\"importResult.failCount > 0\">\n              失败 <span class=\"fail-count\">{{ importResult.failCount }}</span> 道题目\n            </p>\n          </div>\n          \n          <div v-if=\"importResult.errors.length > 0\" class=\"import-errors\">\n            <el-collapse>\n              <el-collapse-item title=\"查看失败详情\" name=\"errors\">\n                <div v-for=\"(error, index) in importResult.errors\" :key=\"index\" class=\"error-detail\">\n                  第{{ error.row }}行：{{ error.message }}\n                </div>\n              </el-collapse-item>\n            </el-collapse>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <el-button type=\"primary\" @click=\"handleComplete\">完成</el-button>\n          <el-button @click=\"resetImport\">重新导入</el-button>\n        </div>\n      </div>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nimport { getToken } from '@/utils/auth'\nimport { downloadTemplate, parseImportFile, batchImportQuestions } from '@/api/biz/question'\nimport { download } from '@/utils/request'\n\nexport default {\n  name: \"BatchImport\",\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    bankId: {\n      type: [String, Number],\n      required: true\n    },\n    defaultMode: {\n      type: String,\n      default: 'excel' // excel 或 document\n    }\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      currentStep: 0,\n      importMode: 'excel', // 导入模式：excel 或 document\n      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + getToken()\n      },\n      uploadedFile: null,\n      parsing: false,\n      importing: false,\n      parsedData: [],\n      parseErrors: [],\n      importResult: {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    }\n  },\n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.resetImport()\n      }\n    },\n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    },\n    defaultMode(val) {\n      if (val) {\n        this.importMode = val\n      }\n    }\n  },\n  methods: {\n    // 下载模板\n    downloadTemplate(type) {\n      const fileName = type === 'all' ? '题目导入模板.xlsx' : `${this.getQuestionTypeName(type)}导入模板.xlsx`\n      download('/biz/question/downloadTemplate', { questionType: type }, fileName)\n    },\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 下一步\n    nextStep() {\n      this.currentStep++\n    },\n    // 上一步\n    prevStep() {\n      this.currentStep--\n    },\n    // 文件上传前验证\n    beforeFileUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (this.importMode === 'excel') {\n        const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                       file.type === 'application/vnd.ms-excel'\n        if (!isExcel) {\n          this.$message.error('只能上传Excel文件!')\n          return false\n        }\n      } else if (this.importMode === 'document') {\n        const isDocument = file.type === 'text/plain' ||\n                          file.type === 'application/msword' ||\n                          file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                          file.name.toLowerCase().endsWith('.txt') ||\n                          file.name.toLowerCase().endsWith('.doc') ||\n                          file.name.toLowerCase().endsWith('.docx')\n        if (!isDocument) {\n          this.$message.error('只能上传文档文件(.txt, .doc, .docx)!')\n          return false\n        }\n      }\n\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n      return true\n    },\n    // 文件上传成功\n    handleFileSuccess(response, file) {\n      if (response.code === 200) {\n        this.uploadedFile = {\n          name: file.name,\n          url: response.url,\n          fileName: response.fileName\n        }\n        this.$message.success('文件上传成功')\n      } else {\n        this.$message.error(response.msg || '文件上传失败')\n      }\n    },\n    // 文件上传失败\n    handleFileError() {\n      this.$message.error('文件上传失败')\n    },\n    // 解析文件\n    parseFile() {\n      this.parsing = true\n\n      if (this.importMode === 'excel') {\n        // Excel文件解析\n        const parseData = {\n          fileName: this.uploadedFile.fileName,\n          bankId: this.bankId\n        }\n        parseImportFile(parseData).then(response => {\n          this.parsing = false\n          this.parsedData = response.data.questions || []\n          this.parseErrors = response.data.errors || []\n          this.nextStep()\n        }).catch(error => {\n          this.parsing = false\n          console.error('解析文件失败', error)\n          this.$message.error('解析文件失败')\n        })\n      } else if (this.importMode === 'document') {\n        // 文档内容解析\n        this.parseDocumentContent()\n      }\n    },\n\n    // 解析文档内容\n    async parseDocumentContent() {\n      try {\n        // 读取文件内容\n        const fileContent = await this.readFileContent(this.uploadedFile.url)\n\n        // 解析题目\n        const parseResult = this.parseQuestionContent(fileContent)\n\n        this.parsing = false\n        this.parsedData = parseResult.questions\n        this.parseErrors = parseResult.errors\n        this.nextStep()\n      } catch (error) {\n        this.parsing = false\n        console.error('解析文档内容失败', error)\n        this.$message.error('解析文档内容失败')\n      }\n    },\n\n    // 读取文件内容\n    readFileContent(fileUrl) {\n      return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest()\n        xhr.open('GET', process.env.VUE_APP_BASE_API + fileUrl, true)\n        xhr.setRequestHeader('Authorization', 'Bearer ' + getToken())\n        xhr.responseType = 'text'\n\n        xhr.onload = function() {\n          if (xhr.status === 200) {\n            resolve(xhr.responseText)\n          } else {\n            reject(new Error('读取文件失败'))\n          }\n        }\n\n        xhr.onerror = function() {\n          reject(new Error('读取文件失败'))\n        }\n\n        xhr.send()\n      })\n    },\n\n    // 解析题目内容\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      try {\n        // 按题型分割内容\n        const sections = this.splitByQuestionType(content)\n\n        sections.forEach((section, sectionIndex) => {\n          try {\n            const parsedQuestions = this.parseSectionQuestions(section)\n            questions.push(...parsedQuestions)\n          } catch (error) {\n            errors.push(`第${sectionIndex + 1}个题型区域解析失败: ${error.message}`)\n          }\n        })\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 按题型分割内容\n    splitByQuestionType(content) {\n      const sections = []\n      const typeRegex = /\\[(单选题|多选题|判断题)\\]/g\n\n      let lastIndex = 0\n      let match\n      let currentType = null\n\n      while ((match = typeRegex.exec(content)) !== null) {\n        if (currentType) {\n          // 保存上一个区域\n          sections.push({\n            type: currentType,\n            content: content.substring(lastIndex, match.index).trim()\n          })\n        }\n        currentType = match[1]\n        lastIndex = match.index + match[0].length\n      }\n\n      // 保存最后一个区域\n      if (currentType) {\n        sections.push({\n          type: currentType,\n          content: content.substring(lastIndex).trim()\n        })\n      }\n\n      return sections\n    },\n\n    // 解析区域内的题目\n    parseSectionQuestions(section) {\n      const questions = []\n      const questionType = this.convertQuestionType(section.type)\n\n      // 按题号分割题目\n      const questionBlocks = this.splitByQuestionNumber(section.content)\n\n      questionBlocks.forEach((block, index) => {\n        try {\n          const question = this.parseQuestionBlock(block, questionType, index + 1)\n          if (question) {\n            questions.push(question)\n          }\n        } catch (error) {\n          throw new Error(`第${index + 1}题解析失败: ${error.message}`)\n        }\n      })\n\n      return questions\n    },\n\n    // 按题号分割题目\n    splitByQuestionNumber(content) {\n      const blocks = []\n      const numberRegex = /^\\s*(\\d+)[.:：．]\\s*/gm\n\n      let lastIndex = 0\n      let match\n\n      while ((match = numberRegex.exec(content)) !== null) {\n        if (lastIndex > 0) {\n          // 保存上一题\n          blocks.push(content.substring(lastIndex, match.index).trim())\n        }\n        lastIndex = match.index\n      }\n\n      // 保存最后一题\n      if (lastIndex < content.length) {\n        blocks.push(content.substring(lastIndex).trim())\n      }\n\n      return blocks.filter(block => block.length > 0)\n    },\n\n    // 解析单个题目块\n    parseQuestionBlock(block, questionType, questionIndex) {\n      const lines = block.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      // 提取题号和题干\n      const firstLine = lines[0]\n      const numberMatch = firstLine.match(/^\\s*(\\d+)[.:：．]\\s*(.*)/)\n      if (!numberMatch) {\n        throw new Error('题号格式不正确')\n      }\n\n      let questionContent = numberMatch[2]\n      let currentLineIndex = 1\n\n      // 继续读取题干内容（直到遇到选项）\n      while (currentLineIndex < lines.length) {\n        const line = lines[currentLineIndex]\n        if (this.isOptionLine(line)) {\n          break\n        }\n        questionContent += '\\n' + line\n        currentLineIndex++\n      }\n\n      const question = {\n        questionType: questionType,\n        questionContent: questionContent.trim(),\n        difficulty: '中等',\n        explanation: '',\n        options: [],\n        correctAnswer: ''\n      }\n\n      // 解析选项（对于选择题）\n      if (questionType !== 'judgment') {\n        const optionResult = this.parseOptions(lines, currentLineIndex)\n        question.options = optionResult.options\n        currentLineIndex = optionResult.nextIndex\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMeta(lines, currentLineIndex, question)\n\n      return question\n    },\n\n    // 判断是否为选项行\n    isOptionLine(line) {\n      return /^[A-Za-z][.:：．]\\s*/.test(line)\n    },\n\n    // 解析选项\n    parseOptions(lines, startIndex) {\n      const options = []\n      let currentIndex = startIndex\n\n      while (currentIndex < lines.length) {\n        const line = lines[currentIndex]\n        const optionMatch = line.match(/^([A-Za-z])[.:：．]\\s*(.*)/)\n\n        if (!optionMatch) {\n          break\n        }\n\n        options.push({\n          optionKey: optionMatch[1].toUpperCase(),\n          optionContent: optionMatch[2].trim()\n        })\n\n        currentIndex++\n      }\n\n      return { options, nextIndex: currentIndex }\n    },\n\n    // 解析题目元信息（答案、解析、难度）\n    parseQuestionMeta(lines, startIndex, question) {\n      for (let i = startIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 解析答案\n        const answerMatch = line.match(/^答案[：:]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswer(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 解析解析\n        const explanationMatch = line.match(/^解析[：:]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 解析难度\n        const difficultyMatch = line.match(/^难度[：:]\\s*(简单|中等|困难)/)\n        if (difficultyMatch) {\n          question.difficulty = difficultyMatch[1]\n          continue\n        }\n      }\n\n      // 如果没有显式答案，尝试从题干中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 解析答案\n    parseAnswer(answerText, questionType) {\n      if (questionType === 'judgment') {\n        // 判断题答案处理\n        if (answerText.includes('正确') || answerText.includes('对') || answerText.toLowerCase().includes('true')) {\n          return 'true'\n        } else if (answerText.includes('错误') || answerText.includes('错') || answerText.includes('假') || answerText.toLowerCase().includes('false')) {\n          return 'false'\n        }\n        return answerText.trim()\n      } else {\n        // 选择题答案处理\n        return answerText.replace(/[,，\\s]/g, '').toUpperCase()\n      }\n    },\n\n    // 从题干中提取答案\n    extractAnswerFromContent(content, questionType) {\n      // 支持的括号类型\n      const bracketPatterns = [\n        /【([^】]+)】/g,\n        /\\[([^\\]]+)\\]/g,\n        /（([^）]+)）/g,\n        /\\(([^)]+)\\)/g\n      ]\n\n      for (const pattern of bracketPatterns) {\n        const matches = [...content.matchAll(pattern)]\n        if (matches.length > 0) {\n          const answer = matches[matches.length - 1][1] // 取最后一个匹配\n          return this.parseAnswer(answer, questionType)\n        }\n      }\n\n      return ''\n    },\n\n    // 转换题型\n    convertQuestionType(typeText) {\n      const typeMap = {\n        '单选题': 'single',\n        '多选题': 'multiple',\n        '判断题': 'judgment'\n      }\n      return typeMap[typeText] || 'single'\n    },\n\n    // 导入数据\n    importData() {\n      this.importing = true\n      const importData = {\n        bankId: this.bankId,\n        questions: this.parsedData\n      }\n      batchImportQuestions(importData).then(response => {\n        this.importing = false\n        this.importResult = response.data\n        this.nextStep()\n      }).catch(error => {\n        this.importing = false\n        console.error('导入数据失败', error)\n        this.$message.error('导入数据失败')\n      })\n    },\n    // 完成导入\n    handleComplete() {\n      this.$emit('success')\n      this.handleClose()\n    },\n    // 重置导入\n    resetImport() {\n      this.currentStep = 0\n      this.importMode = this.defaultMode || 'excel'\n      this.uploadedFile = null\n      this.parsedData = []\n      this.parseErrors = []\n      this.importResult = {\n        successCount: 0,\n        failCount: 0,\n        errors: []\n      }\n    },\n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.import-container {\n  padding: 20px 0;\n}\n\n.step-content {\n  min-height: 400px;\n}\n\n.template-section h3,\n.upload-section h3,\n.preview-section h3 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-buttons {\n  margin: 20px 0;\n  display: flex;\n  gap: 15px;\n}\n\n.template-tips {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.template-tips h4 {\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.template-tips ul {\n  margin: 0;\n  padding-left: 20px;\n}\n\n.template-tips li {\n  margin-bottom: 5px;\n  color: #666;\n}\n\n/* 文档导入样式 */\n.import-mode-section h3 {\n  margin-bottom: 20px;\n  color: #333;\n}\n\n.document-section {\n  margin-top: 20px;\n}\n\n.document-section h4 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.document-format-tips {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n}\n\n.document-format-tips h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.format-rules {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.rule-item {\n  background: #fff;\n  padding: 12px;\n  border-radius: 4px;\n  border-left: 3px solid #409eff;\n}\n\n.rule-item h5 {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.rule-item p {\n  margin: 0;\n  color: #666;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.rule-item code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  color: #e74c3c;\n}\n\n.document-example {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.document-example h4 {\n  margin-bottom: 15px;\n  color: #333;\n  font-size: 16px;\n}\n\n.example-text {\n  background: #fff;\n  padding: 15px;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n.upload-area {\n  text-align: center;\n  padding: 40px 0;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  background: #fafafa;\n  transition: border-color 0.3s;\n}\n\n.upload-area:hover {\n  border-color: #409EFF;\n}\n\n.upload-area i {\n  font-size: 48px;\n  color: #c0c4cc;\n  margin-bottom: 20px;\n}\n\n.upload-text p {\n  margin: 0;\n  color: #666;\n}\n\n.upload-tip {\n  font-size: 12px;\n  color: #999;\n  margin-top: 5px;\n}\n\n.uploaded-file {\n  margin-top: 15px;\n}\n\n.error-section {\n  margin-bottom: 20px;\n}\n\n.error-list {\n  max-height: 150px;\n  overflow-y: auto;\n  background: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  padding: 10px;\n}\n\n.error-item {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.table-tip {\n  text-align: center;\n  color: #999;\n  font-size: 12px;\n  margin-top: 10px;\n}\n\n.result-section {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.result-icon {\n  margin-bottom: 20px;\n}\n\n.result-stats {\n  margin: 20px 0;\n}\n\n.success-count {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.fail-count {\n  color: #f56c6c;\n  font-weight: bold;\n  font-size: 18px;\n}\n\n.import-errors {\n  margin-top: 20px;\n  text-align: left;\n}\n\n.error-detail {\n  color: #f56c6c;\n  font-size: 14px;\n  margin-bottom: 5px;\n}\n\n.step-actions {\n  text-align: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #e4e7ed;\n}\n</style>\n"]}]}