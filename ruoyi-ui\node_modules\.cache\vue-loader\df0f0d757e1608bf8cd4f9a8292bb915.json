{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue?vue&type=style&index=0&id=4755e558&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\components\\QuestionCard.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["QuestionCard.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2RA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "QuestionCard.vue", "sourceRoot": "src/views/biz/questionBank/components", "sourcesContent": ["<template>\n  <div class=\"question-card\">\n    <!-- 题目头部 -->\n    <div class=\"question-header\">\n      <div class=\"header-left\">\n        <el-checkbox\n          :value=\"selected\"\n          @change=\"handleSelectionChange\"\n          style=\"margin-right: 12px;\"\n        ></el-checkbox>\n        <span class=\"question-number\">{{ index }}.</span>\n        <span class=\"question-type-bracket\">[{{ getQuestionTypeName(question.questionType) }}]</span>\n        <span class=\"difficulty-label\">难度系数: {{ getDifficultyName(question.difficulty) }}</span>\n        <span class=\"create-time\">创建时间: {{ formatTime(question.createTime) }}</span>\n      </div>\n      <div class=\"header-right\">\n        <el-button\n          type=\"text\"\n          :icon=\"expanded ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpand\"\n          size=\"small\"\n        >\n          {{ expanded ? '收起' : '展开' }}\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-edit\"\n          @click=\"handleEdit\"\n          size=\"small\"\n        >\n          编辑\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-copy-document\"\n          @click=\"handleCopy\"\n          size=\"small\"\n        >\n          复制\n        </el-button>\n        <el-button\n          type=\"text\"\n          icon=\"el-icon-delete\"\n          @click=\"handleDelete\"\n          size=\"small\"\n          style=\"color: #F56C6C;\"\n        >\n          删除\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 题目内容 -->\n    <div class=\"question-content\">\n      <div v-html=\"question.questionContent\"></div>\n    </div>\n\n    <!-- 展开内容 -->\n    <div v-if=\"expanded\" class=\"question-detail\">\n\n\n      <!-- 题目选项（包括选择题和判断题） -->\n      <div v-if=\"validOptions.length > 0\" class=\"answer-section\">\n        <div class=\"options-list\">\n          <div\n            v-for=\"(option, index) in validOptions\"\n            :key=\"option.key || option.optionKey\"\n            class=\"option-item\"\n            :class=\"{ 'correct-option': isCorrectOption(option.key || option.optionKey) }\"\n          >\n            <span class=\"option-key\">{{ getOptionKey(option, index) }}.</span>\n            <span class=\"option-content\" v-html=\"option.content || option.optionContent\"></span>\n            <span v-if=\"isCorrectOption(option.key || option.optionKey)\" class=\"correct-mark\">✓</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 题目解析 -->\n      <div v-if=\"question.explanation || question.analysis\" class=\"explanation-section\">\n        <div class=\"explanation-text\" v-html=\"question.explanation || question.analysis\"></div>\n      </div>\n\n\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"QuestionCard\",\n  props: {\n    question: {\n      type: Object,\n      required: true\n    },\n    index: {\n      type: Number,\n      required: true\n    },\n    expanded: {\n      type: Boolean,\n      default: false\n    },\n    selected: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {}\n  },\n  computed: {\n    isChoiceQuestion() {\n      return ['single', 'multiple', 1, 2].includes(this.question.questionType)\n    },\n    isJudgmentQuestion() {\n      return ['judgment', 3].includes(this.question.questionType)\n    },\n    // 过滤有效的选项\n    validOptions() {\n      let options = this.question.options\n\n      // 如果options是字符串，需要解析为JSON\n      if (typeof options === 'string') {\n        try {\n          options = JSON.parse(options)\n        } catch (e) {\n          return []\n        }\n      }\n\n      if (!options || !Array.isArray(options)) {\n        return []\n      }\n\n      return options.filter(option => {\n        // 检查选项是否有效\n        const key = option.key || option.optionKey\n        const content = option.content || option.optionContent\n\n        const hasKey = key && key.toString().trim()\n        // 内容可以为空，但key必须存在\n        const hasValidKey = hasKey\n        // 如果内容为空或只包含HTML标签，则过滤掉\n        const hasValidContent = content && content.toString().trim() &&\n                               content.toString().replace(/<[^>]*>/g, '').trim()\n\n        return hasValidKey && hasValidContent\n      })\n    }\n  },\n  methods: {\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        // 字符串格式\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题',\n        // 数字格式\n        1: '单选题',\n        2: '多选题',\n        3: '判断题'\n      }\n      return typeMap[type] || '未知题型'\n    },\n    // 获取题型颜色\n    getQuestionTypeColor(type) {\n      const colorMap = {\n        // 字符串格式\n        'single': 'primary',\n        'multiple': 'success',\n        'judgment': 'warning',\n        // 数字格式\n        1: 'primary',\n        2: 'success',\n        3: 'warning'\n      }\n      return colorMap[type] || 'info'\n    },\n    // 获取难度名称\n    getDifficultyName(difficulty) {\n      const difficultyMap = {\n        // 字符串格式\n        '简单': '简单',\n        '中等': '中等',\n        '困难': '困难',\n        // 数字格式\n        1: '简单',\n        2: '中等',\n        3: '困难'\n      }\n      return difficultyMap[difficulty] || '中等'\n    },\n    // 获取判断题答案类型（用于标签颜色）\n    getJudgmentAnswerType(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? 'success' : 'danger'\n    },\n    // 获取判断题答案文本\n    getJudgmentAnswerText(answer) {\n      const isTrue = answer === 'true' || answer === true || answer === 1 || answer === '1'\n      return isTrue ? '正确' : '错误'\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '--'\n      return new Date(time).toLocaleString()\n    },\n\n    // 获取选项键显示\n    getOptionKey(option, index) {\n      const key = option.key || option.optionKey\n\n      // 判断题特殊处理\n      if (this.isJudgmentQuestion) {\n        if (key === 'true' || key === true) {\n          return 'A'\n        } else if (key === 'false' || key === false) {\n          return 'B'\n        }\n      }\n\n      // 选择题直接返回原key，如果没有key则使用字母序列\n      if (key) {\n        return key\n      } else {\n        return String.fromCharCode(65 + index) // A, B, C, D...\n      }\n    },\n\n    // 判断是否为正确选项\n    isCorrectOption(originalKey) {\n      if (!originalKey) return false\n\n      // 首先尝试从选项的 isCorrect 字段判断\n      const option = this.validOptions.find(opt =>\n        (opt.key || opt.optionKey) === originalKey\n      )\n      if (option && option.isCorrect !== undefined) {\n        return option.isCorrect === true\n      }\n\n      // 如果没有 isCorrect 字段，则使用 correctAnswer 字段\n      if (!this.question.correctAnswer) return false\n\n      // 处理单选题（字符串格式：'single' 或数字格式：1）\n      if (this.question.questionType === 'single' || this.question.questionType === 1) {\n        return this.question.correctAnswer.toString() === originalKey.toString()\n      }\n      // 处理多选题（字符串格式：'multiple' 或数字格式：2）\n      else if (this.question.questionType === 'multiple' || this.question.questionType === 2) {\n        const correctAnswers = this.question.correctAnswer.toString().split(',').map(ans => ans.trim())\n        return correctAnswers.includes(originalKey.toString())\n      }\n      return false\n    },\n    // 切换展开状态\n    toggleExpand() {\n      this.$emit('toggle-expand', this.question.questionId)\n    },\n    // 编辑题目\n    handleEdit() {\n      this.$emit('edit', this.question)\n    },\n    // 复制题目\n    handleCopy() {\n      this.$emit('copy', this.question)\n    },\n    // 删除题目\n    handleDelete() {\n      this.$emit('delete', this.question)\n    },\n\n    // 处理选择状态变化\n    handleSelectionChange(selected) {\n      this.$emit('selection-change', this.question.questionId, selected)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.question-card {\n  background: #fff;\n  border-radius: 6px;\n  box-shadow: 0 2px 6px rgba(0,0,0,0.08);\n  margin-bottom: 12px;\n  overflow: hidden;\n}\n\n.question-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.question-number {\n  font-weight: bold;\n  font-size: 15px;\n  color: #333;\n}\n\n.question-type-bracket {\n  color: #409eff;\n  font-weight: 500;\n  font-size: 13px;\n}\n\n.difficulty-label {\n  font-size: 13px;\n  color: #666;\n}\n\n.create-time {\n  font-size: 11px;\n  color: #999;\n}\n\n.header-right {\n  display: flex;\n  gap: 5px;\n}\n\n.question-preview {\n  padding: 16px 20px;\n}\n\n.question-content {\n  padding: 12px 16px 12px 50px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  background: #fafafa;\n}\n\n.question-detail {\n  border-top: 1px solid #e9ecef;\n  padding: 20px;\n}\n\n.detail-section {\n  margin-bottom: 20px;\n}\n\n.detail-section:last-child {\n  margin-bottom: 0;\n}\n\n.detail-section h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #333;\n  font-weight: 600;\n}\n\n.content-text, .explanation-text {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.options-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.option-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  background: #fff;\n  border-radius: 6px;\n  gap: 12px;\n  border: 1px solid #e4e7ed;\n  transition: all 0.3s ease;\n}\n\n.option-item:hover {\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.option-item.correct-option {\n  background: #f0f9ff;\n  border: 1px solid #67c23a;\n  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);\n}\n\n.option-key {\n  font-weight: bold;\n  color: #409eff;\n  min-width: 24px;\n  font-size: 15px;\n}\n\n.option-content {\n  flex: 1;\n  font-size: 15px;\n  color: #333;\n  line-height: 1.6;\n}\n\n.correct-mark {\n  color: #67c23a;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.answer-section {\n  padding: 20px;\n  background: #f9f9f9;\n}\n\n.judgment-answer {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #fff;\n  border-radius: 4px;\n}\n\n.answer-label {\n  font-weight: bold;\n  color: #333;\n}\n\n.answer-value {\n  font-weight: 500;\n}\n\n.answer-value.success {\n  color: #67c23a;\n}\n\n.answer-value.danger {\n  color: #f56c6c;\n}\n\n.explanation-section {\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n  background: #f5f5f5;\n}\n\n.explanation-text {\n  font-size: 14px;\n  line-height: 1.8;\n  color: #666;\n  background: #fff;\n  padding: 16px;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.explanation-text::before {\n  content: \"解析：\";\n  font-weight: bold;\n  color: #409eff;\n  display: block;\n  margin-bottom: 8px;\n}\n</style>\n"]}]}